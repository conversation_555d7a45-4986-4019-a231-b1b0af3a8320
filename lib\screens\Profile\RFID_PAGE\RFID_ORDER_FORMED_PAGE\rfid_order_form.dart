import 'package:flutter/material.dart';

class RfidOrderForm extends StatefulWidget {
  const RfidOrderForm({super.key});

  @override
  State<RfidOrderForm> createState() => _RfidOrderFormState();
}

class _RfidOrderFormState extends State<RfidOrderForm> {
  final _formKey = GlobalKey<FormState>();

  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _addressController = TextEditingController();
  final TextEditingController _stateController = TextEditingController();
  final TextEditingController _pincodeController = TextEditingController();
  final TextEditingController _amountController = TextEditingController();

  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0E0E0E), // Dark background

      appBar: AppBar(
        backgroundColor: const Color(0xFF0E0E0E),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back,
              color: Color(0xFF67C44C)), // Green arrow
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'RFID Order Form',
          style: TextStyle(color: Colors.white),
        ),
      ),

      // Use a Column so we can pin the "Submit Order" button at the bottom.
      body: SafeArea(
        child: Column(
          children: [
            // 1) Scrollable Form content
            Expanded(
              child: SingleChildScrollView(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Contact Info Section
                      const Text(
                        'Contact Information',
                        style: TextStyle(
                          color: Color(0xFF67C44C),
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      // Full Name
                      _buildTextField(
                        label: 'Full Name *',
                        controller: _nameController,
                        keyboardType: TextInputType.text,
                        validator: (value) => (value == null || value.isEmpty)
                            ? 'Please enter your full name'
                            : null,
                      ),
                      const SizedBox(height: 8),
                      // Email
                      _buildTextField(
                        label: 'Email Address *',
                        controller: _emailController,
                        keyboardType: TextInputType.emailAddress,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter your email address';
                          }
                          // Basic email check
                          if (!RegExp(r'^[^@]+@[^@]+\.[^@]+').hasMatch(value)) {
                            return 'Please enter a valid email address';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 8),
                      // Phone
                      _buildTextField(
                        label: 'Phone Number *',
                        controller: _phoneController,
                        keyboardType: TextInputType.phone,
                        validator: (value) => (value == null || value.isEmpty)
                            ? 'Please enter your phone number'
                            : null,
                      ),
                      const SizedBox(height: 16),

                      // Address Info Section
                      const Text(
                        'Address Information',
                        style: TextStyle(
                          color: Color(0xFF67C44C),
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      // Street Address
                      _buildTextField(
                        label: 'Street Address *',
                        controller: _addressController,
                        keyboardType: TextInputType.streetAddress,
                        validator: (value) => (value == null || value.isEmpty)
                            ? 'Please enter your street address'
                            : null,
                      ),
                      const SizedBox(height: 8),

                      // State & Pincode in one row
                      Row(
                        children: [
                          // State
                          Expanded(
                            child: _buildTextField(
                              label: 'State *',
                              controller: _stateController,
                              keyboardType: TextInputType.text,
                              validator: (value) =>
                                  (value == null || value.isEmpty)
                                      ? 'Please enter your state'
                                      : null,
                            ),
                          ),
                          const SizedBox(width: 12),
                          // Pincode
                          Expanded(
                            child: _buildTextField(
                              label: 'Pincode *',
                              controller: _pincodeController,
                              keyboardType: TextInputType.number,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Please enter your pincode';
                                }
                                // Check if 6 digits (example)
                                if (value.length != 6) {
                                  return 'Please enter a valid 6-digit pincode';
                                }
                                return null;
                              },
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),

                      // Payment Details Section
                      const Text(
                        'Payment Details',
                        style: TextStyle(
                          color: Color(0xFF67C44C),
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      // Amount
                      _buildTextField(
                        label: 'Amount *',
                        controller: _amountController,
                        keyboardType: TextInputType.number,
                        validator: (value) => (value == null || value.isEmpty)
                            ? 'Please enter an amount'
                            : null,
                      ),
                      const SizedBox(height: 16),
                    ],
                  ),
                ),
              ),
            ),

            // 2) Pinned Submit Button at the bottom
            Container(
              color: const Color(0xFF0E0E0E),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: SizedBox(
                width: double.infinity,
                height: 44, // Slightly smaller height
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF67C44C), // Green accent
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  onPressed: _isLoading ? null : _handleSubmit,
                  child: _isLoading
                      ? const CircularProgressIndicator(
                          color: Colors.black,
                        )
                      : const Text(
                          'Submit Order',
                          style: TextStyle(
                            color: Colors.black,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTextField({
    required String label,
    required TextEditingController controller,
    required TextInputType keyboardType,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            color: Colors.white70,
            fontSize: 13, // Slightly smaller label text
          ),
        ),
        const SizedBox(height: 4),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          style: const TextStyle(color: Colors.white, fontSize: 14),
          validator: validator,
          decoration: InputDecoration(
            contentPadding:
                const EdgeInsets.symmetric(vertical: 10, horizontal: 12),
            filled: true,
            fillColor: const Color(0xFF1E1E1E),
            hintText: _hintForLabel(label),
            hintStyle: const TextStyle(color: Colors.white38, fontSize: 13),
            enabledBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: Colors.transparent),
              borderRadius: BorderRadius.circular(8),
            ),
            focusedBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: Colors.grey),
              borderRadius: BorderRadius.circular(8),
            ),
            errorBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: Colors.red),
              borderRadius: BorderRadius.circular(8),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: Colors.red),
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
      ],
    );
  }

  // Provide placeholder text based on label if desired.
  String _hintForLabel(String label) {
    if (label.toLowerCase().contains('full name')) {
      return 'Enter your full name';
    } else if (label.toLowerCase().contains('email')) {
      return 'Enter your email address';
    } else if (label.toLowerCase().contains('phone')) {
      return 'Enter your phone number';
    } else if (label.toLowerCase().contains('street address')) {
      return 'Enter your street address';
    } else if (label.toLowerCase().contains('state')) {
      return 'Enter your state';
    } else if (label.toLowerCase().contains('pincode')) {
      return 'Enter 6-digit pincode';
    } else if (label.toLowerCase().contains('amount')) {
      return 'Enter amount';
    }
    return '';
  }

  Future<void> _handleSubmit() async {
    // Validate form fields
    if (_formKey.currentState?.validate() ?? false) {
      setState(() => _isLoading = true);

      // Simulate an API call or other async operation
      await Future.delayed(const Duration(seconds: 2));

      setState(() => _isLoading = false);

      // Show success message
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Order placed successfully!')),
      );

      // Optionally clear form or pop screen
      _formKey.currentState?.reset();
      _nameController.clear();
      _emailController.clear();
      _phoneController.clear();
      _addressController.clear();
      _stateController.clear();
      _pincodeController.clear();
      _amountController.clear();
    }
  }
}
