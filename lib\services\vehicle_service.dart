import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/api_vehicle_model.dart';
import '../utils/api_constants.dart';

class VehicleService {
  final String _baseUrl = ApiConstants.baseUrl;
  final Map<String, String> _headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  // Get auth token from SharedPreferences
  Future<String?> _getAuthToken() async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString('auth_token');

    if (token == null || token.isEmpty) {
      debugPrint('No auth token found in SharedPreferences');
      return null;
    }

    debugPrint('Auth token retrieved successfully');
    return token;
  }

  // Set auth headers with token
  Future<Map<String, String>> _getAuthHeaders() async {
    final headers = Map<String, String>.from(_headers);
    final token = await _getAuthToken();

    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
      debugPrint('Added Authorization header with token');
    } else {
      debugPrint('WARNING: No auth token available for API request');
    }

    return headers;
  }

  // Get all vehicles
  Future<VehicleResponse> getVehicles() async {
    try {
      // Get headers with auth token
      final headers = await _getAuthHeaders();

      debugPrint('Making GET request to: $_baseUrl/api/v1/user/vehicles');
      final response = await http.get(
        Uri.parse('$_baseUrl/api/v1/user/vehicles'),
        headers: headers,
      );

      debugPrint('Vehicle API Response Status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final jsonData = jsonDecode(response.body);
        return VehicleResponse.fromJson(jsonData);
      } else if (response.statusCode == 401) {
        debugPrint('Authentication error: ${response.body}');
        throw Exception('Authentication failed. Please log in again.');
      } else {
        debugPrint('Error fetching vehicles: ${response.body}');
        throw Exception('Failed to load vehicles: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Exception in getVehicles: $e');
      throw Exception('Failed to load vehicles: $e');
    }
  }

  // Register a vehicle
  Future<bool> registerVehicle(VehicleRegistration registration) async {
    try {
      // Get headers with auth token
      final headers = await _getAuthHeaders();

      debugPrint('Making POST request to: $_baseUrl/api/v1/user/vehicles/save');
      debugPrint('Request body: ${registration.toJsonString()}');

      final response = await http.post(
        Uri.parse('$_baseUrl/api/v1/user/vehicles/save'),
        headers: headers,
        body: registration.toJsonString(),
      );

      debugPrint(
          'Vehicle Registration Response Status: ${response.statusCode}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        final jsonData = jsonDecode(response.body);
        return jsonData['success'] ?? false;
      } else if (response.statusCode == 401) {
        debugPrint('Authentication error: ${response.body}');
        throw Exception('Authentication failed. Please log in again.');
      } else {
        debugPrint('Error registering vehicle: ${response.body}');
        throw Exception('Failed to register vehicle: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Exception in registerVehicle: $e');
      throw Exception('Failed to register vehicle: $e');
    }
  }

  // Set default vehicle
  Future<bool> setDefaultVehicle(int vehicleId) async {
    try {
      // Get headers with auth token
      final headers = await _getAuthHeaders();

      debugPrint(
          'Making POST request to: $_baseUrl/api/v1/user/vehicles/default');
      final requestBody = jsonEncode({'id': vehicleId});
      debugPrint('Request body: $requestBody');

      final response = await http.post(
        Uri.parse('$_baseUrl/api/v1/user/vehicles/default'),
        headers: headers,
        body: requestBody,
      );

      debugPrint('Set Default Vehicle Response Status: ${response.statusCode}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        final jsonData = jsonDecode(response.body);
        return jsonData['success'] ?? false;
      } else if (response.statusCode == 401) {
        debugPrint('Authentication error: ${response.body}');
        throw Exception('Authentication failed. Please log in again.');
      } else {
        debugPrint('Error setting default vehicle: ${response.body}');
        throw Exception(
            'Failed to set default vehicle: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Exception in setDefaultVehicle: $e');
      throw Exception('Failed to set default vehicle: $e');
    }
  }
}
