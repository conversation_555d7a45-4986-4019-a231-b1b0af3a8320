import 'package:flutter/material.dart';
import '../../../utils/app_theme.dart';
import '../../../utils/animations.dart';
import '../../../widgets/animated_button.dart';
import 'models/vehicle_model.dart';

class AddEditVehicleScreen extends StatefulWidget {
  final Vehicle? vehicle;

  const AddEditVehicleScreen({super.key, this.vehicle});

  @override
  AddEditVehicleScreenState createState() => AddEditVehicleScreenState();
}

class AddEditVehicleScreenState extends State<AddEditVehicleScreen> {
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _licenseController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.vehicle != null) {
      _nameController.text = widget.vehicle!.name;
      _licenseController.text = widget.vehicle!.license;
    }
  }

  void _saveVehicle() {
    if (_formKey.currentState!.validate()) {
      setState(() => _isLoading = true);

      // Simulate network delay
      Future.delayed(const Duration(milliseconds: 800), () {
        if (mounted) {
          setState(() => _isLoading = false);
          Navigator.pop(context, {
            'action': widget.vehicle == null ? 'add' : 'edit',
            'name': _nameController.text,
            'license': _licenseController.text,
            'imageUrl':
                'https://via.placeholder.com/150', // Default placeholder image
          });
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final isEditMode = widget.vehicle != null;

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.transparent,
        leading: IconButton(
          icon: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(26), // 0.1 * 255 = ~26
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child:
                const Icon(Icons.arrow_back, color: AppTheme.textPrimaryColor),
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          isEditMode ? 'Edit Vehicle' : 'Add New Vehicle',
          style: const TextStyle(
            color: AppTheme.textPrimaryColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      body: GestureDetector(
        onTap: () => FocusScope.of(context).unfocus(),
        child: SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Vehicle icon
                  Center(
                    child: FadeInAnimation(
                      child: Column(
                        children: [
                          Icon(
                            Icons.directions_car,
                            size: 80,
                            color: AppTheme.primaryColor,
                          ),
                          const SizedBox(height: 12),
                          Text(
                            'Vehicle Details',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: AppTheme.textSecondaryColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 32),

                  // Form Fields
                  StaggeredListAnimation(
                    children: [
                      // Vehicle Name Field
                      Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withAlpha(10),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: TextFormField(
                          controller: _nameController,
                          style: const TextStyle(
                            color: AppTheme.textPrimaryColor,
                            fontWeight: FontWeight.w500,
                          ),
                          decoration: AppTheme.inputDecoration(
                            labelText: 'Vehicle Name',
                            hintText: 'e.g. Tesla Model 3',
                            prefixIcon: const Icon(Icons.directions_car,
                                color: AppTheme.primaryColor),
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter a vehicle name';
                            }
                            return null;
                          },
                        ),
                      ),
                      const SizedBox(height: 20),

                      // License Plate Field
                      Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withAlpha(10),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: TextFormField(
                          controller: _licenseController,
                          style: const TextStyle(
                            color: AppTheme.textPrimaryColor,
                            fontWeight: FontWeight.w500,
                          ),
                          decoration: AppTheme.inputDecoration(
                            labelText: 'License Plate',
                            hintText: 'e.g. ABC 123',
                            prefixIcon: const Icon(Icons.credit_card,
                                color: AppTheme.primaryColor),
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter a license plate';
                            }
                            return null;
                          },
                        ),
                      ),
                      const SizedBox(height: 40),

                      // Save Button
                      AnimatedButton(
                        text: isEditMode ? 'Save Changes' : 'Add Vehicle',
                        icon: isEditMode ? Icons.check : Icons.add,
                        onPressed: _saveVehicle,
                        isLoading: _isLoading,
                        gradient: AppTheme.primaryGradient,
                        width: double.infinity,
                        height: 56,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
