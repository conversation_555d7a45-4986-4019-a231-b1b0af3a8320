import 'dart:async';
import 'package:flutter/material.dart';

/// Generic payment service to integrate with various payment providers.
/// This service handles the interaction with the chosen payment gateway.
class PaymentService {
  // Singleton pattern
  static final PaymentService _instance = PaymentService._internal();
  factory PaymentService() => _instance;
  PaymentService._internal();

  // Flag to track if a payment initiation is currently in progress.
  // This is a simple lock; for more complex scenarios, consider per-transaction locks.
  bool _isPaymentInitiationInProgress = false;

  /// Initiate payment process with a payment provider.
  ///
  /// This method should integrate with your chosen payment gateway SDK or API.
  /// It will use the server-generated `orderData` to configure and launch
  /// the payment flow.
  ///
  /// Returns a Stream that emits payment status updates. Each call to this
  /// method returns a new, independent stream for that specific payment attempt.
  ///
  /// Parameters:
  ///   - [amount]: The amount to be paid.
  ///   - [customerName]: The name of the customer.
  ///   - [customerEmail]: The email of the customer.
  ///   - [customerPhone]: The phone number of the customer.
  ///   - [orderData]: Server-generated data required for payment processing.
  ///                  This MUST include all necessary details like transaction ID,
  ///                  payment provider specific keys, endpoint URLs, callback URLs, etc.
  ///   - [promoCode]: Optional promotional code.
  Future<Stream<Map<String, dynamic>>> initiatePayment({
    required double amount,
    required String customerName,
    required String customerEmail,
    required String customerPhone,
    required Map<String, dynamic> orderData, // Now a required parameter
    String? promoCode,
  }) async {
    if (_isPaymentInitiationInProgress) {
      debugPrint(
          'Payment initiation already in progress, ignoring new request.');
      // It's often better to return a stream that emits an error in this case.
      final errorController =
          StreamController<Map<String, dynamic>>.broadcast();
      errorController
          .addError(Exception('Payment initiation is already in progress.'));
      errorController.close();
      return errorController.stream;
    }

    _isPaymentInitiationInProgress = true;
    // Each payment attempt gets its own stream controller.
    final controller = StreamController<Map<String, dynamic>>.broadcast();

    try {
      debugPrint(
        'Initiating payment with provider. Amount: $amount, Customer: $customerEmail, OrderData: $orderData',
      );

      // --- PRODUCTION INTEGRATION ---
      // TODO: Implement actual payment provider integration here.
      // 1. Validate `orderData`:
      //    Ensure all required fields are present (e.g., transaction_id, api_key, payment_url).
      //    final String transactionId = orderData['transaction_id'];
      //    if (transactionId == null) {
      //      throw ArgumentError('transaction_id is missing in orderData');
      //    }
      //    final String paymentUrl = orderData['payment_url']; // Example: URL for WebView
      //    final String apiKey = orderData['api_key'];

      // 2. Prepare request for the payment provider based on `orderData`.
      //    This might involve constructing a URL with query parameters, preparing a POST body,
      //    or initializing a native SDK with specific configurations.

      // 3. Launch Payment Flow:
      //    - For WebView-based gateways: Navigate to `paymentUrl`.
      //      You'll need a way to listen for redirects or messages from the WebView
      //      to determine payment status (e.g., using `javascriptChannels` in `WebViewWidget`).
      //    - For SDK-based gateways: Call the appropriate SDK method to start the payment.
      //      The SDK will typically provide callbacks for success, failure, or cancellation.
      //      Example (pseudo-code for an SDK):
      //      PaymentProviderSdk.instance.startPayment(
      //        params: orderData, // Pass relevant parts of orderData
      //        onSuccess: (response) {
      //          controller.add({
      //            'status': 'success',
      //            'message': 'Payment successful',
      //            'transactionId': transactionId,
      //            'provider_response': response,
      //            'timestamp': DateTime.now().millisecondsSinceEpoch,
      //          });
      //          controller.close();
      //          _isPaymentInitiationInProgress = false;
      //        },
      //        onFailure: (error) {
      //          controller.add({
      //            'status': 'failure',
      //            'message': error.message ?? 'Payment failed',
      //            'transactionId': transactionId,
      //            'provider_response': error.details,
      //            'timestamp': DateTime.now().millisecondsSinceEpoch,
      //          });
      //          controller.close();
      //          _isPaymentInitiationInProgress = false;
      //        },
      //        onCancelled: () {
      //          controller.add({
      //            'status': 'cancelled',
      //            'message': 'Payment cancelled by user',
      //            'transactionId': transactionId,
      //            'timestamp': DateTime.now().millisecondsSinceEpoch,
      //          });
      //          controller.close();
      //          _isPaymentInitiationInProgress = false;
      //        },
      //      );

      // --- Payment Provider Logic ---
      final String paymentSource =
          orderData['source']?.toString().toLowerCase() ?? '';
      debugPrint('Payment source from orderData: $paymentSource');

      // Based on the backend snippet, route to the correct handler.
      // These handlers will emit events to the controller.
      if (paymentSource == 'phonepe') {
        await _initPhonePeSDK(orderData, controller, amount);
      } else if (paymentSource == 'cashfree') {
        // Cashfree uses orderData.order according to the backend snippet
        final cashfreeOrderData = orderData['order'] as Map<String, dynamic>?;
        if (cashfreeOrderData != null) {
          await _startCashfreeCheckout(cashfreeOrderData, controller, amount);
        } else {
          final String errorMessage =
              'Cashfree order data missing in orderData.order';
          debugPrint(errorMessage);
          controller.addError(Exception(errorMessage));
        }
      } else {
        // Default or Razorpay (as per backend snippet's else condition)
        await _handlePaymentRazorPay(orderData, controller, amount);
      }
      // Ensure the controller is closed and flag reset by the specific payment handlers.
      // If not, close it here as a fallback, though ideally handlers manage their lifecycle.
      // if (!controller.isClosed) {
      //   await controller.close();
      // }
      // _isPaymentInitiationInProgress = false; // Reset by specific handlers
      // --- End Payment Provider Logic ---

      return controller.stream;
    } catch (e, stackTrace) {
      debugPrint('Error during payment initiation: $e\n$stackTrace');
      controller.addError('Failed to initiate payment: $e', stackTrace);
      await controller.close();
      _isPaymentInitiationInProgress = false;
      rethrow; // Caller might also want to handle the immediate error.
    }
    // No `finally` needed if `_isPaymentInitiationInProgress` and `controller.close()`
    // are handled in all execution paths (success, error, SDK callbacks).
  }

  /// Dispose resources if any were globally allocated by the service.
  /// For this implementation, individual streams are managed per call, so
  /// a general dispose might not be strictly necessary unless there are
  /// other shared resources (e.g., SDK initializations that need cleanup).
  // --- Specific Payment Provider Handlers (Stubs) ---
  // These methods should contain the actual SDK integrations and manage the controller.

  Future<void> _initPhonePeSDK(
    Map<String, dynamic> orderData,
    StreamController<Map<String, dynamic>> controller,
    double amount,
  ) async {
    debugPrint('PhonePe SDK: Initializing with orderData: $orderData');
    // TODO: Implement PhonePe SDK integration
    // Simulate async operation and then add status to controller
    await Future.delayed(const Duration(seconds: 2));
    controller.add({
      'status': 'success', // or 'failure', 'cancelled'
      'message': 'PhonePe payment processed (simulated)',
      'transactionId': orderData['transaction_id'] ?? 'phonepe_txn_sim',
      'provider_response': {'detail': 'PhonePe simulated success'},
      'amount': amount,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    });
    await controller.close();
    _isPaymentInitiationInProgress = false;
  }

  Future<void> _startCashfreeCheckout(
    Map<String, dynamic> cashfreeOrderData, // Specific for Cashfree
    StreamController<Map<String, dynamic>> controller,
    double amount,
  ) async {
    debugPrint('Cashfree: Starting checkout with order: $cashfreeOrderData');
    // TODO: Implement Cashfree SDK integration
    await Future.delayed(const Duration(seconds: 2));
    controller.add({
      'status': 'success',
      'message': 'Cashfree payment processed (simulated)',
      'transactionId': cashfreeOrderData['order_id'] ?? 'cashfree_txn_sim',
      'provider_response': {'detail': 'Cashfree simulated success'},
      'amount': amount,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    });
    await controller.close();
    _isPaymentInitiationInProgress = false;
  }

  Future<void> _handlePaymentRazorPay(
    Map<String, dynamic> orderData,
    StreamController<Map<String, dynamic>> controller,
    double amount,
  ) async {
    debugPrint('Razorpay: Handling payment with orderData: $orderData');
    // TODO: Implement Razorpay SDK integration
    await Future.delayed(const Duration(seconds: 2));
    controller.add({
      'status': 'success',
      'message': 'Razorpay payment processed (simulated)',
      'transactionId': orderData['order_id'] ?? // Razorpay might use 'order_id'
          orderData['transaction_id'] ??
          'razorpay_txn_sim',
      'provider_response': {'detail': 'Razorpay simulated success'},
      'amount': amount,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    });
    await controller.close();
    _isPaymentInitiationInProgress = false;
  }

  void dispose() {
    // If any global resources were used by PaymentService (e.g. a singleton SDK instance),
    // clean them up here.
    // For now, individual stream controllers are closed by their respective call flows.
    debugPrint('PaymentService disposed.');
  }
}
