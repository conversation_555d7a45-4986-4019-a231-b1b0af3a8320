import 'package:flutter/material.dart';
import '../../models/api_vehicle_model.dart';
import '../../services/vehicle_service.dart';
import '../../utils/animations.dart';
import '../../utils/app_theme.dart';
import 'vehicle_search_widget.dart';
import 'widgets/brand_filter.dart';
import 'widgets/registration_form.dart';
import 'widgets/vehicle_card.dart';

class VehicleSelectionScreen extends StatefulWidget {
  const VehicleSelectionScreen({super.key});

  @override
  VehicleSelectionScreenState createState() => VehicleSelectionScreenState();
}

class VehicleSelectionScreenState extends State<VehicleSelectionScreen>
    with TickerProviderStateMixin {
  final VehicleService _vehicleService = VehicleService();

  // State variables
  bool _isLoading = true;
  bool _isRegistering = false;
  bool _hasError = false;
  bool _isSearching = false;
  String _errorMessage = '';
  String _searchQuery = '';

  // Vehicle data
  late VehicleResponse _vehicleResponse;
  List<ApiVehicle> _displayedVehicles = [];
  List<ApiVehicle> _filteredVehicles = []; // Vehicles filtered by search
  String _selectedBrand = 'All';
  ApiVehicle? _selectedVehicle;

  // Animation controllers
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late AnimationController _scaleController;

  // Scroll controller
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();

    // Initialize animation controllers
    _fadeController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _slideController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 400),
    );

    _scaleController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );

    _scrollController = ScrollController();

    // Load vehicles
    _loadVehicles();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    _scaleController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  // Load vehicles from API
  Future<void> _loadVehicles() async {
    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = '';
    });

    try {
      // Get vehicles from API (token is now handled automatically in the service)
      _vehicleResponse = await _vehicleService.getVehicles();

      // Set displayed vehicles to all vehicles initially
      _displayedVehicles = _vehicleResponse.allVehicles;

      setState(() {
        _isLoading = false;
      });

      // Start animations
      _fadeController.forward();
      _slideController.forward();
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = e.toString();
      });
    }
  }

  // Filter vehicles by brand
  void _filterByBrand(String brand) {
    setState(() {
      _selectedBrand = brand;
      // Get vehicles for the selected brand
      final brandVehicles = brand == 'All'
          ? _vehicleResponse.allVehicles
          : _vehicleResponse.getVehiclesByBrand(brand);

      // Apply search filter if there's a search query
      if (_searchQuery.isNotEmpty) {
        _filteredVehicles =
            _filterVehiclesBySearch(brandVehicles, _searchQuery);
        _displayedVehicles = _filteredVehicles;
      } else {
        _displayedVehicles = brandVehicles;
      }
    });
  }

  // Handle search query changes
  void _handleSearch(String query) {
    setState(() {
      _searchQuery = query;
      _isSearching = true;
    });

    // Get the base list of vehicles based on selected brand
    final baseVehicles = _selectedBrand == 'All'
        ? _vehicleResponse.allVehicles
        : _vehicleResponse.getVehiclesByBrand(_selectedBrand);

    // Apply search filter
    if (query.isNotEmpty) {
      _filteredVehicles = _filterVehiclesBySearch(baseVehicles, query);
      setState(() {
        _displayedVehicles = _filteredVehicles;
        _isSearching = false;
      });
    } else {
      // If search is empty, just show all vehicles for the selected brand
      setState(() {
        _displayedVehicles = baseVehicles;
        _isSearching = false;
      });
    }
  }

  // Filter vehicles by search query
  List<ApiVehicle> _filterVehiclesBySearch(
      List<ApiVehicle> vehicles, String query) {
    final lowercaseQuery = query.toLowerCase();
    return vehicles.where((vehicle) {
      // Search in name
      final nameMatch = vehicle.name.toLowerCase().contains(lowercaseQuery);

      // Search in brand name if available
      final brandMatch = vehicle.brandName != null &&
          vehicle.brandName!.toLowerCase().contains(lowercaseQuery);

      // Search in variants if available
      final variantsMatch = vehicle.variants != null &&
          vehicle.variants!.toLowerCase().contains(lowercaseQuery);

      // Search in battery capacity
      final batteryMatch =
          vehicle.batteryCapacity.toLowerCase().contains(lowercaseQuery);

      // Return true if any field matches
      return nameMatch || brandMatch || variantsMatch || batteryMatch;
    }).toList();
  }

  // Select a vehicle
  void _selectVehicle(ApiVehicle vehicle) {
    setState(() {
      if (_selectedVehicle?.id == vehicle.id) {
        // Deselect if already selected
        _selectedVehicle = null;
      } else {
        // Select new vehicle
        _selectedVehicle = vehicle;

        // Scroll to bottom to show registration form
        Future.delayed(const Duration(milliseconds: 300), () {
          if (_scrollController.hasClients) {
            _scrollController.animateTo(
              _scrollController.position.maxScrollExtent,
              duration: const Duration(milliseconds: 500),
              curve: Curves.easeOutCubic,
            );
          }
        });
      }
    });
  }

  // Register vehicle
  Future<void> _registerVehicle(String registrationNumber) async {
    if (_selectedVehicle == null) return;

    setState(() {
      _isRegistering = true;
    });

    try {
      final registration = VehicleRegistration(
        registrationNumber: registrationNumber,
        vehicleId: _selectedVehicle!.id,
      );

      final success = await _vehicleService.registerVehicle(registration);

      if (success) {
        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Vehicle registered successfully!'),
              backgroundColor: AppTheme.primaryColor,
            ),
          );

          // Navigate back or to next screen
          Navigator.pop(context, {
            'success': true,
            'vehicle': _selectedVehicle,
            'registrationNumber': registrationNumber,
          });
        }
      } else {
        // Show error message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to register vehicle. Please try again.'),
              backgroundColor: Colors.red,
            ),
          );
          setState(() {
            _isRegistering = false;
          });
        }
      }
    } catch (e) {
      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
        setState(() {
          _isRegistering = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.transparent,
        leading: IconButton(
          icon: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(26), // 0.1 * 255 = ~26
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child:
                const Icon(Icons.arrow_back, color: AppTheme.textPrimaryColor),
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'Select Your Vehicle',
          style: TextStyle(
            color: AppTheme.textPrimaryColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return _buildLoadingState();
    }

    if (_hasError) {
      return _buildErrorState();
    }

    return _buildContent();
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const ShimmerLoading(
            child: CircleAvatar(
              radius: 40,
              backgroundColor: Colors.white,
              child: Icon(
                Icons.directions_car,
                size: 40,
                color: Colors.grey,
              ),
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'Loading Vehicles...',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: AppTheme.textSecondaryColor
                  .withAlpha(204), // 0.8 * 255 = ~204
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 60,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            const Text(
              'Failed to Load Vehicles',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: AppTheme.textSecondaryColor
                    .withAlpha(204), // 0.8 * 255 = ~204
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _loadVehicles,
              icon: const Icon(Icons.refresh),
              label: const Text('Try Again'),
              style: AppTheme.primaryButtonStyle,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContent() {
    return ListView(
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      physics: const BouncingScrollPhysics(),
      children: [
        // Search widget
        VehicleSearchWidget(
          onSearch: _handleSearch,
          isSearching: _isSearching,
        ),

        // Brand filter
        if (_vehicleResponse.brands.isNotEmpty)
          BrandFilter(
            brands: _vehicleResponse.brands,
            selectedBrand: _selectedBrand,
            onBrandSelected: _filterByBrand,
          ),

        // Vehicles grid
        FadeTransition(
          opacity: _fadeController,
          child: SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(0, 0.1),
              end: Offset.zero,
            ).animate(_slideController),
            child: _buildVehiclesGrid(),
          ),
        ),

        // Registration form
        if (_selectedVehicle != null)
          Padding(
            padding: const EdgeInsets.only(top: 24),
            child: RegistrationForm(
              selectedVehicle: _selectedVehicle!,
              onRegister: _registerVehicle,
              isLoading: _isRegistering,
            ),
          ),

        // Bottom padding
        const SizedBox(height: 100),
      ],
    );
  }

  Widget _buildVehiclesGrid() {
    if (_displayedVehicles.isEmpty) {
      // Show different message based on whether search is active
      final message = _searchQuery.isNotEmpty
          ? 'No vehicles found matching "$_searchQuery"'
          : 'No vehicles found for $_selectedBrand';

      return SizedBox(
        height: 200,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.search_off,
                size: 48,
                color: AppTheme.textSecondaryColor
                    .withAlpha(153), // 0.6 * 255 = ~153
              ),
              const SizedBox(height: 16),
              Text(
                message,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 16,
                  color: AppTheme.textSecondaryColor
                      .withAlpha(204), // 0.8 * 255 = ~204
                ),
              ),
              if (_searchQuery.isNotEmpty)
                TextButton(
                  onPressed: () => _handleSearch(''),
                  child: const Text('Clear Search'),
                ),
            ],
          ),
        ),
      );
    }

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.75,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: _displayedVehicles.length,
      itemBuilder: (context, index) {
        final vehicle = _displayedVehicles[index];
        final isSelected = _selectedVehicle?.id == vehicle.id;

        return VehicleCard(
          vehicle: vehicle,
          onTap: _selectVehicle,
          isSelected: isSelected,
          animation: Tween<double>(begin: 0.0, end: 1.0).animate(
            CurvedAnimation(
              parent: _slideController,
              curve: Interval(
                0.1 * (index % 4), // Stagger based on position
                1.0,
                curve: Curves.easeOut,
              ),
            ),
          ),
        );
      },
    );
  }
}
