import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'HELP&SUPPORT/support_center_page.dart' as support;
import 'package:shimmer/shimmer.dart';

/// Simple data class for each FAQ category
class FAQCategory {
  final String title;
  final IconData icon;
  final List<String> questions;
  final List<String> answers;

  FAQCategory({
    required this.title,
    required this.icon,
    required this.questions,
    required this.answers,
  });
}

class FAQPage extends StatefulWidget {
  const FAQPage({super.key});

  @override
  State<FAQPage> createState() => _FAQPageState();
}

class _FAQPageState extends State<FAQPage> with TickerProviderStateMixin {
  late AnimationController _searchBarController;
  late Animation<double> _searchBarAnimation;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _searchBarController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _searchBarAnimation = CurvedAnimation(
      parent: _searchBarController,
      curve: Curves.easeInOut,
    );
    _searchBarController.forward();
    
    // Simulate loading
    Future.delayed(const Duration(milliseconds: 1500), () {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    });
  }
  // Controller for the search bar
  final TextEditingController _searchController = TextEditingController();

  // Current search query
  String _searchQuery = '';

  // Example FAQ data with answers
  final List<FAQCategory> faqData = [
    FAQCategory(
      title: 'Charging & Stations',
      icon: Icons.electric_car,
      questions: [
        'How do I start a charging session?',
        'What types of connectors are supported?',
        'How can I find the nearest charging station?',
        'What should I do if a station is not working?',
        'How long does a typical charging session take?',
      ],
      answers: [
        'To start a charging session, locate a station on the map, tap on it, and follow the on-screen instructions. You can pay using the app wallet or RFID card.',
        'We support Type 2, CCS2, CHAdeMO, and GB/T connectors. The app shows which connectors are available at each station.',
        'Open the app and use the map view to see all nearby stations. You can filter by connector type, availability, and distance.',
        'Please report non-working stations through the app by selecting the station and tapping "Report Issue". Our team will address it promptly.',
        'Charging time depends on your vehicle\'s battery capacity and the charger\'s power output. Fast chargers typically take 30-60 minutes for 80% charge.',
      ],
    ),
    FAQCategory(
      title: 'Wallet & Payments',
      icon: Icons.account_balance_wallet_rounded,
      questions: [
        'How do I add money to my wallet?',
        'Why is my transaction pending?',
        'How to check my wallet balance?',
        'What are the wallet transaction limits?',
        'How to link my bank account to the wallet?',
      ],
      answers: [
        'Go to the Wallet section, tap "Add Money", select your payment method, enter the amount, and confirm the transaction.',
        'Transactions may be pending due to network issues or bank verification. Most transactions complete within 30 minutes. If it takes longer, contact support.',
        'Your wallet balance is displayed on the Wallet screen. You can also see it on your profile page.',
        'The minimum transaction is ₹100 and the maximum is ₹10,000 per day. Monthly limit is ₹50,000.',
        'Go to Wallet > Payment Methods > Add Bank Account and follow the instructions to securely link your account.',
      ],
    ),
    FAQCategory(
      title: 'RFID Cards',
      icon: Icons.credit_card,
      questions: [
        'How do I order an RFID card?',
        'How to link my RFID card to my account?',
        'What if I lose my RFID card?',
        'Can I share my RFID card with family members?',
        'How long does RFID card delivery take?',
      ],
      answers: [
        'Go to Profile > RFID Cards > Order New Card. Fill in your delivery details and complete the payment.',
        'Go to Profile > RFID Cards > Link Card. Enter the card number found on the back of your card and follow the instructions.',
        'If you lose your card, immediately go to Profile > RFID Cards, select the lost card, and tap "Deactivate Card" to prevent unauthorized use.',
        'Yes, you can share your RFID card with family members, but all charges will be billed to your account.',
        'RFID cards are typically delivered within 3-5 business days to metro cities and 5-7 days to other locations.',
      ],
    ),
    FAQCategory(
      title: 'Account & Profile',
      icon: Icons.person,
      questions: [
        'How do I update my profile information?',
        'How to change my password?',
        'Can I have multiple vehicles in my profile?',
        'How to delete my account?',
        'How is my data protected?',
      ],
      answers: [
        'Go to Profile > Edit Profile to update your personal information, including name, email, and phone number.',
        'Go to Profile > Security > Change Password. You\'ll need to enter your current password and then set a new one.',
        'Yes, you can add multiple vehicles. Go to Profile > My Vehicles > Add Vehicle and enter the required details.',
        'Go to Profile > Settings > Delete Account. Note that this action is permanent and will erase all your data.',
        'We use industry-standard encryption to protect your personal and payment information. Read our Privacy Policy for more details.',
      ],
    ),
  ];

  // Keep track of which FAQ cards are expanded
  final Set<int> _expandedIndexes = {};
  final Map<String, bool> _expandedQuestions = {};

  // Define the corner radius and primary color used in header
  final double _cornerRadius = 12.0;
  final Color _primaryColor = const Color(0xFF3D7AF5);

  @override
  void dispose() {
    _searchBarController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Widget _buildLoadingShimmer() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: 5,
      itemBuilder: (context, index) {
        return Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: Container(
            margin: const EdgeInsets.only(bottom: 16),
            height: 80,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(_cornerRadius),
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    // Filter the FAQ data based on the search query
    final filteredFaq = faqData.where((category) {
      final titleMatches =
          category.title.toLowerCase().contains(_searchQuery.toLowerCase());
      final questionMatches = category.questions.any(
        (q) => q.toLowerCase().contains(_searchQuery.toLowerCase()),
      );
      return titleMatches || questionMatches;
    }).toList();

    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDarkMode ? Colors.black : const Color(0xFFF5F5F5),
      appBar: AppBar(
        backgroundColor: _primaryColor,
        title: const Text(
          'Help & Support',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
        centerTitle: true,
        elevation: 0,
        actions: [
          Container(
            margin: const EdgeInsets.only(right: 12),
            child: InkWell(
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (context) => const support.HelpSupportPage()),
                );
              },
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: const BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.support_agent,
                  color: Color(0xFF3D7AF5),
                  size: 24,
                ),
              ),
            ),
          ),
        ],
      ),
      body: _isLoading ? _buildLoadingShimmer() : SafeArea(
        child: Column(
          children: [
            // Search bar with simplified design (no double layer)
            Padding(
              padding: const EdgeInsets.fromLTRB(20, 20, 20, 20),
              child: ScaleTransition(
                scale: _searchBarAnimation,
                child: TextField(
                  controller: _searchController,
                  onChanged: (value) => setState(() => _searchQuery = value),
                  style: TextStyle(
                    color: isDarkMode ? Colors.white : Colors.black,
                    fontSize: 15,
                  ),
                decoration: InputDecoration(
                  filled: true,
                  fillColor: isDarkMode ? const Color(0xFF2A2A2A) : Colors.white,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(30),
                    borderSide: BorderSide.none,
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(30),
                    borderSide: BorderSide(color: Colors.grey.withAlpha(77)),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(30),
                    borderSide: BorderSide(color: _primaryColor),
                  ),
                  hintText: 'Search for queries...',
                  hintStyle: TextStyle(
                    color: isDarkMode
                        ? Colors.grey.shade500
                        : Colors.grey.shade600,
                    fontSize: 15,
                  ),
                  prefixIcon: Icon(
                    Icons.search,
                    color: const Color(0xFF3D7AF5),
                    size: 20,
                  ),
                  suffixIcon: _searchQuery.isNotEmpty
                      ? IconButton(
                          icon: Icon(Icons.clear, color: Colors.grey),
                          onPressed: () {
                            setState(() {
                              _searchController.clear();
                              _searchQuery = '';
                            });
                            _searchBarController.reverse().then((_) {
                              _searchBarController.forward();
                            });
                          },
                        )
                      : null,
                ),
              ),
            ),

            // FAQ list
            Expanded(
              child: filteredFaq.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.search_off,
                              size: 64,
                              color: isDarkMode
                                  ? Colors.grey.shade600
                                  : Colors.grey.shade400),
                          const SizedBox(height: 16),
                          Text(
                            'No results found',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: isDarkMode
                                  ? Colors.grey.shade300
                                  : Colors.grey.shade700,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Try a different search term',
                            style: TextStyle(
                              fontSize: 16,
                              color: isDarkMode
                                  ? Colors.grey.shade400
                                  : Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ),
                    ) : ListView.builder(
                        key: ValueKey<String>(_searchQuery),
                        padding: const EdgeInsets.symmetric(horizontal: 20),
                        itemCount: filteredFaq.length,
                        itemBuilder: (context, index) {
                          final category = filteredFaq[index];
                          final isExpanded = _expandedIndexes.contains(index);
                          return _buildFaqCard(
                            index: index,
                            category: category,
                            isExpanded: isExpanded,
                          );
                        },
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build each FAQ card with smooth expand/collapse animation.
  Widget _buildFaqCard({
    required int index,
    required FAQCategory category,
    required bool isExpanded,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 10, horizontal: 4),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(_cornerRadius),
      ),
      color: isDarkMode ? const Color(0xFF1A1A1A) : Colors.white,
      elevation: 4,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
            // FAQ Header
            GestureDetector(
              onTap: () {
                setState(() {
                  if (isExpanded) {
                    _expandedIndexes.remove(index);
                  } else {
                    _expandedIndexes.add(index);
                  }
                });
              },
              child: Container(
                height: 60,
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: _primaryColor,
                  borderRadius: isExpanded
                      ? BorderRadius.only(
                          topLeft: Radius.circular(_cornerRadius),
                          topRight: Radius.circular(_cornerRadius),
                        )
                      : BorderRadius.circular(_cornerRadius),
                  boxShadow: isExpanded ? [
                    BoxShadow(
                      color: _primaryColor.withAlpha(100),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    )
                  ] : [],
                ),
                child: Row(
                  children: [
                    Icon(
                      category.icon,
                      color: Colors.white,
                      size: 28,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        category.title,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    Icon(
                      isExpanded
                          ? Icons.keyboard_arrow_up
                          : Icons.keyboard_arrow_down,
                      color: Colors.white,
                    ),
                  ],
                ),
              ),
            ),
            // FAQ Body (expanded)
            if (isExpanded)
              Container(
                padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
                decoration: BoxDecoration(
                  color: isDarkMode ? const Color(0xFF1A1A1A) : Colors.white,
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(12),
                    bottomRight: Radius.circular(12),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: List.generate(category.questions.length, (qIndex) {
                    final question = category.questions[qIndex];
                    final answer = category.answers[qIndex];
                    final questionKey = '${category.title}-$qIndex';
                    final isQuestionExpanded =
                        _expandedQuestions[questionKey] ?? false;

                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        InkWell(
                          onTap: () {
                            setState(() {
                              _expandedQuestions[questionKey] =
                                  !isQuestionExpanded;
                            });
                            HapticFeedback.lightImpact();
                          },
                          child: Padding(
                            padding: const EdgeInsets.symmetric(vertical: 8),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Icon(
                                  isQuestionExpanded
                                      ? Icons.remove_circle
                                      : Icons.add_circle,
                                  color: _primaryColor,
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    question,
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                      height: 1.4,
                                      color: isDarkMode
                                          ? Colors.white
                                          : Colors.black87,
                                      letterSpacing: 0.2,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        AnimatedCrossFade(
                          duration: const Duration(milliseconds: 200),
                          firstChild: const SizedBox.shrink(),
                          secondChild: Padding(
                            padding: const EdgeInsets.only(
                              left: 28,
                              bottom: 12,
                              right: 8,
                            ),
                            child: Text(
                              answer,
                              style: TextStyle(
                                fontSize: 15,
                                height: 1.5,
                                color: isDarkMode
                                    ? Colors.grey.shade300
                                    : Colors.grey.shade700,
                                letterSpacing: 0.1,
                                wordSpacing: 1.0,
                              ),
                            ),
                          ),
                          crossFadeState: isQuestionExpanded
                              ? CrossFadeState.showSecond
                              : CrossFadeState.showFirst,
                        ),
                        if (qIndex < category.questions.length - 1)
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 16),
                            child: Divider(
                              color: isDarkMode
                                  ? Colors.grey.shade800
                                  : Colors.grey.shade200,
                            ),
                          ),
                      ],
                    );
                  }),
                ),
              ),
        ],
      ),
    );
  }
}
// Class moved to support_center_page.dart
