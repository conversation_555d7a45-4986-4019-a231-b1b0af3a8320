import 'dart:async';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../repositories/wallet_repository.dart';
import '../models/wallet_models.dart';
import '../../../core/models/api_response.dart';
import '../../../core/services/connectivity_service.dart';
import 'payment_service.dart';

/// Service for handling wallet-related operations
class WalletService {
  final WalletRepository _walletRepository;
  final ConnectivityService _connectivityService;
  final PaymentService _paymentService;

  // Cache for wallet data
  WalletInfo? _cachedWalletInfo;
  DateTime? _walletInfoLastFetched;

  // Stream controller for wallet updates
  final _walletUpdateController = StreamController<WalletInfo>.broadcast();

  // Stream for wallet updates
  Stream<WalletInfo> get walletUpdates => _walletUpdateController.stream;

  WalletService({
    required WalletRepository walletRepository,
    required ConnectivityService connectivityService,
    required PaymentService payuService,
  })  : _walletRepository = walletRepository,
        _connectivityService = connectivityService,
        _paymentService = payuService;

  /// Get wallet information
  /// Uses caching to reduce API calls
  Future<ApiResponse<WalletInfo>> getWalletInfo(
      {bool forceRefresh = false}) async {
    // Check if we have cached data and it's not too old (less than 5 minutes)
    final now = DateTime.now();
    final cacheValid = _cachedWalletInfo != null &&
        _walletInfoLastFetched != null &&
        now.difference(_walletInfoLastFetched!).inMinutes < 5;

    // Return cached data if valid and not forcing refresh
    if (cacheValid && !forceRefresh) {
      return ApiResponse<WalletInfo>(
        success: true,
        message: 'Wallet info retrieved from cache',
        data: _cachedWalletInfo!,
      );
    }

    // Check connectivity before making API call
    final isConnected = await _connectivityService.checkConnectionManually();
    if (!isConnected) {
      // Return cached data if available, even if old
      if (_cachedWalletInfo != null) {
        return ApiResponse<WalletInfo>(
          success: true,
          message: 'Using cached wallet info (offline)',
          data: _cachedWalletInfo!,
        );
      } else {
        return ApiResponse<WalletInfo>(
          success: false,
          message: 'No internet connection and no cached data available',
        );
      }
    }

    // Make API call
    final response = await _walletRepository.getWalletInfo();

    // Update cache if successful
    if (response.success && response.data != null) {
      _cachedWalletInfo = response.data;
      _walletInfoLastFetched = now;

      // Notify listeners
      _walletUpdateController.add(response.data!);
    }

    return response;
  }

  /// Add money to wallet using payment service
  /// Add money to wallet.
  ///
  /// This method first calls the backend to initialize the payment and get `orderData`,
  /// then uses `PaymentService` to process the payment with this data.
  Future<Stream<Map<String, dynamic>>> addMoney(
    double amount, {
    required BuildContext context, // Context might be used for UI interactions
    required String
        paymentGateway, // Added: 'cashfree', 'razorpay', 'payu', 'phonepe'
    String? promoCode,
    // `orderData` is no longer passed directly; it's fetched from the repository.
  }) async {
    try {
      debugPrint(
          'Attempting to add money: $amount, gateway: $paymentGateway, promo: $promoCode');

      // 1. Initialize payment with the backend to get orderData.
      //    The receiptId and currency are now handled by the repository or backend.
      final initializationResponse = await _walletRepository.initializePayment(
        amount,
        paymentGateway, // Pass the selected payment gateway
        promoCode: promoCode,
      );

      if (!initializationResponse.success ||
          initializationResponse.data == null) {
        debugPrint(
            'Failed to initialize payment with backend: ${initializationResponse.message}');
        // Return a stream that emits an error
        final errorController =
            StreamController<Map<String, dynamic>>.broadcast();
        errorController.addError(Exception(
            'Failed to initialize payment: ${initializationResponse.message}'));
        errorController.close();
        return errorController.stream;
      }

      final Map<String, dynamic> orderData = initializationResponse.data!;
      debugPrint(
          'Payment initialized successfully, received orderData: $orderData');

      // 2. Proceed with payment using the fetched orderData.
      // Get user information from SharedPreferences for PaymentService (if still needed beyond orderData)
      final prefs = await SharedPreferences.getInstance();
      final String customerName = prefs.getString('user_name') ?? "Demo User";
      final String customerEmail =
          prefs.getString('user_email') ?? "<EMAIL>";
      final String customerPhone =
          prefs.getString('user_phone') ?? "9999999999";

      debugPrint(
          'Using customer details for PaymentService - Name: $customerName, Email: $customerEmail, Phone: $customerPhone');

      final paymentStatusStream = await _paymentService.initiatePayment(
        amount: amount,
        customerName: customerName,
        customerEmail: customerEmail,
        customerPhone: customerPhone,
        orderData: orderData, // Pass the server-generated orderData
        promoCode:
            promoCode, // Promo code might also be part of orderData or handled by backend
      );

      // Set up a listener to handle payment status updates
      paymentStatusStream.listen((status) {
        debugPrint('Payment status update: $status');
        // Handle different payment statuses
        if (status['status'] == 'success') {
          _handleSuccessfulPayment(amount, status);
        } else if (status['status'] == 'failure' ||
            status['status'] == 'failed') {
          // Handle payment failure
          debugPrint('Payment failed: ${status['message']}');
        }
      });

      return paymentStatusStream;
    } catch (e) {
      debugPrint('Error in addMoney: $e');
      rethrow;
    }
  }

  /// Handle successful payment
  void _handleSuccessfulPayment(
      double amount, Map<String, dynamic> status) async {
    try {
      debugPrint('Payment successful, refreshing wallet info');

      // Force refresh wallet info
      await getWalletInfo(forceRefresh: true);

      // Log payment details
      debugPrint('Added ₹$amount to wallet');
      debugPrint('Payment details: $status');
    } catch (e) {
      debugPrint('Error handling successful payment: $e');
    }
  }

  /// Get charging sessions history
  Future<ApiResponse<List<ChargingSession>>> getChargingSessions() async {
    // Check connectivity before making API call
    final isConnected = await _connectivityService.checkConnectionManually();
    if (!isConnected) {
      return ApiResponse<List<ChargingSession>>(
        success: false,
        message: 'No internet connection',
      );
    }

    return await _walletRepository.getChargingSessions();
  }

  /// Get ongoing charging sessions
  Future<ApiResponse<List<ChargingSession>>> getOngoingSessions() async {
    // Check connectivity before making API call
    final isConnected = await _connectivityService.checkConnectionManually();
    if (!isConnected) {
      return ApiResponse<List<ChargingSession>>(
        success: false,
        message: 'No internet connection',
      );
    }

    return await _walletRepository.getOngoingSessions();
  }

  /// Get billing details for a transaction
  Future<ApiResponse<BillingDetails>> getBillingDetails(
      String transactionId) async {
    // Check connectivity before making API call
    final isConnected = await _connectivityService.checkConnectionManually();
    if (!isConnected) {
      return ApiResponse<BillingDetails>(
        success: false,
        message: 'No internet connection',
      );
    }

    return await _walletRepository.getBillingDetails(transactionId);
  }

  /// Verify promocode
  Future<ApiResponse<Map<String, dynamic>>> verifyPromocode(
      String promocode) async {
    // Check connectivity before making API call
    final isConnected = await _connectivityService.checkConnectionManually();
    if (!isConnected) {
      return ApiResponse<Map<String, dynamic>>(
        success: false,
        message: 'No internet connection',
      );
    }

    return await _walletRepository.verifyPromocode(promocode);
  }

  /// Clear wallet cache
  void clearCache() {
    _cachedWalletInfo = null;
    _walletInfoLastFetched = null;
    debugPrint('Wallet cache cleared');
  }

  /// Dispose resources
  void dispose() {
    _walletUpdateController.close();
  }
}
