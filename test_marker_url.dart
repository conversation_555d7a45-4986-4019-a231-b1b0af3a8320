import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';

void main() async {
  // Test the marker URL
  // IMPORTANT: Never hardcode tokens in production code
  // This is a test file only - in production, tokens should be obtained securely
  final headers = {
    'Authorization':
        'Bearer YOUR_TOKEN_HERE', // Replace with a token at runtime
    'Content-Type': 'text/plain'
  };

  try {
    debugPrint(
        'Sending request to: https://api2.eeil.online/api/v1/user/stations/markers');
    final response = await http
        .get(
          Uri.parse('https://api2.eeil.online/api/v1/user/stations/markers'),
          headers: headers,
        )
        .timeout(const Duration(seconds: 15));

    debugPrint('Response status code: ${response.statusCode}');

    if (response.statusCode == 200) {
      final Map<String, dynamic> data = json.decode(response.body);
      debugPrint('Successfully parsed JSON response');

      if (data.containsKey('data') && data['data'] is List) {
        final List<dynamic> stationList = data['data'];
        debugPrint('Fetched ${stationList.length} station markers from API');

        // Print the first station
        if (stationList.isNotEmpty) {
          debugPrint('First station: ${json.encode(stationList.first)}');
        }
      }
    } else {
      debugPrint('API Error: ${response.statusCode} - ${response.body}');
    }
  } catch (e) {
    debugPrint('Exception when fetching station markers: $e');
  }
}
