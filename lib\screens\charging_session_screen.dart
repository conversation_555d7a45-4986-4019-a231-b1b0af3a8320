import 'dart:async';
import 'dart:io';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import '../services/charging_session_service.dart';
import '../services/charging_parameters_service.dart';
import '../models/charging_session.dart';

// Custom painter for multi-layered circular progress indicator
class CircularProgressPainter extends CustomPainter {
  final double progress;
  final Color progressColor;
  final double strokeWidth;
  final bool addGlow;
  final double rotationAngle;

  CircularProgressPainter({
    required this.progress,
    required this.progressColor,
    this.strokeWidth = 20.0,
    this.addGlow = true,
    this.rotationAngle = 0.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = math.min(size.width, size.height) / 2 - strokeWidth;

    // Draw background track
    final trackPaint = Paint()
      ..color = Colors.white.withOpacity(0.2)
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.round;

    canvas.drawCircle(center, radius, trackPaint);

    // Draw progress arc
    final progressPaint = Paint()
      ..shader = SweepGradient(
        colors: [
          const Color(0xFF22C55E),
          const Color(0xFF4ADE80),
          const Color(0xFF22C55E),
        ],
        stops: const [0.0, 0.5, 1.0],
        startAngle: -math.pi / 2,
        endAngle: -math.pi / 2 + 2 * math.pi,
        transform: GradientRotation(rotationAngle),
      ).createShader(Rect.fromCircle(center: center, radius: radius))
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.round;

    if (addGlow) {
      progressPaint.maskFilter = const MaskFilter.blur(BlurStyle.normal, 4);
    }

    final sweepAngle = 2 * math.pi * progress.clamp(0.0, 1.0);

    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      -math.pi / 2,
      sweepAngle,
      false,
      progressPaint,
    );

    // Draw end cap circle
    if (progress > 0 && progress < 1) {
      final endAngle = -math.pi / 2 + sweepAngle;
      final endX = center.dx + radius * math.cos(endAngle);
      final endY = center.dy + radius * math.sin(endAngle);

      // Glow effect
      final glowPaint = Paint()
        ..color = Colors.white.withOpacity(0.5)
        ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 8);

      canvas.drawCircle(Offset(endX, endY), strokeWidth * 0.7, glowPaint);

      // White circle
      final circlePaint = Paint()
        ..color = Colors.white
        ..style = PaintingStyle.fill;

      canvas.drawCircle(Offset(endX, endY), strokeWidth * 0.4, circlePaint);
    }
  }

  @override
  bool shouldRepaint(covariant CircularProgressPainter oldDelegate) {
    return progress != oldDelegate.progress ||
        rotationAngle != oldDelegate.rotationAngle;
  }
}

// Custom painter for matrix bar visualization
class MatrixBarPainter extends CustomPainter {
  final List<double> values;
  final Color color;
  final double animationValue;

  MatrixBarPainter({
    required this.values,
    required this.color,
    required this.animationValue,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final barWidth = size.width / (values.length * 2 - 1);
    final maxHeight = size.height * 0.8;

    for (int i = 0; i < values.length; i++) {
      final x = i * barWidth * 2;
      final height = maxHeight * values[i] * animationValue;
      final y = size.height - height;

      final rect = RRect.fromRectAndRadius(
        Rect.fromLTWH(x, y, barWidth, height),
        const Radius.circular(4),
      );

      final paint = Paint()
        ..color = color.withOpacity(0.8)
        ..style = PaintingStyle.fill;

      canvas.drawRRect(rect, paint);

      // Add glow effect
      final glowPaint = Paint()
        ..color = color.withOpacity(0.3)
        ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 4);

      canvas.drawRRect(rect, glowPaint);
    }
  }

  @override
  bool shouldRepaint(covariant MatrixBarPainter oldDelegate) {
    return animationValue != oldDelegate.animationValue;
  }
}

class ChargingSessionScreen extends StatefulWidget {
  final String? stationUid;
  final String? connectorId;
  final double initialCharge;
  final bool isDirectMode; // Flag to indicate Direct Mode (UI testing)

  const ChargingSessionScreen({
    super.key,
    this.stationUid,
    this.connectorId,
    this.initialCharge = 0.75,
    this.isDirectMode = false, // Default to Normal Mode
  });

  @override
  State<ChargingSessionScreen> createState() => _ChargingSessionScreenState();
}

class _ChargingSessionScreenState extends State<ChargingSessionScreen>
    with TickerProviderStateMixin {
  late double _chargePercentage;
  String _currentPrice = "₹23.45";
  String _currentPower = "15.6 kW";
  String _powerOutput = "50 kW";
  String _co2EmissionSaved = "3.2 kg";
  final String _environmentalImpactCO2 = "0.05 kg CO2";
  final String _eta = "1h 15m";
  bool _isCharging = true;
  double _sliderValue = 0.0;
  ChargingSession? _session;
  bool _isLoadingSession = false;
  String? _errorMessage;

  // Real charging flow manager
  final ChargingFlowManager _chargingFlowManager = ChargingFlowManager();
  String? _transactionId;
  String? _authorizationReference;
  Map<String, dynamic>? _realTimeChargingData;

  // Size properties
  final double progressSize = 240.0; // Reduced size
  final double strokeWidth = 12.0; // Reduced stroke width

  // Animation controllers
  late AnimationController _progressAnimationController;
  late AnimationController _rotationAnimationController;
  late AnimationController _matrixAnimationController;
  late AnimationController _pulseAnimationController;

  // Matrix bar data
  late List<double> _matrixValues;
  Timer? _matrixTimer;

  Timer? _progressTimer;

  @override
  void initState() {
    super.initState();
    _chargePercentage = widget.initialCharge;

    // Initialize matrix values
    _matrixValues =
        List.generate(12, (index) => 0.3 + math.Random().nextDouble() * 0.7);

    // Initialize animation controllers
    _progressAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2000),
    );

    _rotationAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 8),
    );

    _matrixAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    _pulseAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    );

    // Start animations
    _progressAnimationController.value = 0.0;
    _progressAnimationController.animateTo(_chargePercentage,
        curve: Curves.easeInOut);
    _rotationAnimationController.repeat();
    _matrixAnimationController.forward();
    _pulseAnimationController.repeat(reverse: true);

    // Start matrix animation
    _startMatrixAnimation();

    // Initialize error message to empty
    _errorMessage = null;

    // Check if we're in Direct Mode (UI testing)
    if (widget.isDirectMode) {
      debugPrint(
          '🔧 DIRECT MODE: Starting simulation with mock data for UI testing');
      _startChargingSimulation();
    } else if (widget.stationUid != null && widget.connectorId != null) {
      debugPrint('🔌 NORMAL MODE: Fetching real charging session data');
      _fetchChargingSession();
    } else {
      debugPrint('⚠️ FALLBACK: No station/connector data, starting simulation');
      _startChargingSimulation();
    }
  }

  void _startMatrixAnimation() {
    _matrixTimer = Timer.periodic(const Duration(milliseconds: 500), (timer) {
      if (!mounted) {
        timer.cancel();
        return;
      }

      setState(() {
        _matrixValues = List.generate(
            12, (index) => 0.3 + math.Random().nextDouble() * 0.7);
      });
    });
  }

  // Start real charging session using the API flow with AUTHENTIC PARAMETERS
  Future<void> _fetchChargingSession() async {
    // Prevent multiple simultaneous requests
    if (_isLoadingSession) return;

    if (!mounted) return;

    setState(() {
      _isLoadingSession = true;
      _errorMessage = null;
    });

    try {
      debugPrint(
          '🚀 Starting real charging session flow with AUTHENTIC PARAMETERS');

      // CRITICAL: Use authentic charging parameters from global service
      final chargingParamsService = ChargingParametersService();

      // Get authentic parameters or use fallbacks
      final authenticChargingValue =
          chargingParamsService.hasAuthenticParameters()
              ? chargingParamsService.getChargingValue()
              : 20.0;
      final authenticChargeType = chargingParamsService.hasAuthenticParameters()
          ? chargingParamsService.getChargeType()
          : 'units';
      final authenticPricePerUnit =
          chargingParamsService.hasAuthenticParameters()
              ? chargingParamsService.getPricePerUnit()
              : 0.25;

      debugPrint('🔌 CHARGING SESSION USING AUTHENTIC PARAMETERS:');
      debugPrint('  Charging Value: $authenticChargingValue');
      debugPrint('  Charge Type: $authenticChargeType');
      debugPrint('  Price Per Unit: ₹$authenticPricePerUnit');
      debugPrint(
          '  Max Power: ${chargingParamsService.hasAuthenticParameters() ? chargingParamsService.getMaxPower() : 0.0}kW');

      // Start the complete charging flow using AUTHENTIC API DATA
      await _chargingFlowManager.startCompleteChargingFlow(
        evseUid: widget.stationUid ?? 'DEFAULT_EVSE',
        connectorId: widget.connectorId ?? 'DEFAULT_CONNECTOR',
        chargingValue:
            authenticChargingValue, // AUTHENTIC VALUE FROM USER SELECTION
        instantCharging: false,
        chargeType: authenticChargeType, // AUTHENTIC CHARGE TYPE
        walletBalance: 100.0, // TODO: Get from wallet service
        pricePerUnit:
            authenticPricePerUnit, // AUTHENTIC PRICE FROM STATION DETAIL API
        onDataReceived: (Map<String, dynamic> data) {
          debugPrint('📊 Received real-time charging data: $data');

          if (!mounted) return;

          setState(() {
            _realTimeChargingData = data;

            // Update UI with real API data - NO MORE MOCK CALCULATIONS!
            if (data['unit'] != null) {
              // CRITICAL FIX: API returns kW (power), not kWh (energy)
              double unitValue = (data['unit'] as num).toDouble();

              // Debug logging to understand the API response format
              debugPrint(
                  '🔍 CRITICAL DEBUG: Raw unit value from API: $unitValue kW (power)');

              // Display as kW (power) not kWh (energy)
              _currentPower = "${unitValue.toStringAsFixed(1)} kW";
              debugPrint(
                  '✅ POWER VALUE: Displaying ${unitValue.toStringAsFixed(1)} kW');
            }

            if (data['amount'] != null) {
              _currentPrice =
                  "₹${data['amount'].toStringAsFixed(2)}"; // Use rupee symbol for authentic pricing
            }

            if (data['co2'] != null) {
              _co2EmissionSaved = "${data['co2'].toStringAsFixed(1)} kg";
            }

            if (data['power_output'] != null) {
              _powerOutput =
                  "${data['power_output'].toStringAsFixed(1)} kW"; // AUTHENTIC POWER OUTPUT
            }

            if (data['soc'] != null) {
              // SOC (State of Charge) is typically in percentage
              double socPercentage = (data['soc'] as num).toDouble() / 100.0;
              _chargePercentage = socPercentage.clamp(0.0, 1.0);

              // Animate to the new percentage
              _progressAnimationController.animateTo(
                _chargePercentage,
                duration: const Duration(milliseconds: 1000),
                curve: Curves.easeInOut,
              );
            }

            // Update session data with real values
            if (_session != null) {
              _session = _session!.copyWith(
                currentCharge: _chargePercentage,
                energyDelivered: data['unit']?.toDouble() ??
                    _session!
                        .energyDelivered, // This is actually power (kW), not energy
                cost: data['amount']?.toDouble() ?? _session!.cost,
                co2Saved: data['co2']?.toDouble() ?? _session!.co2Saved,
                currentPower:
                    data['power_output']?.toDouble() ?? _session!.currentPower,
              );
            }
          });
        },
        onError: (String error) {
          debugPrint('❌ Charging flow error: $error');

          if (!mounted) return;

          setState(() {
            _errorMessage = error;
            _isLoadingSession = false;
          });

          // Fall back to simulation if real API fails
          _startChargingSimulation();
        },
        onSessionComplete: () {
          debugPrint('🏁 Charging session completed');

          if (!mounted) return;

          setState(() {
            _isCharging = false;
          });

          ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
            content: Text('Charging session completed!'),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            margin: EdgeInsets.all(16),
          ));
        },
        onSessionVerified: (Map<String, dynamic> sessionData) {
          debugPrint('✅ Session verified: $sessionData');

          if (!mounted) return;

          setState(() {
            _isLoadingSession = false;
            _transactionId = sessionData['id'];
            _authorizationReference = sessionData['authorization_reference'];

            // Create session object with verified data
            _session = ChargingSession(
              id: sessionData['id'] ??
                  'REAL-${DateTime.now().millisecondsSinceEpoch}',
              stationUid: widget.stationUid ?? 'REAL-STATION',
              connectorId: widget.connectorId ?? 'REAL-CONNECTOR',
              startTime: DateTime.now(),
              currentCharge: _chargePercentage,
              currentPower: 0.0, // Will be updated from real-time data
              energyDelivered: 0.0, // Will be updated from real-time data
              cost: 0.0, // Will be updated from real-time data
              co2Saved: 0.0, // Will be updated from real-time data
            );
          });
        },
      );
    } catch (e, stackTrace) {
      debugPrint('❌ Error in charging session flow: $e');
      debugPrint('Stack trace: $stackTrace');

      if (!mounted) return;

      setState(() {
        _isLoadingSession = false;
        _errorMessage = 'Failed to start charging session';
        if (e is SocketException) {
          _errorMessage = 'Network error. Please check your connection.';
        } else if (e is TimeoutException) {
          _errorMessage = 'Request timed out. Please try again.';
        } else if (e is FormatException) {
          _errorMessage = 'Invalid data received from server';
        }

        // Fall back to simulation if real API fails
        _startChargingSimulation();
      });
    }
  }

  // Simulate a charging session with dynamic updates
  void _startChargingSimulation() {
    _progressTimer = Timer.periodic(const Duration(seconds: 2), (timer) {
      if (!_isCharging || !mounted) {
        timer.cancel();
        return;
      }

      setState(() {
        if (_chargePercentage < 1.0) {
          // Add a bit of randomness to make it look more realistic
          double increment = 0.01 + (math.Random().nextDouble() * 0.02);
          _chargePercentage = (_chargePercentage + increment).clamp(0.0, 1.0);

          // Animate to the new percentage
          _progressAnimationController.animateTo(
            _chargePercentage,
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeInOut,
          );

          // Update metrics with authentic pricing format
          double powerBase = 15.6 + (_chargePercentage * 50);
          _currentPower = "${powerBase.toStringAsFixed(1)} kW";

          double priceBase = 23.45 + (_chargePercentage * 30);
          _currentPrice =
              "₹${priceBase.toStringAsFixed(2)}"; // Use rupee symbol for consistency

          double co2Base = 3.2 + (_chargePercentage * 5);
          _co2EmissionSaved = "${co2Base.toStringAsFixed(1)} kg";

          // Update session data if available
          if (_session != null) {
            _session = _session!.copyWith(
                currentCharge: _chargePercentage,
                energyDelivered:
                    powerBase, // This is actually power, not energy
                cost: priceBase,
                co2Saved: co2Base);
          }
        } else {
          _isCharging = false;
          timer.cancel();
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
              content: Text('Charging complete!'),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
              margin: EdgeInsets.all(16),
            ));
          }
        }
      });
    });
  }

  @override
  void dispose() {
    _progressAnimationController.dispose();
    _rotationAnimationController.dispose();
    _matrixAnimationController.dispose();
    _pulseAnimationController.dispose();
    _progressTimer?.cancel();
    _matrixTimer?.cancel();
    super.dispose();
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Menu button
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: Colors.white.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: IconButton(
              icon:
                  const Icon(Icons.menu_rounded, color: Colors.white, size: 24),
              onPressed: () => Navigator.pop(context),
            ),
          ),

          // Power button
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.3),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: Colors.white.withOpacity(0.2),
                width: 1,
              ),
            ),
            child: IconButton(
              icon: const Icon(Icons.power_settings_new_rounded,
                  color: Colors.white, size: 24),
              onPressed: () => _showStopChargingDialog(),
            ),
          ),
        ],
      ),
    );
  }

  // Enhanced stop charging method with proper API integration
  void _stopCharging() async {
    try {
      setState(() {
        _isCharging = false;
      });

      // Cancel the progress timer
      _progressTimer?.cancel();

      // If we're in normal mode and have real charging session data, stop via API
      if (!widget.isDirectMode && _transactionId != null) {
        // Call the charging flow manager to stop the session
        await _chargingFlowManager.stopChargingSession(
          transactionId: _transactionId!,
          onError: (errorMessage) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(errorMessage),
                  backgroundColor: Colors.red,
                  behavior: SnackBarBehavior.floating,
                  margin: const EdgeInsets.all(16),
                  duration: const Duration(seconds: 2),
                ),
              );
            }
          },
          onSessionStopped: (finalBillingData) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Charging session stopped successfully!'),
                  backgroundColor: Colors.green,
                  behavior: SnackBarBehavior.floating,
                  margin: EdgeInsets.all(16),
                  duration: Duration(seconds: 1),
                ),
              );
            }
          },
        );
      } else {
        debugPrint('🔧 Direct mode or no transaction ID - stopping simulation');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Charging session stopped!'),
              backgroundColor: Colors.orange,
              behavior: SnackBarBehavior.floating,
              margin: EdgeInsets.all(16),
              duration: Duration(seconds: 1),
            ),
          );
        }
      }

      // Navigate back to previous screen after a short delay
      await Future.delayed(const Duration(milliseconds: 500));
      if (mounted) {
        Navigator.pop(context);
      }
    } catch (e) {
      debugPrint('❌ Error in _stopCharging: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error stopping charging: $e'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            margin: const EdgeInsets.all(16),
          ),
        );
        Navigator.pop(context);
      }
    }
  }

  void _showStopChargingDialog() {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 64,
                height: 64,
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.power_settings_new_rounded,
                  color: Colors.red,
                  size: 32,
                ),
              ),
              const SizedBox(height: 16),
              const Text(
                'Stop Charging?',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                'Are you sure you want to stop the charging session?',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
              const SizedBox(height: 24),
              Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () => Navigator.pop(context),
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                      child: const Text('Cancel'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                        Navigator.pop(context);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text(
                        'Stop',
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // We'll use the existing strokeWidth field but modify it in initState

  // Single real car image - Tesla Model S (high quality)
  final String _realCarImage =
      'https://images.unsplash.com/photo-1617788138017-80ad40651399?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2071&q=80';

  // Build single real car image widget
  Widget _buildRealCarImage() {
    return Container(
      height: 120,
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Image.network(
          _realCarImage,
          fit: BoxFit.cover,
          loadingBuilder: (context, child, loadingProgress) {
            if (loadingProgress == null) return child;
            return Container(
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(16),
              ),
              child: const Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            );
          },
          errorBuilder: (context, error, stackTrace) {
            return Container(
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.3),
                  width: 2,
                ),
              ),
              child: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.directions_car,
                      size: 48,
                      color: Colors.white,
                    ),
                    SizedBox(height: 8),
                    Text(
                      'Electric Vehicle',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildVehicleVisualization() {
    return Container(
      height: 200, // Increased height for better car image display
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Single real car image at the top
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            height: 120,
            child: _buildRealCarImage(),
          ),
          // Matrix bar visualization
          Positioned(
            bottom: 15,
            left: 40,
            right: 40,
            child: AnimatedBuilder(
              animation: _matrixAnimationController,
              builder: (context, child) {
                return Container(
                  height: 30, // Further reduced height
                  child: CustomPaint(
                    painter: MatrixBarPainter(
                      values: _matrixValues,
                      color: const Color(0xFF22C55E),
                      animationValue: _matrixAnimationController.value,
                    ),
                  ),
                );
              },
            ),
          ),

          // Green platform
          Positioned(
            bottom: 8,
            left: 30,
            right: 30,
            child: Container(
              height: 30, // Reduced height
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    const Color(0xFF4ADE80).withOpacity(0.8),
                    const Color(0xFF22C55E).withOpacity(0.6),
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
                borderRadius: BorderRadius.circular(100),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF22C55E).withOpacity(0.3),
                    blurRadius: 15, // Reduced blur
                    spreadRadius: 3, // Reduced spread
                    offset: const Offset(0, 6), // Adjusted offset
                  ),
                ],
              ),
            ),
          ),

          // Car visualization with animation
          AnimatedBuilder(
            animation: _pulseAnimationController,
            builder: (context, child) {
              final scale = 1.0 + (_pulseAnimationController.value * 0.02);
              return Transform.scale(
                scale: scale,
                child: Container(
                  height: 80, // Further reduced height
                  width: 180, // Further reduced width
                  decoration: BoxDecoration(
                    image: const DecorationImage(
                      image: NetworkImage(
                        'https://firebasestorage.googleapis.com/v0/b/ecoplug-92121.appspot.com/o/defaults%2Fmodern_ev_front.png?alt=media&token=18a51cfa-9a3a-4234-869a-c703c7233092',
                      ),
                      fit: BoxFit.contain,
                    ),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Stack(
                    children: [
                      // Charging indicator on car
                      if (_isCharging)
                        Positioned(
                          top: 40,
                          right: 60,
                          child: Container(
                            padding: const EdgeInsets.all(7),
                            decoration: BoxDecoration(
                              color: const Color(0xFF22C55E),
                              shape: BoxShape.circle,
                              boxShadow: [
                                BoxShadow(
                                  color:
                                      const Color(0xFF22C55E).withOpacity(0.5),
                                  blurRadius: 15,
                                  spreadRadius: 3,
                                ),
                              ],
                            ),
                            child: const Icon(
                              Icons.bolt_rounded,
                              color: Colors.white,
                              size: 18,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildChargingProgress() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 6), // Further reduced margin
      width: progressSize * 0.75, // Smaller progress indicator
      height: progressSize * 0.75, // Smaller progress indicator
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Outer glow effect
          Container(
            width: progressSize,
            height: progressSize,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF22C55E).withOpacity(0.25),
                  blurRadius: 30,
                  spreadRadius: 15,
                ),
              ],
            ),
          ),

          // Background circle
          Container(
            width: progressSize - 40,
            height: progressSize - 40,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.white.withOpacity(0.1),
              border: Border.all(
                color: Colors.white.withOpacity(0.2),
                width: 2,
              ),
            ),
          ),

          // Progress indicator
          AnimatedBuilder(
            animation: Listenable.merge([
              _progressAnimationController,
              _rotationAnimationController,
            ]),
            builder: (context, child) {
              return CustomPaint(
                size: Size(progressSize, progressSize),
                painter: CircularProgressPainter(
                  progress: _progressAnimationController.value,
                  progressColor: const Color(0xFF22C55E),
                  strokeWidth: strokeWidth,
                  addGlow: true,
                  rotationAngle:
                      _rotationAnimationController.value * 2 * math.pi,
                ),
              );
            },
          ),

          // Center content
          Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Battery icon
              Container(
                width: 44,
                height: 44,
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.15),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Center(
                  child: Transform.rotate(
                    angle: -math.pi / 2,
                    child: const Icon(
                      Icons.battery_charging_full_rounded,
                      color: Colors.white,
                      size: 28,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 12),
              // Percentage
              AnimatedBuilder(
                animation: _progressAnimationController,
                builder: (context, child) {
                  return Text(
                    '${(_progressAnimationController.value * 100).toInt()}%',
                    style: const TextStyle(
                      fontSize: 48,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      height: 1,
                    ),
                  );
                },
              ),
              const SizedBox(height: 4),
              // Status text
              Text(
                _isCharging ? 'Charging' : 'Charged',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                  color: Colors.white.withOpacity(0.9),
                  letterSpacing: 1,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMetricsGrid() {
    return Container(
      margin: const EdgeInsets.symmetric(
          horizontal: 16, vertical: 4), // Reduced vertical margin
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: _buildMetricCard(
                  icon: Icons.currency_rupee_rounded,
                  title: 'Current Price',
                  value: _currentPrice,
                  gradient: const LinearGradient(
                    colors: [Color(0xFF4ADE80), Color(0xFF22C55E)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
              ),
              const SizedBox(width: 8), // Further reduced spacing
              Expanded(
                child: _buildMetricCard(
                  icon: Icons.bolt_rounded,
                  title: 'Current Power',
                  value: _currentPower,
                  gradient: const LinearGradient(
                    colors: [Color(0xFF60A5FA), Color(0xFF3B82F6)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12), // Reduced spacing
          Row(
            children: [
              Expanded(
                child: _buildMetricCard(
                  icon: Icons.speed_rounded,
                  title: 'Power Output',
                  value: _powerOutput,
                  gradient: const LinearGradient(
                    colors: [Color(0xFFFBBF24), Color(0xFFF59E0B)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
              ),
              const SizedBox(width: 12), // Reduced spacing
              Expanded(
                child: _buildMetricCard(
                  icon: Icons.eco_rounded,
                  title: 'CO₂ Saved',
                  value: _co2EmissionSaved,
                  gradient: const LinearGradient(
                    colors: [Color(0xFF34D399), Color(0xFF10B981)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMetricCard({
    required IconData icon,
    required String title,
    required String value,
    required LinearGradient gradient,
  }) {
    return Container(
      height: 85, // Reduced height
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20), // Slightly smaller radius
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.06), // Lighter shadow
            blurRadius: 15, // Reduced blur
            offset: const Offset(0, 6), // Smaller offset
            spreadRadius: 0,
          ),
        ],
      ),
      child: Stack(
        children: [
          // Gradient overlay
          Positioned(
            top: 0,
            right: 0,
            child: Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                gradient: gradient.scale(0.1),
                borderRadius: const BorderRadius.only(
                  topRight: Radius.circular(24),
                  bottomLeft: Radius.circular(80),
                ),
              ),
            ),
          ),
          // Content
          Padding(
            padding: const EdgeInsets.all(12), // Reduced padding
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  width: 30, // Smaller container
                  height: 30, // Smaller container
                  decoration: BoxDecoration(
                    gradient: gradient,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    icon,
                    color: Colors.white,
                    size: 18, // Smaller icon
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      value,
                      style: const TextStyle(
                        color: Colors.black87,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoBadges() {
    return Padding(
      padding: const EdgeInsets.symmetric(
          horizontal: 16, vertical: 2), // Further reduced padding
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _buildInfoBadge(
            icon: Icons.eco_rounded,
            text: 'You saved $_environmentalImpactCO2',
            color: const Color(0xFF10B981),
            backgroundColor: const Color(0xFF10B981).withOpacity(0.1),
          ),
          const SizedBox(width: 12),
          _buildInfoBadge(
            icon: Icons.access_time_rounded,
            text: 'ETA: $_eta',
            color: const Color(0xFF3B82F6),
            backgroundColor: const Color(0xFF3B82F6).withOpacity(0.1),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoBadge({
    required IconData icon,
    required String text,
    required Color color,
    required Color backgroundColor,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: color.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            color: color,
            size: 14, // Smaller icon
          ),
          const SizedBox(width: 6),
          Text(
            text,
            style: TextStyle(
              color: color,
              fontSize: 12, // Smaller font size
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionSlider() {
    return Container(
      // PERMANENTLY EXPANDED: Fixed margins and height for always-visible state
      margin: const EdgeInsets.fromLTRB(
          16, 12, 16, 20), // Enhanced margins for permanent visibility
      height: 130, // Increased height for better permanent visibility
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(28),
        // Enhanced shadow for permanent visibility
        boxShadow: [
          BoxShadow(
            color: Colors.black
                .withValues(alpha: 0.15), // Stronger shadow for prominence
            blurRadius: 30,
            offset: const Offset(0, 12),
            spreadRadius: 3,
          ),
          BoxShadow(
            color: Colors.black
                .withValues(alpha: 0.08), // Additional subtle shadow
            blurRadius: 15,
            offset: const Offset(0, 6),
            spreadRadius: 1,
          ),
        ],
        // Subtle border for definition
        border: Border.all(
          color: Colors.grey.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // PERMANENTLY EXPANDED: Enhanced header with prominent charging status
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8), // Larger padding for prominence
                decoration: BoxDecoration(
                  color: _isCharging
                      ? const Color(0xFF22C55E).withValues(alpha: 0.15)
                      : Colors.red.withValues(alpha: 0.15),
                  borderRadius: BorderRadius.circular(25),
                  border: Border.all(
                    color: _isCharging
                        ? const Color(0xFF22C55E).withValues(alpha: 0.4)
                        : Colors.red.withValues(alpha: 0.4),
                    width: 2, // Thicker border for visibility
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      _isCharging ? Icons.bolt : Icons.power_off,
                      color: _isCharging ? const Color(0xFF22C55E) : Colors.red,
                      size: 18, // Larger icon
                    ),
                    const SizedBox(width: 8),
                    Text(
                      _isCharging ? 'CHARGING ACTIVE' : 'CHARGING STOPPED',
                      style: TextStyle(
                        fontSize: 14, // Larger text for permanent visibility
                        fontWeight: FontWeight.bold,
                        color:
                            _isCharging ? const Color(0xFF22C55E) : Colors.red,
                        letterSpacing: 0.8,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16), // Increased spacing for permanent layout
          AnimatedSwitcher(
            duration: const Duration(milliseconds: 300),
            child: Text(
              _sliderValue > 0.1
                  ? (_sliderValue > 0.8
                      ? 'Release to Stop Charging'
                      : 'Keep Sliding to Stop')
                  : 'Slide to Stop Charging',
              key: ValueKey(_sliderValue > 0.8
                  ? 'release'
                  : _sliderValue > 0.1
                      ? 'sliding'
                      : 'slide'),
              style: TextStyle(
                fontSize: 18, // Larger text for permanent visibility
                fontWeight: FontWeight.w700, // Bolder for prominence
                color: _sliderValue > 0.5 ? Colors.red[700] : Colors.black87,
                letterSpacing: 0.2,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: 16), // Increased spacing for permanent layout
          Container(
            // Enhanced slider track with improved visual feedback
            margin: const EdgeInsets.symmetric(horizontal: 20),
            height: 60,
            decoration: BoxDecoration(
              // Dynamic background color based on slider progress
              color: _sliderValue > 0.1
                  ? Color.lerp(Colors.grey[50], Colors.red[50], _sliderValue)
                  : Colors.grey[50],
              borderRadius: BorderRadius.circular(32),
              border: Border.all(
                color: _sliderValue > 0.3
                    ? Colors.red.withValues(alpha: 0.3 + (_sliderValue * 0.4))
                    : Colors.grey.withValues(alpha: 0.2),
                width: 2,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                  spreadRadius: 1,
                ),
                // Add red glow when sliding
                if (_sliderValue > 0.3)
                  BoxShadow(
                    color: Colors.red.withValues(alpha: 0.2 * _sliderValue),
                    blurRadius: 15,
                    offset: const Offset(0, 0),
                    spreadRadius: 2,
                  ),
              ],
            ),
            child: Stack(
              children: [
                // Animated progress fill
                AnimatedContainer(
                  duration: const Duration(milliseconds: 100),
                  width:
                      _sliderValue * (MediaQuery.of(context).size.width - 80),
                  height: 60,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Colors.red.withValues(alpha: 0.1),
                        Colors.red.withValues(alpha: 0.3 * _sliderValue),
                      ],
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                    ),
                    borderRadius: BorderRadius.circular(32),
                  ),
                ),
                // Left side text (CHARGING)
                Positioned(
                  left: 24,
                  top: 0,
                  bottom: 0,
                  child: Center(
                    child: AnimatedOpacity(
                      duration: const Duration(milliseconds: 200),
                      opacity:
                          _sliderValue < 0.3 ? 1.0 : 1.0 - (_sliderValue * 2),
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 14, vertical: 8),
                        decoration: BoxDecoration(
                          color: const Color(0xFF22C55E),
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: const Color(0xFF22C55E)
                                  .withValues(alpha: 0.3),
                              blurRadius: 6,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: const Text(
                          'CHARGING',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 13,
                            fontWeight: FontWeight.bold,
                            letterSpacing: 0.5,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                // Right side text (STOP)
                Positioned(
                  right: 24,
                  top: 0,
                  bottom: 0,
                  child: Center(
                    child: AnimatedOpacity(
                      duration: const Duration(milliseconds: 200),
                      opacity: _sliderValue > 0.3 ? 1.0 : 0.5,
                      child: AnimatedScale(
                        duration: const Duration(milliseconds: 200),
                        scale: _sliderValue > 0.7 ? 1.1 : 1.0,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 14, vertical: 8),
                          decoration: BoxDecoration(
                            color: _sliderValue > 0.7
                                ? Colors.red[600]
                                : Colors.red[400],
                            borderRadius: BorderRadius.circular(16),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.red.withValues(
                                    alpha: 0.3 + (_sliderValue * 0.3)),
                                blurRadius: 6 + (_sliderValue * 4),
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: const Text(
                            'STOP',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 13,
                              fontWeight: FontWeight.bold,
                              letterSpacing: 0.5,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                // Enhanced draggable slider button
                AnimatedContainer(
                  duration:
                      Duration(milliseconds: _sliderValue == 0.0 ? 300 : 100),
                  curve:
                      _sliderValue == 0.0 ? Curves.elasticOut : Curves.easeOut,
                  margin: EdgeInsets.only(
                    left: _sliderValue *
                        (MediaQuery.of(context).size.width - 120),
                    top: 4,
                    bottom: 4,
                  ),
                  child: GestureDetector(
                    onHorizontalDragStart: (details) {
                      // Provide haptic feedback when starting to drag
                      // HapticFeedback.lightImpact(); // Uncomment if you want haptic feedback
                    },
                    onHorizontalDragUpdate: (details) {
                      setState(() {
                        // Calculate slider value based on the container width minus button width and margins
                        double containerWidth =
                            MediaQuery.of(context).size.width - 120;
                        _sliderValue =
                            (details.localPosition.dx / containerWidth)
                                .clamp(0.0, 1.0);
                      });
                    },
                    onHorizontalDragEnd: (details) {
                      if (_sliderValue > 0.85) {
                        // Trigger stop charging action
                        _stopCharging();
                      } else {
                        // Animate back to start position with elastic effect
                        setState(() {
                          _sliderValue = 0.0;
                        });
                      }
                    },
                    child: Container(
                      // Enhanced power button with dynamic styling
                      width: 52, // Slightly smaller for better sliding feel
                      height: 52,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: _sliderValue > 0.5
                              ? Colors.red.withValues(alpha: 0.8)
                              : _sliderValue > 0.3
                                  ? Colors.red.withValues(alpha: 0.6)
                                  : Colors.red.withValues(alpha: 0.4),
                          width: _sliderValue > 0.7 ? 4 : 3,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.25),
                            blurRadius: 20 + (_sliderValue * 10),
                            offset: const Offset(0, 8),
                            spreadRadius: 2,
                          ),
                          BoxShadow(
                            color: Colors.red.withValues(
                                alpha: 0.15 + (_sliderValue * 0.25)),
                            blurRadius: 25 + (_sliderValue * 15),
                            offset: const Offset(0, 0),
                            spreadRadius: 4 + (_sliderValue * 2),
                          ),
                          // Enhanced glow effect when sliding
                          BoxShadow(
                            color: Colors.white.withValues(alpha: 0.8),
                            blurRadius: 5,
                            offset: const Offset(0, 0),
                            spreadRadius: 1,
                          ),
                        ],
                      ),
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          // Enhanced pulsing effect with dynamic intensity
                          if (_isCharging)
                            AnimatedBuilder(
                              animation: _pulseAnimationController,
                              builder: (context, child) {
                                return Container(
                                  width: 52 +
                                      (_pulseAnimationController.value * 8) +
                                      (_sliderValue * 8),
                                  height: 52 +
                                      (_pulseAnimationController.value * 8) +
                                      (_sliderValue * 8),
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                      color: Colors.red.withValues(
                                        alpha: (0.4 + (_sliderValue * 0.3)) *
                                            (1 -
                                                _pulseAnimationController
                                                    .value),
                                      ),
                                      width: 3 + (_sliderValue * 2),
                                    ),
                                  ),
                                );
                              },
                            ),
                          // Enhanced power icon with dynamic styling
                          AnimatedContainer(
                            duration: const Duration(milliseconds: 100),
                            padding: const EdgeInsets.all(4),
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: _sliderValue > 0.5
                                  ? Colors.red.withValues(
                                      alpha: 0.1 + (_sliderValue * 0.1))
                                  : Colors.transparent,
                            ),
                            child: AnimatedRotation(
                              duration: const Duration(milliseconds: 200),
                              turns: _sliderValue *
                                  0.1, // Slight rotation when sliding
                              child: Icon(
                                Icons.power_settings_new_rounded,
                                color: _sliderValue > 0.7
                                    ? Colors.red[800]
                                    : _sliderValue > 0.5
                                        ? Colors.red[700]
                                        : Colors.red[600],
                                size: 28 + (_sliderValue * 4), // Dynamic size
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF7DD3C0),
              Color(0xFF22C55E),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildHeader(),
              if (_errorMessage != null)
                Container(
                  margin:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.red.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: Colors.red.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.error_outline_rounded,
                        color: Colors.red,
                        size: 24,
                      ),
                      const SizedBox(width: 10),
                      Expanded(
                        child: Text(
                          _errorMessage!,
                          style: const TextStyle(
                            color: Colors.red,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              // Main content area that fits the screen
              Expanded(
                child: Column(
                  children: [
                    // Content that can resize to fit available space
                    Expanded(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        mainAxisSize:
                            MainAxisSize.min, // Take minimum required space
                        children: [
                          _buildVehicleVisualization(),
                          _buildChargingProgress(),
                          _buildMetricsGrid(),
                          _buildInfoBadges(),
                        ],
                      ),
                    ),
                    // Action slider always visible at bottom
                    _buildActionSlider(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
