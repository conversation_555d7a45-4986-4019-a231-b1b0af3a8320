import 'dart:async';
import 'package:flutter/material.dart';

import 'payment_service.dart';

/// Service for orchestrating payment initiation, potentially selecting a gateway.
/// In this version, it primarily acts as a pass-through to `PaymentService`,
/// but could be expanded to select different payment services based on `orderData` or `source`.
class PaymentGatewayService {
  // Singleton pattern
  static final PaymentGatewayService _instance =
      PaymentGatewayService._internal();
  factory PaymentGatewayService() => _instance;

  // Generic payment service
  final PaymentService _paymentService;

  PaymentGatewayService._internal() : _paymentService = PaymentService();

  /// Initiate payment, expecting `orderData` from a backend.
  ///
  /// This method will use the `PaymentService` to handle the actual payment flow.
  /// The `orderData` is crucial and must contain all necessary information
  /// for the `PaymentService` to interact with the payment provider.
  ///
  /// Returns a Stream from `PaymentService` that emits payment status updates.
  Future<Stream<Map<String, dynamic>>> initiatePayment({
    required double amount,
    required String customerName,
    required String customerEmail,
    required String customerPhone,
    required Map<String, dynamic>
        orderData, // Server-generated order data is now required
    String? promoCode,
    String source =
        'default_gateway', // Can be used to select gateway if logic expands
    BuildContext?
        context, // May be needed if UI interaction is required at this level
  }) async {
    debugPrint(
      'PaymentGatewayService: Initiating payment. Amount: $amount, Source: $source, OrderData: $orderData',
    );

    // Use the generic payment service for all payment gateways
    try {
      return await _paymentService.initiatePayment(
        amount: amount,
        customerName: customerName,
        customerEmail: customerEmail,
        customerPhone: customerPhone,
        orderData: orderData, // Pass the server-generated orderData
        promoCode: promoCode,
      );
    } catch (e) {
      debugPrint(
          'Error in PaymentGatewayService during payment initiation: $e');
      // If `_paymentService.initiatePayment` throws an error before returning a stream,
      // it will be caught here. We should return a stream that emits this error.
      final errorController =
          StreamController<Map<String, dynamic>>.broadcast();
      errorController.addError(
        Exception('PaymentGatewayService: Failed to initiate payment: $e'),
      );
      errorController.close();
      return errorController.stream;
    }
  }

  /// Dispose resources.
  /// Since this service no longer manages its own stream controller,
  /// dispose might not be needed unless other resources are managed here.
  void dispose() {
    // If PaymentGatewayService itself held resources (e.g., subscriptions to other services)
    // they would be cleaned up here. _paymentService.dispose() would be called elsewhere
    // if PaymentService is a singleton managed globally.
    debugPrint('PaymentGatewayService disposed.');
  }
}
