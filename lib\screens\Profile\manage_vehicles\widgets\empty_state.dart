import 'package:flutter/material.dart';
import '../../../../utils/app_theme.dart';
import '../../../../utils/animations.dart';
import '../../../../widgets/animated_button.dart';

class EmptyVehicleState extends StatelessWidget {
  final VoidCallback onAddVehicle;

  const EmptyVehicleState({
    super.key,
    required this.onAddVehicle,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SingleChildScrollView(
        physics: const BouncingScrollPhysics(),
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              FadeInAnimation(
                duration: AppTheme.mediumAnimationDuration,
                child: Container(
                  width: 180,
                  height: 180,
                  decoration: BoxDecoration(
                    color: AppTheme.primaryLightColor,
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: PulseAnimation(
                      duration: const Duration(milliseconds: 2000),
                      child: Icon(
                        Icons.directions_car_outlined,
                        size: 100,
                        color: AppTheme.primaryColor,
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 32),
              FadeInAnimation(
                duration: AppTheme.mediumAnimationDuration,
                delay: const Duration(milliseconds: 200),
                child: Text(
                  'No Vehicles Found',
                  style: AppTheme.headingStyle,
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(height: 16),
              FadeInAnimation(
                duration: AppTheme.mediumAnimationDuration,
                delay: const Duration(milliseconds: 300),
                child: Text(
                  'Add your first vehicle to start managing your EV charging experience',
                  style: AppTheme.bodyStyle,
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(height: 32),
              FadeInAnimation(
                duration: AppTheme.mediumAnimationDuration,
                delay: const Duration(milliseconds: 400),
                child: AnimatedButton(
                  text: 'Add Your First Vehicle',
                  icon: Icons.add,
                  onPressed: onAddVehicle,
                  gradient: AppTheme.primaryGradient,
                  width: 240,
                  height: 56,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
