import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../features/profile/application/profile_notifier_riverpod.dart';
import '../features/wallet/application/wallet_provider.dart';
import 'auth_manager.dart';
import 'logout_service.dart';

/// Comprehensive login service that ensures fresh data loading
/// This service coordinates login and ensures no stale data persists
class LoginService {
  // Singleton pattern
  static final LoginService _instance = LoginService._internal();
  factory LoginService() => _instance;
  LoginService._internal();

  // Services
  final AuthManager _authManager = AuthManager();
  final LogoutService _logoutService = LogoutService();

  /// CRITICAL: Perform login with fresh data guarantee
  Future<bool> performLoginWithFreshData({
    required String token,
    required Map<String, dynamic> userData,
    WidgetRef? ref,
  }) async {
    try {
      debugPrint('🔐 STARTING LOGIN WITH FRESH DATA GUARANTEE');

      // Step 1: Clear any existing stale data first
      await _clearAnyStaleData();

      // Step 2: Save new login state
      await _saveNewLoginState(token, userData);

      // Step 3: Force refresh all providers if ref is available
      if (ref != null) {
        await _forceRefreshAllProviders(ref);
      }

      // Step 4: Validate login success
      final validationSuccess = await _validateLoginSuccess();

      if (validationSuccess) {
        debugPrint('✅ LOGIN WITH FRESH DATA COMPLETED SUCCESSFULLY');
        return true;
      } else {
        debugPrint('❌ LOGIN VALIDATION FAILED');
        return false;
      }
    } catch (e) {
      debugPrint('❌ ERROR DURING LOGIN WITH FRESH DATA: $e');
      return false;
    }
  }

  /// Step 1: Clear any existing stale data
  Future<void> _clearAnyStaleData() async {
    debugPrint('🧹 CLEARING ANY EXISTING STALE DATA BEFORE LOGIN');

    try {
      // Check if user is already logged in
      final isLoggedIn = await _authManager.isLoggedIn();
      
      if (isLoggedIn) {
        debugPrint('⚠️ User already logged in - clearing stale session');
        // Perform silent logout to clear stale data
        await _logoutService.performCompleteLogout();
      }

      debugPrint('✅ STALE DATA CLEARED');
    } catch (e) {
      debugPrint('⚠️ Error clearing stale data: $e');
    }
  }

  /// Step 2: Save new login state
  Future<void> _saveNewLoginState(String token, Map<String, dynamic> userData) async {
    debugPrint('💾 SAVING NEW LOGIN STATE');

    try {
      // Save login state using AuthManager
      await _authManager.saveLoginState(
        token: token,
        userData: userData,
      );

      debugPrint('✅ NEW LOGIN STATE SAVED');
      debugPrint('User ID: ${userData['id']}');
      debugPrint('User Name: ${userData['name']}');
      debugPrint('User Email: ${userData['email']}');
    } catch (e) {
      debugPrint('⚠️ Error saving login state: $e');
      rethrow;
    }
  }

  /// Step 3: Force refresh all providers
  Future<void> _forceRefreshAllProviders(WidgetRef ref) async {
    debugPrint('🔄 FORCING REFRESH OF ALL PROVIDERS');

    try {
      // Invalidate profile provider to force fresh data load
      ref.invalidate(profileProvider);

      // Invalidate wallet provider if it exists
      try {
        ref.invalidate(walletProvider);
      } catch (e) {
        debugPrint('⚠️ Wallet provider not available: $e');
      }

      // Force refresh profile data
      final profileNotifier = ref.read(profileProvider.notifier);
      await profileNotifier.forceRefreshProfile();

      debugPrint('✅ ALL PROVIDERS REFRESHED');
    } catch (e) {
      debugPrint('⚠️ Error refreshing providers: $e');
    }
  }

  /// Step 4: Validate login success
  Future<bool> _validateLoginSuccess() async {
    debugPrint('✅ VALIDATING LOGIN SUCCESS');

    try {
      // Check if user is logged in
      final isLoggedIn = await _authManager.isLoggedIn();
      if (!isLoggedIn) {
        debugPrint('❌ VALIDATION FAILED: User not logged in');
        return false;
      }

      // Check if user data exists
      final userData = await _authManager.getUserData();
      if (userData == null || userData.isEmpty) {
        debugPrint('❌ VALIDATION FAILED: No user data found');
        return false;
      }

      // Check if token exists
      final token = await _authManager.getToken();
      if (token == null || token.isEmpty) {
        debugPrint('❌ VALIDATION FAILED: No token found');
        return false;
      }

      debugPrint('✅ LOGIN VALIDATION SUCCESSFUL');
      debugPrint('User authenticated: ${userData['name']} (${userData['email']})');
      return true;
    } catch (e) {
      debugPrint('⚠️ Error during login validation: $e');
      return false;
    }
  }

  /// Handle user switching (logout current user, login new user)
  Future<bool> switchUser({
    required String newToken,
    required Map<String, dynamic> newUserData,
    WidgetRef? ref,
  }) async {
    debugPrint('🔄 SWITCHING USER ACCOUNTS');

    try {
      // Step 1: Perform complete logout of current user
      final logoutSuccess = await _logoutService.performCompleteLogout();
      if (!logoutSuccess) {
        debugPrint('❌ Failed to logout current user');
        return false;
      }

      // Step 2: Perform login with fresh data for new user
      final loginSuccess = await performLoginWithFreshData(
        token: newToken,
        userData: newUserData,
        ref: ref,
      );

      if (loginSuccess) {
        debugPrint('✅ USER SWITCH COMPLETED SUCCESSFULLY');
        return true;
      } else {
        debugPrint('❌ Failed to login new user');
        return false;
      }
    } catch (e) {
      debugPrint('❌ ERROR DURING USER SWITCH: $e');
      return false;
    }
  }

  /// Get current authenticated user info
  Future<Map<String, dynamic>?> getCurrentUserInfo() async {
    try {
      final isLoggedIn = await _authManager.isLoggedIn();
      if (!isLoggedIn) {
        return null;
      }

      return await _authManager.getUserData();
    } catch (e) {
      debugPrint('Error getting current user info: $e');
      return null;
    }
  }

  /// Check if current session is valid
  Future<bool> isCurrentSessionValid() async {
    try {
      final isLoggedIn = await _authManager.isLoggedIn();
      final isTokenValid = await _authManager.isTokenValid();
      
      return isLoggedIn && isTokenValid;
    } catch (e) {
      debugPrint('Error checking session validity: $e');
      return false;
    }
  }
}
