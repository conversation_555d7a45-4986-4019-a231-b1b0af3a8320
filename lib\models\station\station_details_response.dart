import 'dart:math' as math;
import 'package:flutter/foundation.dart' show debugPrint;
import '../station.dart' as station_model;

/// Response model for the station details API
class StationDetailsResponse {
  String? message;
  bool? success;
  StationDetailsData? data;
  Wallet? wallet;
  int? instantCharging;

  StationDetailsResponse({
    this.message,
    this.success,
    this.data,
    this.wallet,
    this.instantCharging,
  });

  StationDetailsResponse.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    success = json['success'];
    data =
        json['data'] != null ? StationDetailsData.fromJson(json['data']) : null;
    wallet = json['wallet'] != null ? Wallet.fromJson(json['wallet']) : null;
    instantCharging = json['instant_charging'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['message'] = message;
    data['success'] = success;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    if (wallet != null) {
      data['wallet'] = wallet!.toJson();
    }
    data['instant_charging'] = instantCharging;
    return data;
  }
}

/// Station details data model - COMPREHENSIVE API FIELD MAPPING
class StationDetailsData {
  // CRITICAL: Location data - required for station identification
  double? longitude;
  double? latitude;

  // CRITICAL: Station identification - required for display
  String? name;
  String? address;
  String? city;
  String? state;
  String? postalCode; // NEW: Complete address information

  // CRITICAL: Station operational status - required for charging decisions
  bool? openStatus;
  String? openingTimes; // NEW: Operating hours (e.g., "24 Hours")

  // CRITICAL: Rating and review data - required for user decisions
  double? rate; // NEW: Average rating (e.g., 5.0)
  int? rateTotal; // NEW: Total number of reviews (e.g., 49)

  // CRITICAL: Station media and infrastructure
  String? images; // Station images for visual identification
  Map<String, EvseDetails>?
      evses; // CRITICAL: EVSE infrastructure with dynamic keys

  // CRITICAL: Station unique identifier
  String? uid;

  StationDetailsData({
    this.longitude,
    this.latitude,
    this.name,
    this.address,
    this.city,
    this.state,
    this.postalCode,
    this.openStatus,
    this.openingTimes,
    this.rate,
    this.rateTotal,
    this.images,
    this.evses,
    this.uid,
  });

  // CRITICAL: Preserve exact UID from request to ensure data accuracy
  // This guarantees the station details correspond to the requested station
  void preserveUidFromRequest(String requestUid) {
    if ((uid == null || uid!.isEmpty) && requestUid.isNotEmpty) {
      uid = requestUid;
      debugPrint(
          'PRESERVED UID from request: $requestUid (API response had no UID)');
    } else if (uid != null && uid!.isNotEmpty && requestUid.isNotEmpty) {
      // Verify UID consistency - warn if they don't match
      if (uid != requestUid) {
        debugPrint(
            'WARNING: UID mismatch! API response UID: $uid, Request UID: $requestUid');
        debugPrint('Using request UID to ensure data accuracy: $requestUid');
        uid = requestUid; // Use request UID for consistency
      } else {
        debugPrint('UID consistency verified: $uid');
      }
    }
  }

  StationDetailsData.fromJson(Map<String, dynamic> json) {
    // Add debug logging for numeric fields
    debugPrint('StationDetailsData.fromJson: Processing JSON data');
    debugPrint(
        'Raw JSON data: ${json.toString().substring(0, math.min(json.toString().length, 500))}...');

    try {
      // Handle longitude with robust error handling
      if (json['longitude'] != null) {
        debugPrint('longitude type: ${json['longitude'].runtimeType}');
        try {
          if (json['longitude'] is num) {
            longitude = (json['longitude'] as num).toDouble();
          } else if (json['longitude'] is String) {
            longitude = double.tryParse(json['longitude'] as String);
          }
        } catch (e) {
          debugPrint('Error converting longitude: $e');
          longitude = null;
        }
      }

      // Handle latitude with robust error handling
      if (json['latitude'] != null) {
        debugPrint('latitude type: ${json['latitude'].runtimeType}');
        try {
          if (json['latitude'] is num) {
            latitude = (json['latitude'] as num).toDouble();
          } else if (json['latitude'] is String) {
            latitude = double.tryParse(json['latitude'] as String);
          }
        } catch (e) {
          debugPrint('Error converting latitude: $e');
          latitude = null;
        }
      }

      // Handle string fields with null checks
      name = json['name'] as String?;
      address = json['address'] as String?;
      city = json['city'] as String?;

      state = json['state'] as String?;
      postalCode = json['postal_code'] as String?;

      // Handle boolean fields with type checking
      if (json['open_status'] != null) {
        if (json['open_status'] is bool) {
          openStatus = json['open_status'] as bool;
        } else if (json['open_status'] is String) {
          openStatus = (json['open_status'] as String).toLowerCase() == 'true';
        } else if (json['open_status'] is num) {
          openStatus = (json['open_status'] as num) != 0;
        }
      }

      openingTimes = json['opening_times'] as String?;

      // Handle rate with robust error handling
      if (json['rate'] != null) {
        debugPrint('rate type: ${json['rate'].runtimeType}');
        try {
          if (json['rate'] is num) {
            rate = (json['rate'] as num).toDouble();
          } else if (json['rate'] is String) {
            rate = double.tryParse(json['rate'] as String);
          }
        } catch (e) {
          debugPrint('Error converting rate: $e');
          rate = null;
        }
      }

      // Handle rate_total with robust error handling
      if (json['rate_total'] != null) {
        debugPrint('rate_total type: ${json['rate_total'].runtimeType}');
        try {
          if (json['rate_total'] is int) {
            rateTotal = json['rate_total'] as int;
          } else if (json['rate_total'] is double) {
            rateTotal = (json['rate_total'] as double).toInt();
          } else if (json['rate_total'] is String) {
            rateTotal = int.tryParse(json['rate_total'] as String);
          }
        } catch (e) {
          debugPrint('Error converting rate_total: $e');
          rateTotal = null;
        }
      }

      // Handle images field
      images = json['images'] as String?;

      // CRITICAL: Only extract UID from actual UID fields, never from numeric IDs
      // This prevents false data where station details show wrong station info
      uid = json['uid'] as String? ?? json['station_uid'] as String?;
      debugPrint('Station UID from API: $uid');
      debugPrint(
          'Available UID fields in API response: uid=${json['uid']}, station_uid=${json['station_uid']}');

      // Log numeric fields separately to avoid confusion
      debugPrint(
          'Numeric fields (NOT UIDs): station_id=${json['station_id']}, id=${json['id']}');

      // Handle evses map with robust error handling
      if (json['evses'] != null && json['evses'] is Map) {
        evses = <String, EvseDetails>{};
        (json['evses'] as Map).forEach((key, value) {
          if (value is Map<String, dynamic>) {
            evses![key.toString()] = EvseDetails.fromJson(value);
          } else if (value is Map) {
            // Convert to Map<String, dynamic> if needed
            final Map<String, dynamic> convertedValue = {};
            value.forEach((k, v) => convertedValue[k.toString()] = v);
            evses![key.toString()] = EvseDetails.fromJson(convertedValue);
          }
        });

        debugPrint('Processed ${evses!.length} EVSEs from API response');
      } else {
        debugPrint('No EVSEs found in API response or invalid format');
        evses = <String, EvseDetails>{};
      }
    } catch (e) {
      debugPrint('Error parsing StationDetailsData: $e');
      // Don't rethrow - we want to return a partially populated object rather than fail completely
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['longitude'] = longitude;
    data['latitude'] = latitude;
    data['name'] = name;
    data['address'] = address;
    data['city'] = city;
    data['state'] = state;
    data['postal_code'] = postalCode;
    data['open_status'] = openStatus;
    data['opening_times'] = openingTimes;
    data['rate'] = rate;
    data['rate_total'] = rateTotal;
    data['images'] = images;
    data['uid'] = uid; // Only need one assignment

    if (evses != null) {
      final Map<String, dynamic> evsesMap = <String, dynamic>{};
      evses!.forEach((key, value) {
        evsesMap[key] = value.toJson();
      });
      data['evses'] = evsesMap;
    }

    return data;
  }
}

/// EVSE details model - COMPREHENSIVE EVSE DATA CAPTURE
class EvseDetails {
  // CRITICAL: EVSE operational data
  String? lastUsed; // Last usage timestamp for EVSE
  List<Connector>? connectors; // All connectors on this EVSE

  // CRITICAL: EVSE pricing information
  int? price; // Base price for this EVSE
  String? textPrice; // Formatted price text (e.g., "₹14")

  // CRITICAL: EVSE power capabilities
  String? powerOutput; // Power output type (e.g., "DCAC" for both DC and AC)
  int? maxPower; // Maximum power output (e.g., 67 kW)

  // CRITICAL: EVSE identification
  String? name; // EVSE name/identifier

  EvseDetails({
    this.lastUsed,
    this.connectors,
    this.price,
    this.textPrice,
    this.powerOutput,
    this.maxPower,
    this.name,
  });

  EvseDetails.fromJson(Map<String, dynamic> json) {
    debugPrint('EvseDetails.fromJson: Processing JSON data');

    lastUsed = json['last_used'];
    if (json['connectors'] != null) {
      connectors = <Connector>[];
      json['connectors'].forEach((v) {
        connectors!.add(Connector.fromJson(v));
      });
    }

    // Handle numeric fields that might come as either int or double
    if (json['price'] != null) {
      debugPrint('EvseDetails price type: ${json['price'].runtimeType}');
      try {
        price = json['price'] is int
            ? json['price']
            : (json['price'] as num).toInt();
      } catch (e) {
        debugPrint('Error converting price: $e');
        // Keep as null if conversion fails - no fallbacks
        price = null;
      }
    }

    textPrice = json['text_price'];
    powerOutput = json['power_output'];

    if (json['max_power'] != null) {
      debugPrint(
          'EvseDetails max_power type: ${json['max_power'].runtimeType}');
      try {
        maxPower = json['max_power'] is int
            ? json['max_power']
            : (json['max_power'] as num).toInt();
      } catch (e) {
        debugPrint('Error converting max_power: $e');
        // CRITICAL: No fallbacks - keep as null when conversion fails
        maxPower = null;
      }
    }

    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['last_used'] = lastUsed;
    if (connectors != null) {
      data['connectors'] = connectors!.map((v) => v.toJson()).toList();
    }
    data['price'] = price;
    data['text_price'] = textPrice;
    data['power_output'] = powerOutput;
    data['max_power'] = maxPower;
    data['name'] = name;
    return data;
  }
}

/// Connector model for station details - COMPREHENSIVE CONNECTOR DATA CAPTURE
class Connector {
  // CRITICAL: Charging session data
  int? soc; // State of Charge for active charging sessions (e.g., 75%)

  // CRITICAL: Connector identification and relationship
  String? evsesUid; // UID of the EVSE this connector belongs to
  String? connectorId; // Unique connector identifier

  // CRITICAL: Connector operational status
  String? status; // Real-time status (Available, Charging, Offline, etc.)

  // CRITICAL: Connector specifications
  String? label; // Connector display label
  String? type; // Connector type (CCS2, CHAdeMO, Type 2, etc.)
  String? standard; // Connector standard specification

  // CRITICAL: Power specifications
  dynamic
      maxElectricPower; // Maximum power output - PRESERVE EXACT API FORMAT (int, double, or string)
  String?
      powerOutput; // Power type from API 'power_type' field (e.g., "AC", "DC") - CONNECTOR-SPECIFIC

  // CRITICAL: Pricing information
  int? price; // Base price for this connector
  String? priceLabel; // Formatted price label (e.g., "₹14/kWh")
  int? pricePerUnit; // Price per unit of energy

  // CRITICAL: Visual identification
  String? icon; // Connector icon URL for visual identification

  Connector({
    this.soc,
    this.evsesUid,
    this.status,
    this.label,
    this.maxElectricPower,
    this.powerOutput,
    this.connectorId,
    this.standard,
    this.price,
    this.priceLabel,
    this.pricePerUnit,
    this.type,
    this.icon,
  });

  Connector.fromJson(Map<String, dynamic> json) {
    debugPrint('Connector.fromJson: Processing JSON data');
    debugPrint(
        'Raw connector JSON: ${json.toString().substring(0, math.min(json.toString().length, 300))}...');

    try {
      // Handle numeric fields that might come as either int or double
      if (json['soc'] != null) {
        debugPrint('Connector soc type: ${json['soc'].runtimeType}');
        try {
          if (json['soc'] is int) {
            soc = json['soc'] as int;
          } else if (json['soc'] is double) {
            soc = (json['soc'] as double).toInt();
          } else if (json['soc'] is String) {
            soc = int.tryParse(json['soc'] as String);
          }
        } catch (e) {
          debugPrint('Error converting soc: $e');
          soc = null;
        }
      }

      // Handle string fields with proper null checks
      evsesUid = json['evses_uid'] as String?;
      status = json['status'] as String?;
      label = json['label'] as String?;

      // Log status for debugging
      debugPrint('Connector status from API: $status');

      // Handle max_electric_power with robust error handling
      if (json['max_electric_power'] != null) {
        debugPrint(
            '🔍 CRITICAL DEBUG: Raw max_electric_power from API: ${json['max_electric_power']}');
        debugPrint(
            '🔍 CRITICAL DEBUG: max_electric_power type: ${json['max_electric_power'].runtimeType}');
        debugPrint(
            '🔍 CRITICAL DEBUG: max_electric_power value: ${json['max_electric_power']}');
        try {
          // PRESERVE EXACT API FORMAT - no conversion
          maxElectricPower = json['max_electric_power'];
          debugPrint(
              '✅ CRITICAL DEBUG: Preserved exact API format max_electric_power: $maxElectricPower (type: ${maxElectricPower.runtimeType})');
        } catch (e) {
          debugPrint(
              '❌ CRITICAL DEBUG: Error preserving max_electric_power: $e');
          maxElectricPower = null;
        }
      }

      // Handle connector_id with toString() for safety
      if (json['connector_id'] != null) {
        connectorId = json['connector_id'].toString();
      }

      standard = json['standard'] as String?;

      // Handle price with robust error handling
      if (json['price'] != null) {
        debugPrint('Connector price type: ${json['price'].runtimeType}');
        try {
          if (json['price'] is int) {
            price = json['price'] as int;
          } else if (json['price'] is double) {
            price = (json['price'] as double).toInt();
          } else if (json['price'] is String) {
            price = int.tryParse(json['price'] as String);
          }
        } catch (e) {
          debugPrint('Error converting price: $e');
          price = null;
        }
      }

      priceLabel = json['price_label'] as String?;

      // Handle price_per_unit with robust error handling
      if (json['price_per_unit'] != null) {
        debugPrint(
            'Connector price_per_unit type: ${json['price_per_unit'].runtimeType}');
        try {
          if (json['price_per_unit'] is int) {
            pricePerUnit = json['price_per_unit'] as int;
          } else if (json['price_per_unit'] is double) {
            pricePerUnit = (json['price_per_unit'] as double).toInt();
          } else if (json['price_per_unit'] is String) {
            pricePerUnit = int.tryParse(json['price_per_unit'] as String);
          }
        } catch (e) {
          debugPrint('Error converting price_per_unit: $e');
          pricePerUnit = null;
        }
      }

      type = json['type'] as String?;
      debugPrint('Connector type from API: $type');

      // Handle connector-specific power type (AC/DC) - CRITICAL: Use 'power_type' field from API
      powerOutput = json['power_type'] as String?;
      debugPrint('Connector powerOutput (power_type) from API: $powerOutput');

      // Handle icon URL with debug logging
      icon = json['icon'] as String?;
      if (icon != null && icon!.isNotEmpty) {
        debugPrint(
            'Found connector icon URL: $icon for connector type: ${type ?? "unknown"}');
      } else {
        debugPrint(
            'No icon URL found for connector type: ${type ?? "unknown"}');

        // Try to get icon from other fields if available
        if (json['image_url'] != null &&
            json['image_url'] is String &&
            (json['image_url'] as String).isNotEmpty) {
          icon = json['image_url'] as String;
          debugPrint('Using image_url as icon: $icon');
        } else if (json['icon_url'] != null &&
            json['icon_url'] is String &&
            (json['icon_url'] as String).isNotEmpty) {
          icon = json['icon_url'] as String;
          debugPrint('Using icon_url as icon: $icon');
        }
      }
    } catch (e) {
      debugPrint('Error parsing Connector: $e');
      // Don't rethrow - we want to return a partially populated object rather than fail completely
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['soc'] = soc;
    data['evses_uid'] = evsesUid;
    data['status'] = status;
    data['label'] = label;
    data['max_electric_power'] = maxElectricPower;
    data['power_type'] = powerOutput;
    data['connector_id'] = connectorId;
    data['standard'] = standard;
    data['price'] = price;
    data['price_label'] = priceLabel;
    data['price_per_unit'] = pricePerUnit;
    data['type'] = type;
    data['icon'] = icon;
    return data;
  }

  /// Dynamic power value formatter that preserves exact API format
  static String _formatPowerValueDynamic(
      dynamic powerValue, String? connectorId) {
    if (powerValue == null) {
      debugPrint(
          '❌ CRITICAL DEBUG: Power value is null in API response for connector $connectorId');
      return '';
    }

    // Check if the value is zero or negative (for numeric types)
    if ((powerValue is num && powerValue <= 0)) {
      debugPrint(
          '❌ CRITICAL DEBUG: Power value is zero or negative in API response for connector $connectorId: $powerValue');
      return '';
    }

    // Preserve exact API format - no conversion
    String formattedPower = '$powerValue kW';
    debugPrint(
        '✅ CRITICAL DEBUG: Preserved exact API format for connector $connectorId: $powerValue (${powerValue.runtimeType}) -> $formattedPower');

    // Log special cases for debugging
    if (powerValue == 59 || powerValue == 59.0) {
      debugPrint(
          '🚨 SPECIAL CHECK: API returned 59 value - displaying as "$formattedPower" preserving exact format');
    }
    if (powerValue == 59.5) {
      debugPrint(
          '🚨 DECIMAL CHECK: API returned 59.5 - displaying as "$formattedPower" preserving exact format');
    }

    return formattedPower;
  }

  // Convert to the main Connector model
  station_model.Connector toMainConnector() {
    // Debug log for icon URL conversion
    if (icon != null && icon!.isNotEmpty) {
      debugPrint(
          'Converting connector with icon URL: $icon to main connector model');
    } else {
      debugPrint(
          'Converting connector without icon URL to main connector model');

      // Get a real icon URL based on the connector type
      if (type != null && type!.isNotEmpty) {
        final normalizedType = type!.toLowerCase().replaceAll(' ', '');

        // Use the API connector icon URL based on connector type
        if (normalizedType.contains('ccs') ||
            normalizedType.contains('combo')) {
          icon = 'https://api2.eeil.online/uploads/connector_type/ccs2.svg';
        } else if (normalizedType.contains('type2')) {
          icon = 'https://api2.eeil.online/uploads/connector_type/type2.svg';
        } else if (normalizedType.contains('chademo')) {
          icon = 'https://api2.eeil.online/uploads/connector_type/chademo.svg';
        } else if (normalizedType.contains('gb/t') ||
            normalizedType.contains('gbt')) {
          icon = 'https://api2.eeil.online/uploads/connector_type/gbt.svg';
        } else if (normalizedType.contains('tesla')) {
          icon = 'https://api2.eeil.online/uploads/connector_type/tesla.svg';
        } else if (normalizedType.contains('type1')) {
          icon = 'https://api2.eeil.online/uploads/connector_type/type1.svg';
        } else {
          // CRITICAL: No default icon - only use real API data
          debugPrint('No icon URL provided by API for connector type: $type');
          // Leave icon as null to show "not specified" in UI
        }

        debugPrint(
            'Using API connector icon URL: $icon for connector type: $type');
      }
    }

    // Format price label if not provided but price is available
    String formattedPriceLabel = priceLabel ?? '';
    if ((formattedPriceLabel.isEmpty || formattedPriceLabel == 'null') &&
        price != null) {
      formattedPriceLabel = '₹${price!.toDouble()}';
    }

    // Log the power value for debugging
    debugPrint(
        '🔍 CRITICAL DEBUG: toMainConnector power value: maxElectricPower=$maxElectricPower');
    debugPrint(
        '🔍 CRITICAL DEBUG: toMainConnector power type: ${maxElectricPower.runtimeType}');

    // Format power string preserving exact API format
    String formattedPower =
        Connector._formatPowerValueDynamic(maxElectricPower, connectorId);

    // DELETED: No default status - use real API data only
    String formattedStatus = status ?? '';
    bool isAvailable = formattedStatus.toLowerCase() == 'available';

    debugPrint('Formatted connector data: price=${price?.toDouble()}, '
        'priceLabel=$formattedPriceLabel, power=$formattedPower, '
        'status=$formattedStatus, isAvailable=$isAvailable');

    // CRITICAL: Validate essential connector data before creating object
    if ((connectorId == null || connectorId?.isEmpty == true) &&
        (type == null || type?.isEmpty == true)) {
      throw FormatException('Connector missing essential identification data');
    }

    return station_model.Connector(
      id: connectorId ?? '',
      name: label ?? type ?? '',
      type: type ?? '',
      // CRITICAL: No default price - use 0.0 only when API provides null
      price: price?.toDouble() ?? 0.0,
      power: formattedPower,
      iconUrl: icon,
      totalGuns: 1,
      availableGuns: isAvailable ? 1 : 0,
      icon: icon,
      status: formattedStatus,
      maxElectricPower: maxElectricPower,
      standard: standard,
      priceLabel: formattedPriceLabel,
      powerOutput: powerOutput, // Pass connector-specific power output
      evsesUid: evsesUid,
    );
  }
}

/// Wallet model - COMPREHENSIVE WALLET DATA CAPTURE
class Wallet {
  // CRITICAL: Wallet identification
  int? id; // Wallet unique identifier
  int? userId; // Associated user ID

  // CRITICAL: Financial data
  double? balance; // Current wallet balance for charging payments
  int? exposure; // Wallet exposure/limit information

  // CRITICAL: Audit trail
  String? createdAt; // Wallet creation timestamp
  String? updatedAt; // Last wallet update timestamp

  Wallet({
    this.id,
    this.userId,
    this.balance,
    this.exposure,
    this.createdAt,
    this.updatedAt,
  });

  Wallet.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userId = json['user_id'];
    balance =
        json['balance'] != null ? (json['balance'] as num).toDouble() : null;
    exposure = json['exposure'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['user_id'] = userId;
    data['balance'] = balance;
    data['exposure'] = exposure;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    return data;
  }
}
