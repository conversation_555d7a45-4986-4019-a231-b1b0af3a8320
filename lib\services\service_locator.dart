import '../services/auth/auth_service.dart';
import '../services/sync_service.dart';
import '../services/token_service.dart';
import '../repositories/station_repository.dart';
import '../repositories/user_repository.dart';
import '../repositories/wallet_repository.dart';
import '../repositories/wallet_repository_impl.dart';
import '../core/api/api_service.dart'; // Use centralized API service
import '../services/connectivity_service.dart';
import '../services/station_data_cache.dart';
import '../services/wallet_service.dart';
import '../services/auth_manager.dart';
import '../services/charging_session_service.dart';

/// Service locator for dependency injection
/// Provides centralized access to services and repositories
class ServiceLocator {
  // Singleton pattern
  static final ServiceLocator _instance = ServiceLocator._internal();
  factory ServiceLocator() => _instance;

  // Services
  late final ApiService _apiService;
  late final SyncService _syncService;
  late final TokenService _tokenService;
  late final ConnectivityService _connectivityService;
  late final StationDataCache _stationDataCache;
  late final WalletService _walletService;
  late final AuthManager _authManager;
  late final ChargingSessionService _chargingSessionService;

  // Services
  late final AuthService _authService;
  late final StationRepository _stationRepository;
  late final UserRepository _userRepository;
  late final WalletRepository _walletRepository;
  late final WalletRepositoryImpl _walletRepositoryImpl;

  ServiceLocator._internal()
      : _apiService = ApiService(), // Use centralized API service
        _syncService = SyncService(),
        _tokenService = TokenService(),
        _connectivityService = ConnectivityService(),
        _stationDataCache = StationDataCache(),
        _walletService = WalletService(),
        _authManager = AuthManager(),
        _chargingSessionService = ChargingSessionService(),
        _authService = AuthService(),
        _stationRepository = StationRepository(),
        _userRepository = UserRepository(),
        _walletRepository = WalletRepository(),
        _walletRepositoryImpl = WalletRepositoryImpl(
            ApiService()); // Use centralized API service instance

  // Getters for services
  ApiService get apiService => _apiService;
  SyncService get syncService => _syncService;
  TokenService get tokenService => _tokenService;
  ConnectivityService get connectivityService => _connectivityService;
  StationDataCache get stationDataCache => _stationDataCache;
  WalletService get walletService => _walletService;
  AuthManager get authManager => _authManager;
  ChargingSessionService get chargingSessionService => _chargingSessionService;

  // Getters for services and repositories
  AuthService get authService => _authService;
  StationRepository get stationRepository => _stationRepository;
  UserRepository get userRepository => _userRepository;
  WalletRepository get walletRepository => _walletRepository;
  WalletRepositoryImpl get walletRepositoryImpl => _walletRepositoryImpl;
}
