import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:math';

import '../../utils/app_themes.dart';
import '../../models/api_response.dart';
import '../../models/wallet/wallet_response.dart';
import '../../services/service_locator.dart';
import '../Profile/FAQ/faq_page.dart';
import 'add_balance_sheet.dart';
import 'filter_transactions_sheet.dart';
import '../../services/auth_manager.dart';

/// Model for wallet data.
class WalletModel {
  double currentBalance;
  int rewardPoints;
  List<Transaction> transactions;
  DateTime lastUpdated;

  WalletModel({
    required this.currentBalance,
    required this.rewardPoints,
    required this.transactions,
    DateTime? lastUpdated,
  }) : lastUpdated = lastUpdated ?? DateTime.now();

  // Factory constructor to create WalletModel from API response.
  // Uncomment and modify when integrating with API.
  // factory WalletModel.fromJson(Map<String, dynamic> json) {
  //   return WalletModel(
  //     currentBalance: json['currentBalance'],
  //     rewardPoints: json['rewardPoints'],
  //     transactions: (json['transactions'] as List)
  //         .map((tx) => Transaction.fromJson(tx))
  //         .toList(),
  //   );
  // }
}

class Transaction {
  final String id;
  final String title;
  final DateTime dateTime;
  final double amount;

  Transaction({
    this.id = '',
    required this.title,
    required this.dateTime,
    required this.amount,
  });

  // Factory constructor to create Transaction from API response.
  // Uncomment and modify when integrating with API.
  // factory Transaction.fromJson(Map<String, dynamic> json) {
  //   return Transaction(
  //     id: json['id']?.toString() ?? '',
  //     title: json['title'],
  //     dateTime: DateTime.parse(json['dateTime']),
  //     amount: json['amount'],
  //   );
  // }
}

class WalletPage extends StatefulWidget {
  const WalletPage({super.key});

  @override
  State<WalletPage> createState() => _WalletPageState();
}

class _WalletPageState extends State<WalletPage> {
  late WalletModel _walletModel;
  late List<Transaction> _displayedTransactions;
  bool _isLoading = false;
  String? _errorMessage;
  WalletResponse? _walletResponse;

  // Auth manager for getting user data
  final AuthManager _authManager = AuthManager();

  @override
  void initState() {
    super.initState();

    // Initialize state variables
    _walletModel = WalletModel(
      currentBalance: 0,
      rewardPoints: 0,
      transactions: [],
    );
    _displayedTransactions = [];
    _retryAttempt = 0;
    _isRetrying = false;
    _retryTimer = null;

    // Schedule initialization after widget is mounted
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      if (!mounted) return;

      // Check if user is logged in
      final bool isLoggedIn = await _authManager.isLoggedIn();
      if (!isLoggedIn) {
        if (mounted) {
          Navigator.of(context).pushReplacementNamed('/auth');
          return;
        }
      }

      // Set initial loading state
      setState(() {
        _isLoading = true;
      });

      // Always fetch from API
      debugPrint('Fetching wallet data from API');
      _fetchWalletData();

      // Set up periodic refresh of wallet data
      Timer.periodic(const Duration(minutes: 5), (timer) {
        if (mounted) {
          // Only fetch if the wallet page is currently visible
          if (ModalRoute.of(context)?.isCurrent ?? false) {
            debugPrint('Periodic wallet data refresh');
            _fetchWalletData();
          }
        }
      });
    });
  }

  // Process wallet data from API
  void _processWalletData(WalletResponse? walletResponse) {
    if (walletResponse == null || !mounted) return;

    _walletResponse = walletResponse;

    // Convert API data to our WalletModel format
    final transactions = <Transaction>[];

    if (_walletResponse?.wallet?.paymentHistory != null) {
      for (var payment in _walletResponse!.wallet!.paymentHistory!) {
        // Skip transactions with null or 0 amount
        if (payment.amount == null || payment.amount == 0) continue;

        // Create a Transaction object from PaymentHistory
        transactions.add(Transaction(
          id: payment.id?.toString() ?? '',
          title: payment.remark ??
              (payment.isCredit ? 'Balance Added' : 'Payment'),
          dateTime: payment.date,
          amount: payment.isCredit ? payment.amount! : -payment.amount!,
        ));
      }
    }

    setState(() {
      _walletModel = WalletModel(
        currentBalance: _walletResponse?.wallet?.balance ?? 0.0,
        rewardPoints: 0, // API doesn't provide reward points
        transactions: transactions,
      );
      _displayedTransactions = List<Transaction>.from(_walletModel.transactions)
        ..sort((a, b) => b.dateTime.compareTo(a.dateTime));
      _isLoading = false;
      _errorMessage = null;

      // Reset retry counter on success
      _retryAttempt = 0;
      _isRetrying = false;
    });
  }

  // Track retry attempts to implement exponential backoff
  int _retryAttempt = 0;
  Timer? _retryTimer;
  bool _isRetrying = false;

  @override
  void dispose() {
    _retryTimer?.cancel();
    super.dispose();
  }

  // Fetch wallet data from the API with improved error handling
  Future<void> _fetchWalletData() async {
    // Only show loading if we don't have any data yet and not retrying
    if (_walletModel.transactions.isEmpty && !_isRetrying) {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });
    }

    try {
      debugPrint('Fetching wallet data from API...');

      // Get authentication token
      final String? token = await _authManager.getToken();
      if (token == null) {
        throw Exception('Authentication token not available');
      }

      // Make the API call with better timeout handling
      final walletInfoResponse =
          await ServiceLocator().walletRepository.getWalletInfo().timeout(
                const Duration(seconds: 30),
                onTimeout: () => ApiResponse.error('Request timed out'),
              );

      // Convert the WalletInfo response to WalletResponse format
      if (walletInfoResponse.success && walletInfoResponse.data != null) {
        final walletInfo = walletInfoResponse.data!;
        final walletResponse = WalletResponse(
          wallet: Wallet(
            balance: walletInfo.balance,
            paymentHistory: walletInfo.recentTransactions
                .map((tx) => PaymentHistory(
                      id: int.tryParse(tx.id),
                      amount: tx.amount,
                      remark: tx.title,
                      createdAt: tx.timestamp.toIso8601String(),
                      type: tx.type == 'credit' ? 'cr' : 'dr',
                      status: tx.status.toUpperCase(),
                      source: tx.transactionReference,
                    ))
                .toList(),
          ),
          paymentOption: 'wallet',
          success: true,
        );

        debugPrint('Successfully fetched wallet data from API');

        // Process and display the wallet data
        _processWalletData(walletResponse);
      } else {
        // Handle API error
        debugPrint('API error: ${walletInfoResponse.message}');
        throw Exception(walletInfoResponse.message);
      }
    } catch (e) {
      debugPrint('Error fetching wallet data: $e');

      // Check if widget is still mounted before updating state
      if (mounted) {
        // Show error message and attempt retry
        setState(() {
          _isLoading = false;

          // Provide more detailed error message based on the exception
          if (e.toString().contains('timeout')) {
            _errorMessage =
                'Connection timed out. Please check your internet connection and try again.';
          } else if (e.toString().contains('404')) {
            _errorMessage =
                'Wallet information not found. Please try again later.';
          } else if (e.toString().contains('401') ||
              e.toString().contains('403')) {
            _errorMessage = 'Authentication error. Please log in again.';

            // Navigate to login screen after a short delay
            Future.delayed(const Duration(seconds: 3), () {
              if (mounted) {
                Navigator.of(context).pushReplacementNamed('/auth');
              }
            });

            return; // Don't schedule retry for auth errors
          } else {
            _errorMessage = 'Failed to load wallet data: ${e.toString()}';
          }
        });

        // Schedule retry with exponential backoff
        _scheduleRetry();
      } else {
        // If widget is not mounted, cancel any pending retry
        _retryTimer?.cancel();
      }
    }
  }

  void _scheduleRetry() {
    if (_retryAttempt >= 5) {
      // Stop retrying after 5 attempts
      debugPrint('Maximum retry attempts reached for wallet data');
      _retryAttempt = 0;
      _isRetrying = false;
      return;
    }

    // Calculate backoff delay: 2^attempt * 1000ms (1s, 2s, 4s, 8s, 16s)
    final backoffDelay =
        Duration(milliseconds: (1000 * pow(2, _retryAttempt)).toInt());
    _isRetrying = true;

    debugPrint(
        'Scheduling wallet data retry in ${backoffDelay.inMilliseconds}ms (attempt ${_retryAttempt + 1})');

    // Cancel any existing timer
    _retryTimer?.cancel();

    // Start new timer with debug message
    _retryTimer = Timer(backoffDelay, () {
      if (mounted) {
        debugPrint('Retrying wallet data fetch (attempt ${_retryAttempt + 1})');
        _retryAttempt++;
        _fetchWalletData();
      }
    });
  }

  // Function to add funds using PayU integration
  void _handleAddFunds() async {
    // Show the add balance bottom sheet
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return AddBalanceSheet(
          onAddBalance: (amount, {String source = 'payu'}) async {
            // Close the bottom sheet immediately
            Navigator.pop(context);

            // Show loading indicator
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Row(
                  children: [
                    SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Text('Initiating payment via ${source.toUpperCase()}...'),
                  ],
                ),
                duration: const Duration(
                    seconds: 30), // Long duration as we'll dismiss it manually
              ),
            );

            // Initiate payment without waiting for the result
            // The payment flow will be handled by the payment gateway SDK and our stream listener
            _initiatePayment(amount, source: source);
          },
        );
      },
    );
  }

  // Initiate payment with the selected payment gateway
  Future<void> _initiatePayment(double amount, {required String source}) async {
    if (!mounted) return;

    try {
      setState(() {
        _isLoading = true;
      });

      debugPrint('Initiating $source payment for amount: $amount');

      // Payment methods are not currently supported
      throw Exception('Payment method $source not supported');

      // Payment is being handled by PayU SDK, and the result will come
      // through the PayU payment status listener in _setupPayUListener
    } catch (e) {
      debugPrint('Error initiating payment: $e');
      _showPaymentErrorDialog('Failed to initiate payment', {
        'status': 'error',
        'message': e.toString(),
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // Show detailed payment error dialog
  void _showPaymentErrorDialog(String message, Map<String, dynamic> status) {
    if (!mounted) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Payment Failed'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(message),
              const SizedBox(height: 16),
              const Text(
                'Error Details:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Text(
                status['data']?['error'] ?? 'No additional details available',
                style: const TextStyle(fontSize: 14),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _handleAddFunds(); // Try again
            },
            child: const Text('Try Again'),
          ),
        ],
      ),
    );
  }

  // Handle redeeming reward points
  void _handleRedeem() {
    if (_walletModel.rewardPoints <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('You have no reward points to redeem'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    // In a real implementation, this would call an API
    setState(() {
      final redeemValue =
          _walletModel.rewardPoints * 0.1; // Example conversion rate
      _walletModel.currentBalance += redeemValue;
      _walletModel.rewardPoints = 0;

      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
              'Successfully redeemed points for ₹${redeemValue.toStringAsFixed(2)}'),
          backgroundColor: Colors.green,
        ),
      );
    });
  }

  void _handleFilter() {
    // Get current filters if any are applied
    final currentCategory = _getCurrentCategory();
    final currentStatuses = _getCurrentStatuses();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (ctx) {
        return SizedBox(
          height: MediaQuery.of(ctx).size.height * 0.6,
          child: FilterTransactionsSheet(
            initialCategory: currentCategory,
            initialStatuses: currentStatuses,
            initialStartDate: null,
            initialEndDate: null,
            onApply: (selectedCategory, selectedStatuses, startDate, endDate) {
              try {
                setState(() {
                  _displayedTransactions =
                      _walletModel.transactions.where((tx) {
                    // Determine transaction category by title
                    final titleLower = tx.title.toLowerCase();
                    String txCategory;

                    if (titleLower.contains('recharge')) {
                      txCategory = 'recharge';
                    } else if (titleLower.contains('refund')) {
                      txCategory = 'refund';
                    } else {
                      txCategory = 'sessions';
                    }

                    // Check if category matches
                    bool categoryMatch = selectedCategory == Category.all ||
                        selectedCategory.toString().split('.').last ==
                            txCategory;

                    // Determine transaction status based on amount and title
                    String txStatus;
                    if (titleLower.contains('failed') ||
                        titleLower.contains('rejected')) {
                      txStatus = 'Rejected';
                    } else if (titleLower.contains('pending') ||
                        titleLower.contains('processing') ||
                        titleLower.contains('initiated')) {
                      txStatus = 'Pending';
                    } else {
                      txStatus = 'Complete';
                    }

                    // Check if status matches any selected status
                    bool statusMatch = selectedStatuses.contains(txStatus);

                    // Apply date filters if specified
                    bool dateMatch = true;
                    if (startDate != null) {
                      final start = DateTime(
                          startDate.year, startDate.month, startDate.day);
                      final txDate = DateTime(
                          tx.dateTime.year, tx.dateTime.month, tx.dateTime.day);
                      dateMatch = dateMatch &&
                          (txDate.isAtSameMomentAs(start) ||
                              txDate.isAfter(start));
                    }

                    if (endDate != null) {
                      final end = DateTime(
                          endDate.year, endDate.month, endDate.day, 23, 59, 59);
                      dateMatch = dateMatch && tx.dateTime.isBefore(end);
                    }

                    return categoryMatch && statusMatch && dateMatch;
                  }).toList();

                  // Sort transactions by date (newest first)
                  _displayedTransactions
                      .sort((a, b) => b.dateTime.compareTo(a.dateTime));
                });
              } catch (e) {
                // Show error message if filtering fails
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Error applying filters: ${e.toString()}'),
                    backgroundColor: Colors.red,
                  ),
                );

                // Reset to all transactions if there's an error
                setState(() {
                  _displayedTransactions =
                      List<Transaction>.from(_walletModel.transactions)
                        ..sort((a, b) => b.dateTime.compareTo(a.dateTime));
                });
              }
            },
          ),
        );
      },
    );
  }

  // Helper method to determine current category filter
  Category _getCurrentCategory() {
    // If no transactions are displayed, return all
    if (_displayedTransactions.isEmpty) {
      return Category.all;
    }

    // Check if all transactions are of the same category
    bool allRecharge = true;
    bool allRefund = true;
    bool allSessions = true;

    for (var tx in _displayedTransactions) {
      final titleLower = tx.title.toLowerCase();
      if (!titleLower.contains('recharge')) allRecharge = false;
      if (!titleLower.contains('refund')) allRefund = false;
      if (titleLower.contains('recharge') || titleLower.contains('refund')) {
        allSessions = false;
      }
    }

    if (allRecharge) return Category.recharge;
    if (allRefund) return Category.refund;
    if (allSessions) return Category.sessions;

    return Category.all;
  }

  // Helper method to determine current status filters
  Set<String> _getCurrentStatuses() {
    // Default to all statuses if no filtering is applied
    if (_displayedTransactions.length == _walletModel.transactions.length) {
      return {'Complete', 'Pending', 'Rejected'};
    }

    // Determine which statuses are currently included
    Set<String> statuses = {};

    for (var tx in _displayedTransactions) {
      final titleLower = tx.title.toLowerCase();

      if (titleLower.contains('failed') || titleLower.contains('rejected')) {
        statuses.add('Rejected');
      } else if (titleLower.contains('pending') ||
          titleLower.contains('processing') ||
          titleLower.contains('initiated')) {
        statuses.add('Pending');
      } else {
        statuses.add('Complete');
      }
    }

    return statuses.isEmpty ? {'Complete', 'Pending', 'Rejected'} : statuses;
  }

  @override
  Widget build(BuildContext context) {
    // Show modern loading animation if loading and no data yet
    if (_isLoading && _walletModel.transactions.isEmpty) {
      final isDarkMode = Theme.of(context).brightness == Brightness.dark;
      final primaryColor =
          isDarkMode ? AppThemes.primaryColor : const Color(0xFF4776E6);
      final secondaryColor =
          isDarkMode ? const Color(0xFF8E54E9) : const Color(0xFF8E54E9);

      return Scaffold(
        backgroundColor: isDarkMode ? AppThemes.darkBackground : Colors.white,
        appBar: AppBar(
          backgroundColor: isDarkMode ? AppThemes.darkBackground : Colors.white,
          elevation: 0,
          title: Text(
            'My Wallet',
            style: TextStyle(
              color: isDarkMode ? AppThemes.darkTextPrimary : Colors.black,
              fontSize: 22,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        body: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Animated wallet card placeholder
                TweenAnimationBuilder<double>(
                  tween: Tween<double>(begin: 0.8, end: 1.0),
                  duration: const Duration(milliseconds: 600),
                  curve: Curves.easeOutCubic,
                  builder: (context, value, child) {
                    return Transform.scale(
                      scale: value,
                      child: Container(
                        width: double.infinity,
                        height: 180,
                        margin: const EdgeInsets.only(top: 16),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              primaryColor.withAlpha(isDarkMode ? 180 : 220),
                              secondaryColor.withAlpha(isDarkMode ? 180 : 220),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(24),
                          boxShadow: [
                            BoxShadow(
                              color: secondaryColor.withAlpha(40),
                              blurRadius: 20,
                              offset: const Offset(0, 10),
                            ),
                          ],
                        ),
                        child: Stack(
                          children: [
                            // Animated shimmer effect
                            Positioned.fill(
                              child: TweenAnimationBuilder<double>(
                                tween: Tween<double>(begin: -1.0, end: 2.0),
                                duration: const Duration(milliseconds: 1500),
                                curve: Curves.easeInOut,
                                builder: (context, value, child) {
                                  return ShaderMask(
                                    shaderCallback: (rect) {
                                      return LinearGradient(
                                        begin: Alignment(value - 1, 0),
                                        end: Alignment(value, 0),
                                        colors: [
                                          Colors.white.withAlpha(0),
                                          Colors.white.withAlpha(50),
                                          Colors.white.withAlpha(100),
                                          Colors.white.withAlpha(50),
                                          Colors.white.withAlpha(0),
                                        ],
                                        stops: const [
                                          0.0,
                                          0.35,
                                          0.5,
                                          0.65,
                                          1.0
                                        ],
                                      ).createShader(rect);
                                    },
                                    child: Container(
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(24),
                                        color: Colors.white.withAlpha(0),
                                      ),
                                    ),
                                  );
                                },
                                onEnd: () {
                                  if (mounted) setState(() {});
                                },
                              ),
                            ),

                            // Content placeholders
                            Padding(
                              padding: const EdgeInsets.all(24.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Container(
                                        width: 120,
                                        height: 16,
                                        decoration: BoxDecoration(
                                          color: Colors.white.withAlpha(70),
                                          borderRadius:
                                              BorderRadius.circular(8),
                                        ),
                                      ),
                                      Container(
                                        width: 40,
                                        height: 40,
                                        decoration: BoxDecoration(
                                          color: Colors.white.withAlpha(50),
                                          shape: BoxShape.circle,
                                        ),
                                        child: Center(
                                          child: SizedBox(
                                            width: 20,
                                            height: 20,
                                            child: CircularProgressIndicator(
                                              strokeWidth: 2,
                                              valueColor:
                                                  AlwaysStoppedAnimation<Color>(
                                                Colors.white.withAlpha(200),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 24),
                                  Container(
                                    width: 180,
                                    height: 32,
                                    decoration: BoxDecoration(
                                      color: Colors.white.withAlpha(100),
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                  ),
                                  const Spacer(),
                                  Container(
                                    width: double.infinity,
                                    height: 48,
                                    decoration: BoxDecoration(
                                      color: Colors.white.withAlpha(80),
                                      borderRadius: BorderRadius.circular(16),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),

                const SizedBox(height: 32),

                // Transaction header placeholder
                TweenAnimationBuilder<double>(
                  tween: Tween<double>(begin: 0.0, end: 1.0),
                  duration: const Duration(milliseconds: 800),
                  curve: Curves.easeOutCubic,
                  builder: (context, value, child) {
                    return Opacity(
                      opacity: value,
                      child: Transform.translate(
                        offset: Offset(0, 20 * (1 - value)),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Container(
                              width: 150,
                              height: 24,
                              decoration: BoxDecoration(
                                color: isDarkMode
                                    ? Colors.grey.shade800
                                    : Colors.grey.shade300,
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            Container(
                              width: 80,
                              height: 36,
                              decoration: BoxDecoration(
                                color: isDarkMode
                                    ? Colors.grey.shade800
                                    : Colors.grey.shade200,
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),

                const SizedBox(height: 24),

                // Transaction list placeholders with staggered animation
                Expanded(
                  child: ListView.builder(
                    itemCount: 6,
                    itemBuilder: (context, index) {
                      return TweenAnimationBuilder<double>(
                        tween: Tween<double>(begin: 0.0, end: 1.0),
                        duration: Duration(milliseconds: 600 + (index * 100)),
                        curve: Curves.easeOutCubic,
                        builder: (context, value, child) {
                          return Opacity(
                            opacity: value,
                            child: Transform.translate(
                              offset: Offset(0, 20 * (1 - value)),
                              child: Container(
                                height: 80,
                                margin: const EdgeInsets.only(bottom: 12),
                                decoration: BoxDecoration(
                                  color: isDarkMode
                                      ? AppThemes.darkCard
                                      : Colors.white,
                                  borderRadius: BorderRadius.circular(16),
                                  boxShadow: isDarkMode
                                      ? null
                                      : [
                                          BoxShadow(
                                            color: Colors.black.withAlpha(8),
                                            blurRadius: 10,
                                            offset: const Offset(0, 4),
                                          ),
                                        ],
                                ),
                                child: Row(
                                  children: [
                                    const SizedBox(width: 16),
                                    // Animated shimmer for transaction icon
                                    TweenAnimationBuilder<double>(
                                      tween:
                                          Tween<double>(begin: 0.7, end: 0.9),
                                      duration:
                                          const Duration(milliseconds: 1000),
                                      curve: Curves.easeInOut,
                                      builder: (context, shimmerValue, _) {
                                        return Container(
                                          width: 48,
                                          height: 48,
                                          decoration: BoxDecoration(
                                            gradient: LinearGradient(
                                              begin: Alignment.topLeft,
                                              end: Alignment.bottomRight,
                                              colors: [
                                                isDarkMode
                                                    ? Colors.grey.shade800
                                                    : Colors.grey.shade200,
                                                isDarkMode
                                                    ? Colors.grey.shade700
                                                        .withAlpha(
                                                            (shimmerValue * 255)
                                                                .toInt())
                                                    : Colors.grey.shade300
                                                        .withAlpha(
                                                            (shimmerValue * 255)
                                                                .toInt()),
                                                isDarkMode
                                                    ? Colors.grey.shade800
                                                    : Colors.grey.shade200,
                                              ],
                                            ),
                                            shape: BoxShape.circle,
                                          ),
                                        );
                                      },
                                      onEnd: () {
                                        if (mounted) setState(() {});
                                      },
                                    ),
                                    const SizedBox(width: 16),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Container(
                                            width: 120 + (index * 20) % 60,
                                            height: 16,
                                            decoration: BoxDecoration(
                                              color: isDarkMode
                                                  ? Colors.grey.shade800
                                                  : Colors.grey.shade300,
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                            ),
                                          ),
                                          const SizedBox(height: 8),
                                          Container(
                                            width: 80,
                                            height: 12,
                                            decoration: BoxDecoration(
                                              color: isDarkMode
                                                  ? Colors.grey.shade800
                                                      .withAlpha(150)
                                                  : Colors.grey.shade200,
                                              borderRadius:
                                                  BorderRadius.circular(6),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Container(
                                      width: 70,
                                      height: 24,
                                      decoration: BoxDecoration(
                                        color: index % 2 == 0
                                            ? const Color(0xFF00C853)
                                                .withAlpha(isDarkMode ? 40 : 30)
                                            : const Color(0xFFFF3D00).withAlpha(
                                                isDarkMode ? 40 : 30),
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                    ),
                                    const SizedBox(width: 16),
                                  ],
                                ),
                              ),
                            ),
                          );
                        },
                      );
                    },
                  ),
                ),

                // Loading indicator at the bottom
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 24.0),
                  child: Center(
                    child: Column(
                      children: [
                        SizedBox(
                          width: 40,
                          height: 40,
                          child: Stack(
                            alignment: Alignment.center,
                            children: [
                              // Outer rotating circle
                              TweenAnimationBuilder<double>(
                                tween:
                                    Tween<double>(begin: 0, end: 2 * 3.14159),
                                duration: const Duration(milliseconds: 1500),
                                curve: Curves.easeInOutCubic,
                                builder: (context, value, child) {
                                  return Transform.rotate(
                                    angle: value,
                                    child: CircularProgressIndicator(
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                          primaryColor),
                                      strokeWidth: 3,
                                    ),
                                  );
                                },
                                onEnd: () {
                                  if (mounted) setState(() {});
                                },
                              ),

                              // Inner pulsing icon
                              TweenAnimationBuilder<double>(
                                tween: Tween<double>(begin: 0.8, end: 1.1),
                                duration: const Duration(milliseconds: 800),
                                curve: Curves.easeInOut,
                                builder: (context, value, child) {
                                  return Transform.scale(
                                    scale: value,
                                    child: Icon(
                                      Icons.account_balance_wallet,
                                      size: 18,
                                      color: primaryColor,
                                    ),
                                  );
                                },
                                onEnd: () {
                                  if (mounted) setState(() {});
                                },
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Loading your wallet...',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: isDarkMode
                                ? AppThemes.darkTextSecondary
                                : Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    // Show error message if there's an error and no data
    if (_errorMessage != null && _walletModel.transactions.isEmpty) {
      final isDarkMode = Theme.of(context).brightness == Brightness.dark;

      return Scaffold(
        backgroundColor: isDarkMode ? AppThemes.darkBackground : Colors.white,
        appBar: AppBar(
          backgroundColor: isDarkMode ? AppThemes.darkBackground : Colors.white,
          elevation: 0,
          title: Text(
            'My Wallet',
            style: TextStyle(
              color: isDarkMode ? AppThemes.darkTextPrimary : Colors.black,
              fontSize: 22,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                color: isDarkMode ? Colors.red.shade300 : Colors.red.shade700,
                size: 64,
              ),
              const SizedBox(height: 16),
              Text(
                'Error Loading Wallet',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: isDarkMode
                      ? AppThemes.darkTextPrimary
                      : Colors.grey.shade800,
                ),
              ),
              const SizedBox(height: 8),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 32),
                child: Text(
                  _errorMessage!,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 16,
                    color: isDarkMode
                        ? AppThemes.darkTextSecondary
                        : Colors.grey.shade600,
                  ),
                ),
              ),
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: () {
                  // Refresh wallet data
                  setState(() {
                    _isLoading = true;
                  });
                  Future.delayed(const Duration(seconds: 1), () {
                    if (mounted) {
                      setState(() {
                        _isLoading = false;
                      });
                    }
                  });
                },
                icon: const Icon(Icons.refresh_rounded, size: 18),
                label: const Text('Retry'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: isDarkMode
                      ? AppThemes.primaryColor
                      : const Color(0xFF4776E6),
                  foregroundColor: Colors.white,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }

    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: isDarkMode ? AppThemes.darkBackground : Colors.white,
        elevation: 0,
        title: Text(
          'My Wallet',
          style: TextStyle(
            color: isDarkMode ? AppThemes.darkTextPrimary : Colors.black,
            fontSize: 22,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: false,
        actions: [
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            margin: const EdgeInsets.only(right: 16),
            decoration: BoxDecoration(
              color: isDarkMode
                  ? AppThemes.secondaryColor.withAlpha(40)
                  : Colors.blue.withAlpha(26),
              borderRadius: BorderRadius.circular(20),
              border: isDarkMode
                  ? Border.all(
                      color: AppThemes.secondaryColor.withAlpha(80), width: 1)
                  : null,
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(20),
                onTap: () {
                  Navigator.push(
                    context,
                    PageRouteBuilder(
                      pageBuilder: (context, animation, secondaryAnimation) =>
                          const FAQPage(),
                      transitionsBuilder: (
                        context,
                        animation,
                        secondaryAnimation,
                        child,
                      ) {
                        return FadeTransition(
                          opacity: animation,
                          child: SlideTransition(
                            position: Tween<Offset>(
                              begin: const Offset(0.05, 0),
                              end: Offset.zero,
                            ).animate(animation),
                            child: child,
                          ),
                        );
                      },
                    ),
                  );
                },
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.question_answer_outlined,
                        color:
                            isDarkMode ? AppThemes.secondaryColor : Colors.blue,
                        size: 18,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'FAQ',
                        style: TextStyle(
                          color: isDarkMode
                              ? AppThemes.secondaryColor
                              : Colors.blue,
                          fontWeight: FontWeight.w600,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
      backgroundColor:
          isDarkMode ? AppThemes.darkBackground : const Color(0xFFF8F9FA),
      // Removed floating action button as per requirements
      body: SafeArea(
        child: RefreshIndicator(
          onRefresh: () async {
            // Simulate refresh
            await Future.delayed(const Duration(seconds: 1));
            if (mounted) {
              setState(() {
                // Refresh UI if needed
              });
            }
            return;
          },
          color: const Color(0xFF4776E6),
          child: SingleChildScrollView(
            physics: const BouncingScrollPhysics(),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Balance card with animation
                TweenAnimationBuilder<double>(
                  tween: Tween<double>(begin: 0.8, end: 1.0),
                  duration: const Duration(milliseconds: 600),
                  curve: Curves.easeOutCubic,
                  builder: (context, value, child) {
                    return Transform.scale(
                      scale: value,
                      child: _buildBalanceCard(),
                    );
                  },
                ),

                const SizedBox(height: 16),

                // Transaction header with animation
                TweenAnimationBuilder<double>(
                  tween: Tween<double>(begin: 0.0, end: 1.0),
                  duration: const Duration(milliseconds: 800),
                  curve: Curves.easeOutCubic,
                  builder: (context, value, child) {
                    return Opacity(
                      opacity: value,
                      child: Transform.translate(
                        offset: Offset(0, 20 * (1 - value)),
                        child: child,
                      ),
                    );
                  },
                  child: _buildTransactionHeader(),
                ),

                const SizedBox(height: 8),

                // Transaction list with staggered animation
                ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: _displayedTransactions.length,
                  itemBuilder: (context, index) {
                    // Staggered animation delay based on index
                    final delay = 300 + (index * 100);

                    return TweenAnimationBuilder<double>(
                      tween: Tween<double>(begin: 0.0, end: 1.0),
                      duration: Duration(milliseconds: 600 + delay),
                      curve: Curves.easeOutCubic,
                      builder: (context, value, child) {
                        return Opacity(
                          opacity: value,
                          child: Transform.translate(
                            offset: Offset(0, 20 * (1 - value)),
                            child: child,
                          ),
                        );
                      },
                      child: _buildTransactionItem(
                          _displayedTransactions[index], isDarkMode),
                    );
                  },
                ),

                const SizedBox(height: 24),

                // Rewards card with animation
                TweenAnimationBuilder<double>(
                  tween: Tween<double>(begin: 0.0, end: 1.0),
                  duration: const Duration(milliseconds: 1000),
                  curve: Curves.easeOutCubic,
                  builder: (context, value, child) {
                    return Opacity(
                      opacity: value,
                      child: Transform.translate(
                        offset: Offset(0, 30 * (1 - value)),
                        child: child,
                      ),
                    );
                  },
                  child: _buildRewardsCard(),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBalanceCard() {
    return Container(
      width: double.infinity,
      constraints: const BoxConstraints(maxWidth: 500),
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF4776E6), Color(0xFF8E54E9)],
        ),
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF8E54E9).withAlpha(77),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Current Balance',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Container(
                decoration: BoxDecoration(
                  color: Colors.white.withAlpha(51),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: IconButton(
                  icon: const Icon(Icons.refresh_rounded, color: Colors.white),
                  onPressed: () {
                    // Refresh wallet data from API (force refresh)
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Refreshing wallet data...'),
                        duration: Duration(seconds: 1),
                      ),
                    );

                    // Simulate refresh
                    setState(() {
                      // Refresh UI if needed
                    });
                  },
                  tooltip: 'Refresh Balance',
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          TweenAnimationBuilder<double>(
            tween: Tween<double>(begin: 0.5, end: 1.0),
            duration: const Duration(milliseconds: 800),
            curve: Curves.elasticOut,
            builder: (context, value, child) {
              return Transform.scale(
                scale: value,
                alignment: Alignment.centerLeft,
                child: child,
              );
            },
            child: Text(
              '₹${_walletModel.currentBalance.toStringAsFixed(2)}',
              style: const TextStyle(
                fontSize: 38,
                fontWeight: FontWeight.bold,
                color: Colors.white,
                letterSpacing: -0.5,
              ),
            ),
          ),
          const SizedBox(height: 24),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _handleAddFunds,
              icon: const Icon(Icons.add_rounded),
              label: const Text(
                'Add Balance',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.white,
                foregroundColor: const Color(0xFF8E54E9),
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionHeader() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Padding(
      padding: const EdgeInsets.only(right: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'Transaction History',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w700,
              letterSpacing: -0.5,
              color: isDarkMode ? AppThemes.darkTextPrimary : Colors.black87,
            ),
          ),
          Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(12),
              onTap: _handleFilter,
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                decoration: BoxDecoration(
                  color: isDarkMode ? AppThemes.darkCard : Colors.grey.shade200,
                  borderRadius: BorderRadius.circular(10),
                  border: isDarkMode
                      ? Border.all(color: AppThemes.darkBorder)
                      : null,
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.filter_list_rounded,
                        size: 16,
                        color: isDarkMode
                            ? AppThemes.primaryColor
                            : Colors.black87),
                    const SizedBox(width: 4),
                    Text('Filter',
                        style: TextStyle(
                            fontSize: 13,
                            fontWeight: FontWeight.w600,
                            color: isDarkMode
                                ? AppThemes.darkTextPrimary
                                : Colors.black87)),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionItem(Transaction tx, bool isDarkMode) {
    final isCredit = tx.amount >= 0;
    final formattedDate = _formatDateTime(tx.dateTime);

    // Determine icon based on transaction type
    IconData transactionIcon;
    Color iconBgColor;
    Color iconColor;

    final titleLower = tx.title.toLowerCase();

    // Payment/Charging category
    if (titleLower.contains('charging') ||
        titleLower.contains('payment') ||
        titleLower.contains('ecoplug') ||
        titleLower.contains('charger')) {
      transactionIcon = Icons.bolt;
      iconBgColor = const Color(0xFF4CAF50).withAlpha(26);
      iconColor = const Color(0xFF4CAF50);
    }
    // Wallet/Balance category
    else if (titleLower.contains('balance added') ||
        titleLower.contains('recharge') ||
        titleLower.contains('wallet') ||
        titleLower.contains('added')) {
      transactionIcon = Icons.add_card;
      iconBgColor = const Color(0xFF4776E6).withAlpha(26);
      iconColor = const Color(0xFF4776E6);
    }
    // Refund category
    else if (titleLower.contains('refund')) {
      transactionIcon = Icons.assignment_return;
      iconBgColor = const Color(0xFF00C853).withAlpha(26);
      iconColor = const Color(0xFF00C853);
    }
    // Failed payment category
    else if (titleLower.contains('failed') ||
        titleLower.contains('rejected') ||
        titleLower.contains('cancelled')) {
      transactionIcon = Icons.cancel;
      iconBgColor = const Color(0xFFFF3D00).withAlpha(26);
      iconColor = const Color(0xFFFF3D00);
    }
    // Cashback/Reward category
    else if (titleLower.contains('cashback') ||
        titleLower.contains('reward') ||
        titleLower.contains('bonus')) {
      transactionIcon = Icons.redeem;
      iconBgColor = const Color(0xFFE040FB).withAlpha(26);
      iconColor = const Color(0xFFE040FB);
    }
    // Default category based on credit/debit
    else {
      if (isCredit) {
        transactionIcon = Icons.arrow_downward;
        iconBgColor = const Color(0xFF4776E6).withAlpha(26);
        iconColor = const Color(0xFF4776E6);
      } else {
        transactionIcon = Icons.arrow_upward;
        iconBgColor = const Color(0xFFFF9800).withAlpha(26);
        iconColor = const Color(0xFFFF9800);
      }
    }

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 6),
      decoration: BoxDecoration(
        color: isDarkMode ? AppThemes.darkCard : Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: isDarkMode ? Border.all(color: AppThemes.darkBorder) : null,
        boxShadow: isDarkMode
            ? null
            : [
                BoxShadow(
                  color: Colors.black.withAlpha(8),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () {
            // Show transaction details
            showModalBottomSheet(
              context: context,
              isScrollControlled: true,
              backgroundColor: Colors.transparent,
              builder: (context) {
                return Container(
                  height: MediaQuery.of(context).size.height * 0.6,
                  decoration: BoxDecoration(
                    color: isDarkMode ? AppThemes.darkSurface : Colors.white,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(20),
                      topRight: Radius.circular(20),
                    ),
                    border: isDarkMode
                        ? Border(
                            top: BorderSide(
                                color: AppThemes.darkBorder, width: 1))
                        : null,
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(24),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text(
                              'Transaction Details',
                              style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            IconButton(
                              icon: const Icon(Icons.close),
                              onPressed: () => Navigator.pop(context),
                            ),
                          ],
                        ),
                        const SizedBox(height: 24),
                        Center(
                          child: Container(
                            width: 64,
                            height: 64,
                            decoration: BoxDecoration(
                              color: iconBgColor,
                              shape: BoxShape.circle,
                            ),
                            child: Icon(
                              transactionIcon,
                              color: iconColor,
                              size: 32,
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),
                        Center(
                          child: Text(
                            '${isCredit ? '+ ' : '- '}₹${tx.amount.abs().toStringAsFixed(2)}',
                            style: TextStyle(
                              fontSize: 28,
                              fontWeight: FontWeight.bold,
                              // Use green for credits, red for debits, regardless of failed status
                              color: isCredit
                                  ? const Color(0xFF00C853)
                                  : const Color(0xFFFF3D00),
                            ),
                          ),
                        ),
                        Center(
                          child: Container(
                            margin: const EdgeInsets.symmetric(vertical: 8),
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: titleLower.contains('failed')
                                  ? const Color(0xFFFF3D00).withAlpha(26)
                                  : (isCredit
                                      ? const Color(0xFF00C853).withAlpha(26)
                                      : const Color(0xFFFF3D00).withAlpha(26)),
                              borderRadius: BorderRadius.circular(16),
                            ),
                            child: Text(
                              titleLower.contains('failed')
                                  ? 'Payment Failed'
                                  : (isCredit ? 'Credit' : 'Debit'),
                              style: TextStyle(
                                color: titleLower.contains('failed')
                                    ? const Color(0xFFFF3D00)
                                    : (isCredit
                                        ? const Color(0xFF00C853)
                                        : const Color(0xFFFF3D00)),
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(height: 24),
                        const Divider(),
                        const SizedBox(height: 16),
                        _buildDetailRow('Transaction', tx.title),
                        _buildDetailRow('Date & Time', formattedDate),
                        _buildDetailRow(
                            'Status',
                            titleLower.contains('failed')
                                ? 'Payment Failed'
                                : 'Completed'),
                      ],
                    ),
                  ),
                );
              },
            );
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Row(
              children: [
                // Transaction icon
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: iconBgColor,
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: iconColor.withAlpha(50),
                      width: 1.5,
                    ),
                  ),
                  child: Icon(
                    transactionIcon,
                    color: iconColor,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                // Transaction details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        tx.title,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: isDarkMode
                              ? AppThemes.darkTextPrimary
                              : Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        formattedDate,
                        style: TextStyle(
                          color: isDarkMode
                              ? AppThemes.darkTextSecondary
                              : Colors.grey.shade600,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                // Amount
                Text(
                  '${isCredit ? '+ ' : '- '}₹${tx.amount.abs().toStringAsFixed(2)}',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w700,
                    // Use green for credits, red for debits, regardless of failed status
                    color: isCredit
                        ? const Color(0xFF00C853)
                        : const Color(0xFFFF3D00),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              color: Colors.grey.shade600,
              fontSize: 14,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDateTime(DateTime dt) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = DateTime(now.year, now.month, now.day - 1);
    final dateToCheck = DateTime(dt.year, dt.month, dt.day);

    if (dateToCheck == today) {
      return 'Today, ${dt.hour}:${dt.minute.toString().padLeft(2, '0')}';
    } else if (dateToCheck == yesterday) {
      return 'Yesterday, ${dt.hour}:${dt.minute.toString().padLeft(2, '0')}';
    } else {
      return '${dt.day}/${dt.month}/${dt.year}';
    }
  }

  Widget _buildRewardsCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFFFF8F00), Color(0xFFFF5722)],
        ),
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFFFF5722).withAlpha(77),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.star_rounded, color: Colors.white, size: 28),
              const SizedBox(width: 12),
              const Text(
                'Reward Points',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          TweenAnimationBuilder<double>(
            tween: Tween<double>(begin: 0.5, end: 1.0),
            duration: const Duration(milliseconds: 800),
            curve: Curves.elasticOut,
            builder: (context, value, child) {
              return Transform.scale(
                scale: value,
                alignment: Alignment.centerLeft,
                child: child,
              );
            },
            child: Text(
              '${_walletModel.rewardPoints} points',
              style: const TextStyle(
                fontSize: 34,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
          const SizedBox(height: 20),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _handleRedeem,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.white,
                foregroundColor: const Color(0xFFFF5722),
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: const Text(
                'Redeem Points',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
