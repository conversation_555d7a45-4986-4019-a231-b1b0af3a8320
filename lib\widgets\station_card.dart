import 'package:flutter/material.dart';
import 'package:ecoplug/utils/app_themes.dart';
import 'package:ecoplug/models/station.dart';

/// Dynamic Station Card Widget that displays REAL API data
/// NO MORE HARDCODED VALUES - All data comes from Station model
class StationCard extends StatelessWidget {
  final Station station;
  final VoidCallback? onTap;
  final double? distance; // Optional distance override

  const StationCard({
    super.key,
    required this.station,
    this.onTap,
    this.distance,
  });

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // Calculate real availability from API data
    final totalConnectors = station.connectors.length;
    final availableConnectors = station.connectors
        .where((connector) =>
            connector.status?.toLowerCase() == 'available' ||
            connector.status?.toLowerCase() == 'online' ||
            connector.status?.toLowerCase() == 'operational')
        .length;

    // Use real distance from station model or provided override
    final displayDistance = distance ?? station.distance;
    final distanceText = displayDistance > 0
        ? '${displayDistance.toStringAsFixed(1)} km'
        : 'Distance unknown';

    // Create availability text from real API data
    final availabilityText = totalConnectors > 0
        ? '$availableConnectors/$totalConnectors Connectors Available'
        : 'No connector data';

    // Determine station status color based on real API data
    Color statusColor;
    if (station.status.toLowerCase().contains('open') ||
        station.status.toLowerCase().contains('available')) {
      statusColor = Colors.green;
    } else if (station.status.toLowerCase().contains('closed') ||
        station.status.toLowerCase().contains('unavailable')) {
      statusColor = Colors.red;
    } else {
      statusColor = Colors.orange; // Unknown status
    }

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        // Add border for dark mode consistency
        side: isDarkMode
            ? BorderSide(color: AppThemes.darkBorder, width: 1)
            : BorderSide.none,
      ),
      color: Theme.of(context)
          .colorScheme
          .surface, // Use theme-adaptive surface color
      elevation: isDarkMode
          ? 0
          : 2, // No elevation in dark mode, subtle elevation in light mode
      child: InkWell(
        borderRadius: BorderRadius.circular(16),
        onTap: onTap ??
            () {
              // Default navigation if no custom onTap provided
              Navigator.of(context).pushNamed('/stationDetails', arguments: {
                'uid': station.uid,
                'station': station,
              });
            },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // Station icon with status indicator
              Stack(
                children: [
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary.withValues(
                          alpha:
                              0.1), // Use theme primary color with low opacity
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      Icons.ev_station,
                      color: Theme.of(context)
                          .colorScheme
                          .primary, // Use theme primary color
                      size: 28,
                    ),
                  ),
                  // Status indicator dot
                  Positioned(
                    right: 4,
                    top: 4,
                    child: Container(
                      width: 12,
                      height: 12,
                      decoration: BoxDecoration(
                        color: statusColor,
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: Theme.of(context)
                              .colorScheme
                              .surface, // Use theme-adaptive surface color for border
                          width: 2,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Real station name from API
                    Text(
                      station.name.isNotEmpty
                          ? station.name
                          : 'Station Name Not Available',
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                        color: Theme.of(context)
                            .colorScheme
                            .onSurface, // Use theme-adaptive text color
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    // Real distance and availability from API
                    Text(
                      '$distanceText · $availabilityText',
                      style: TextStyle(
                        fontSize: 14,
                        color: Theme.of(context).colorScheme.onSurface.withValues(
                            alpha:
                                0.7), // Use theme-adaptive secondary text color
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    // Real station status from API
                    Row(
                      children: [
                        Icon(
                          Icons.circle,
                          size: 8,
                          color: statusColor,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          station.status.isNotEmpty
                              ? station.status
                              : 'Status Unknown',
                          style: TextStyle(
                            fontSize: 12,
                            color: statusColor,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        if (station.rating > 0) ...[
                          const SizedBox(width: 8),
                          Icon(
                            Icons.star,
                            size: 12,
                            color: Colors.amber,
                          ),
                          const SizedBox(width: 2),
                          Text(
                            station.rating.toStringAsFixed(1),
                            style: TextStyle(
                              fontSize: 12,
                              color: Theme.of(context)
                                  .colorScheme
                                  .onSurface
                                  .withValues(
                                      alpha:
                                          0.6), // Use theme-adaptive tertiary text color
                            ),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.chevron_right,
                color: Theme.of(context)
                    .colorScheme
                    .onSurface
                    .withValues(alpha: 0.5), // Use theme-adaptive icon color
              ),
            ],
          ),
        ),
      ),
    );
  }
}
