class WalletResponse {
  Wallet? wallet;
  String? paymentOption;
  OfferMessage? offerMessage;
  bool? success;

  WalletResponse(
      {this.wallet, this.paymentOption, this.offerMessage, this.success});

  WalletResponse.fromJson(Map<String, dynamic> json) {
    wallet = json['wallet'] != null ? Wallet.fromJson(json['wallet']) : null;
    paymentOption = json['payment_option'];
    offerMessage = json['offer_message'] != null
        ? OfferMessage.fromJson(json['offer_message'])
        : null;
    success = json['success'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (wallet != null) {
      data['wallet'] = wallet!.toJson();
    }
    data['payment_option'] = paymentOption;
    if (offerMessage != null) {
      data['offer_message'] = offerMessage!.toJson();
    }
    data['success'] = success;
    return data;
  }
}

class Wallet {
  double? balance;
  int? userId;
  List<PaymentHistory>? paymentHistory;

  Wallet({this.balance, this.userId, this.paymentHistory});

  Wallet.fromJson(Map<String, dynamic> json) {
    balance = json['balance'] is int
        ? (json['balance'] as int).toDouble()
        : json['balance'];
    userId = json['user_id'];
    if (json['payment_history'] != null) {
      paymentHistory = <PaymentHistory>[];
      json['payment_history'].forEach((v) {
        paymentHistory!.add(PaymentHistory.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['balance'] = balance;
    data['user_id'] = userId;
    if (paymentHistory != null) {
      data['payment_history'] = paymentHistory!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class PaymentHistory {
  int? id;
  double? amount;
  String? status;
  String? type;
  String? remark;
  String? createdAt;
  String? source;

  PaymentHistory(
      {this.id,
      this.amount,
      this.status,
      this.type,
      this.remark,
      this.createdAt,
      this.source});

  PaymentHistory.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    // Handle amount that could be int, double, or null
    if (json['amount'] is int) {
      amount = (json['amount'] as int).toDouble();
    } else if (json['amount'] is double) {
      amount = json['amount'];
    } else {
      amount = 0.0;
    }
    status = json['status'];
    type = json['type'];
    remark = json['remark'];
    createdAt = json['created_at'];
    source = json['source'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['amount'] = amount;
    data['status'] = status;
    data['type'] = type;
    data['remark'] = remark;
    data['created_at'] = createdAt;
    data['source'] = source;
    return data;
  }

  // Get formatted date
  DateTime get date {
    try {
      return DateTime.parse(createdAt ?? '');
    } catch (e) {
      return DateTime.now();
    }
  }

  // Check if transaction is credit
  bool get isCredit => type == 'cr';

  // Check if transaction is completed
  bool get isCompleted => status == 'COMPLETED';

  // Get formatted amount with sign
  String getFormattedAmount() {
    if (isCredit) {
      return '+₹${amount?.toStringAsFixed(2) ?? '0.00'}';
    } else {
      return '-₹${amount?.toStringAsFixed(2) ?? '0.00'}';
    }
  }

  // Get color based on transaction type
  int getColorValue() {
    if (!isCompleted) {
      return 0xFFFF9800; // Orange for pending/rejected
    }
    return isCredit
        ? 0xFF4CAF50
        : 0xFFE53935; // Green for credit, Red for debit
  }
}

class OfferMessage {
  String? title;
  String? message;
  bool? status;

  OfferMessage({this.title, this.message, this.status});

  OfferMessage.fromJson(Map<String, dynamic> json) {
    title = json['title'];
    message = json['message'];
    status = json['status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['title'] = title;
    data['message'] = message;
    data['status'] = status;
    return data;
  }
}
