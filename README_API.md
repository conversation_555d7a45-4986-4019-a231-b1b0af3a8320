# EcoPlug API Integration

This document explains how to use the API structure in the EcoPlug application.

## Overview

The API structure is designed to work with both local dummy data and real API calls, making it easy to switch between development and production environments without changing any UI/UX elements.

## Key Components

1. **Models**: Data models for all API responses (`lib/models/`)
2. **Repositories**: Handle business logic and data transformation (`lib/repositories/`)
3. **API Service**: Handles all HTTP requests (`lib/services/api/api_service.dart`)
4. **API Config**: Contains all API endpoints (`lib/config/api_config.dart`)
5. **Service Locator**: Provides easy access to services and repositories (`lib/services/service_locator.dart`)

## How to Use

### 1. Access repositories through the service locator

```dart
final authRepository = ServiceLocator().authRepository;
final stationRepository = ServiceLocator().stationRepository;
final walletRepository = ServiceLocator().walletRepository;
final userRepository = ServiceLocator().userRepository;
```

### 2. Call repository methods

```dart
// Authentication
final loginResponse = await authRepository.login(phoneNumber);

// Stations
final markers = await stationRepository.getMarkers();

// Wallet
final walletInfo = await walletRepository.getWalletInfo();

// User
final userProfile = await userRepository.getUserProfile();
```

### 3. Handle responses

```dart
if (response.success && response.data != null) {
  // Handle success case
  final data = response.data;
} else {
  // Handle error case
  final errorMessage = response.message;
}
```

## Switching to Real API

To switch from mock data to real API:

1. Update the API configuration in `lib/config/api_config.dart`:
   ```dart
   static const String baseUrl = 'https://your-api-url.com/api';
   static const String apiKey = 'YOUR_API_KEY';
   static const String apiSecret = 'YOUR_API_SECRET';
   ```

2. Set development mode to false in `lib/services/api/api_service.dart`:
   ```dart
   bool get isDevelopmentMode => false;
   ```

That's it! The application will now use your real API instead of mock data, with no changes to the UI/UX.

## Example

See `lib/examples/api_usage_example.dart` for examples of how to use the API structure in your application.

## Testing

For testing, you can use the mock data by keeping `isDevelopmentMode` set to `true`.

## Security

The API structure includes token management and secure storage of credentials using `shared_preferences`.

For more detailed information, see the full API Integration Guide in `API_INTEGRATION_GUIDE.md`.
