import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:ecoplug/screens/dashboard/google_map_widget.dart'; // Import the GoogleMapWidget
import 'package:ecoplug/models/station.dart';
import 'package:ecoplug/services/station_service.dart';
import 'package:ecoplug/screens/station/station_details_page_fixed.dart';
import 'package:ecoplug/utils/app_theme.dart';
import 'package:ecoplug/core/services/connectivity_service.dart';
import 'package:ecoplug/repositories/station_repository.dart';
import 'package:ecoplug/core/api/api_service.dart';
import 'package:ecoplug/widgets/station_card.dart';

import '../../utils/app_themes.dart';
// Animations may be used in the future

class TripPage extends StatefulWidget {
  const TripPage({super.key});

  @override
  TripPageState createState() => TripPageState();
}

// In the TripPageState class, add:
class TripPageState extends State<TripPage> with TickerProviderStateMixin {
  // Controller for the Google Map
  GoogleMapController? _mapController;

  // Controller for the draggable scrollable sheet
  final DraggableScrollableController _draggableScrollableController =
      DraggableScrollableController();

  // Flag to determine whether to use the MapWidget or GoogleMap
  final bool useMapWidget = true; // Set to true to use your MapWidget

  // Initial camera position set to a placeholder location
  final CameraPosition _initialCameraPosition = const CameraPosition(
    target: LatLng(
      28.6139,
      77.2090,
    ), // Example: New Delhi (replace with current location)
    zoom: 12,
  );

  // Add StationService and related properties
  final StationService _stationService = StationService(
    ConnectivityService(),
    StationRepository(),
    ApiService(),
  );
  List<Station> _stationsAlongRoute = [];
  bool _isLoadingStations = false;

  // Example markers for charging stations - NO INFO WINDOWS TO PREVENT POPUPS
  final Set<Marker> _stationMarkers = {
    Marker(
      markerId: MarkerId('station1'),
      position: LatLng(28.6200, 77.2100),
      // REMOVED: InfoWindow to prevent annoying popups
    ),
    Marker(
      markerId: MarkerId('station2'),
      position: LatLng(28.6250, 77.2150),
      // REMOVED: InfoWindow to prevent annoying popups
    ),
    Marker(
      markerId: MarkerId('station3'),
      position: LatLng(28.6300, 77.2200),
      // REMOVED: InfoWindow to prevent annoying popups
    ),
  };

  // Booleans to track bottom sheet state and navigation mode
  bool _isSheetExpanded = false;
  bool _isNavigating = false;

  // TextEditingControllers for current location and destination
  final TextEditingController _currentLocationController =
      TextEditingController(text: 'Current Location');
  final TextEditingController _destinationController = TextEditingController();

  // Animation controllers
  late AnimationController _mapControlsAnimationController;
  late AnimationController _stationListAnimationController;
  late AnimationController _animationController;
  late AnimationController _searchBarAnimationController;
  late Animation<double> _searchBarAnimation;

  @override
  void initState() {
    super.initState();
    _loadStationsAlongRoute(); // Add this method call

    // Initialize animation controllers
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    _mapControlsAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    _stationListAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    _searchBarAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );

    _searchBarAnimation = CurvedAnimation(
      parent: _searchBarAnimationController,
      curve: Curves.easeOutCubic,
    );

    // Start the animations when the widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _animationController.forward();
      _searchBarAnimationController.forward();
      _mapControlsAnimationController.forward();
      _stationListAnimationController.forward();
    });
  }

  // Add this method to load stations
  Future<void> _loadStationsAlongRoute() async {
    setState(() {
      _isLoadingStations = true;
    });

    try {
      // For now, just get station markers and convert them
      final markersResponse = await _stationService.getStationMarkers();
      if (markersResponse.success && markersResponse.data != null) {
        setState(() {
          _stationsAlongRoute =
              []; // TODO: Convert markers to stations if needed
          _isLoadingStations = false;
        });
      } else {
        throw Exception(markersResponse.message);
      }
    } catch (e) {
      // Use a proper logging framework in production
      debugPrint('Error loading stations: $e');
      setState(() {
        _isLoadingStations = false;
      });
    }
  }

  @override
  void dispose() {
    _mapController?.dispose();
    _currentLocationController.dispose();
    _destinationController.dispose();
    _animationController.dispose();
    _searchBarAnimationController.dispose();
    _mapControlsAnimationController.dispose();
    _stationListAnimationController.dispose();
    super.dispose();
  }

  // Called when the Google Map is created
  void _onMapCreated(GoogleMapController controller) {
    _mapController = controller;
  }

  // Dummy method to simulate route calculation and update UI.
  // In a production app, call your routing API here.
  void _calculateRoute() {
    setState(() {
      // Once a destination is entered, the bottom sheet will appear with station list and summary.
      _isSheetExpanded = true;
    });
  }

  // This method simulates the start of navigation
  void _startNavigation() {
    setState(() {
      _isNavigating = true;
      // In a real application, you would call your navigation logic here.
    });
  }

  // When the user taps on the map, collapse the bottom sheet (if not navigating)
  void _onMapTap(LatLng latLng) {
    // Always minimize the sheet when tapping on the map, unless in navigation mode
    if (!_isNavigating) {
      // Fold/minimize the sheet to its minimum size
      if (_draggableScrollableController.isAttached) {
        _draggableScrollableController.animateTo(
          0.1, // Minimum sheet size
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOutCubic,
        );
      }

      setState(() {
        _isSheetExpanded = false;
      });

      // Unfocus any text field to dismiss keyboard
      FocusScope.of(context).unfocus();
    }
  }

  // Handler for GoogleMapWidget tap
  void _handleMapTap(LatLng position) {
    // Just delegate to the main map tap handler with the position
    _onMapTap(position);
  }

  @override
  Widget build(BuildContext context) {
    // Use the current theme from the context
    return Builder(
      builder: (context) => Scaffold(
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        // Main Stack to overlay Map, TopBar, and BottomSheet
        body: Stack(
          children: [
            // Use GoogleMapWidget instead of GoogleMap when useMapWidget is true
            useMapWidget
                ? GoogleMapWidget(
                    onTap: _handleMapTap, // Use the handler method
                    // Add station markers to the map
                    stations: _stationsAlongRoute
                        .map((station) => {
                              'id': station.id,
                              'name': station.name,
                              'address': station.address,
                              'latitude': station.latitude,
                              'longitude': station.longitude,
                              'status': station.status,
                              'connectorType': station.connectorType,
                            })
                        .toList(),
                  ) // Use the GoogleMapWidget instead
                : GoogleMap(
                    onMapCreated: _onMapCreated,
                    initialCameraPosition: _initialCameraPosition,
                    markers: _stationMarkers,
                    onTap: _onMapTap,
                    myLocationEnabled: true, // Requires location permissions
                    myLocationButtonEnabled: true,
                  ),
            // Top Bar for current location and destination search fields
            SafeArea(child: _buildTopBar()),
            // Collapsible Bottom Sheet to show trip summary and station list or navigation info
            _buildBottomSheet(),

            // Removed the positioned buttons since they're now in the top bar
          ],
        ),
        // Removed the bottom navigation bar
      ),
    );
  }

  /// Enhanced top bar with modern design and animations
  Widget _buildTopBar() {
    return AnimatedBuilder(
      animation: _searchBarAnimation,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, -50 * (1 - _searchBarAnimation.value)),
          child: Opacity(
            opacity: _searchBarAnimation.value,
            child: Container(
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).brightness == Brightness.dark
                    ? AppThemes.darkCard
                    : Colors.white,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.black.withAlpha(60)
                        : Colors.black.withAlpha(26),
                    blurRadius: 8,
                    spreadRadius: 0,
                    offset: const Offset(0, 4),
                  ),
                ],
                border: Border.all(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? AppThemes.darkBorder
                      : Colors.grey.shade300,
                  width: 1.5,
                ),
              ),
              child: Stack(
                clipBehavior: Clip.none,
                children: [
                  // Search fields container
                  Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                    decoration: BoxDecoration(
                      color: Theme.of(context).brightness == Brightness.dark
                          ? AppThemes.darkCard
                          : Colors.white,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Current location field with animation
                        TweenAnimationBuilder<double>(
                          tween: Tween<double>(begin: 0.0, end: 1.0),
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeOutCubic,
                          builder: (context, value, child) {
                            return Opacity(
                              opacity: value,
                              child: Transform.translate(
                                offset: Offset(0, (1 - value) * -10),
                                child: child,
                              ),
                            );
                          },
                          child: Row(
                            children: [
                              Container(
                                width: 36,
                                height: 36,
                                decoration: BoxDecoration(
                                  color: Theme.of(context).brightness ==
                                          Brightness.dark
                                      ? AppThemes.secondaryDarkColor
                                          .withAlpha(128)
                                      : AppTheme.secondaryLightColor,
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                child: Center(
                                  child: Icon(
                                    Icons.my_location,
                                    color: Theme.of(context).brightness ==
                                            Brightness.dark
                                        ? Colors.lightBlueAccent
                                        : AppTheme.secondaryColor,
                                    size: 20,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Material(
                                  color: Colors.transparent,
                                  child: InkWell(
                                    onTap: () {
                                      // Show location picker for current location
                                      HapticFeedback.selectionClick();
                                      _showLocationPicker(isDestination: false);
                                    },
                                    splashColor:
                                        AppTheme.secondaryColor.withAlpha(30),
                                    highlightColor:
                                        AppTheme.secondaryColor.withAlpha(20),
                                    borderRadius: BorderRadius.circular(12),
                                    // Increase the hit target area with padding
                                    child: Padding(
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 8.0),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            'Current Location',
                                            style: TextStyle(
                                              fontSize: 12,
                                              fontWeight: FontWeight.w500,
                                              color: Theme.of(context)
                                                          .brightness ==
                                                      Brightness.dark
                                                  ? AppThemes.darkTextSecondary
                                                  : AppTheme.textSecondaryColor,
                                            ),
                                          ),
                                          const SizedBox(height: 2),
                                          Text(
                                            _currentLocationController.text,
                                            style: TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.w600,
                                              color: Theme.of(context)
                                                          .brightness ==
                                                      Brightness.dark
                                                  ? Colors.white
                                                  : AppTheme.textPrimaryColor,
                                            ),
                                            maxLines: 1,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              )
                            ],
                          ),
                        ),

                        // Animated divider
                        TweenAnimationBuilder<double>(
                          tween: Tween<double>(begin: 0.0, end: 1.0),
                          duration: const Duration(milliseconds: 400),
                          curve: Curves.easeOutCubic,
                          builder: (context, value, child) {
                            return Container(
                              margin: const EdgeInsets.symmetric(vertical: 12),
                              width: MediaQuery.of(context).size.width * value,
                              height: 1,
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [
                                    Colors.grey.withAlpha(40),
                                    Colors.grey.withAlpha(10),
                                  ],
                                  begin: Alignment.centerLeft,
                                  end: Alignment.centerRight,
                                ),
                              ),
                            );
                          },
                        ),

                        // Destination field with animation
                        TweenAnimationBuilder<double>(
                          tween: Tween<double>(begin: 0.0, end: 1.0),
                          duration: const Duration(milliseconds: 400),
                          curve: Curves.easeOutCubic,
                          builder: (context, value, child) {
                            return Opacity(
                              opacity: value,
                              child: Transform.translate(
                                offset: Offset(0, (1 - value) * 10),
                                child: child,
                              ),
                            );
                          },
                          child: Row(
                            children: [
                              Container(
                                width: 36,
                                height: 36,
                                decoration: BoxDecoration(
                                  color: Theme.of(context).brightness ==
                                          Brightness.dark
                                      ? AppThemes.secondaryDarkColor
                                          .withAlpha(128)
                                      : AppTheme.secondaryLightColor,
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                child: Center(
                                  child: Icon(
                                    Icons.location_on,
                                    color: Theme.of(context).brightness ==
                                            Brightness.dark
                                        ? Colors.redAccent
                                        : Colors.red,
                                    size: 20,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Material(
                                  color: Colors.transparent,
                                  child: InkWell(
                                    onTap: () {
                                      // Show destination picker
                                      HapticFeedback.selectionClick();
                                      _showLocationPicker(isDestination: true);
                                    },
                                    splashColor: Colors.red.withAlpha(30),
                                    highlightColor: Colors.red.withAlpha(20),
                                    borderRadius: BorderRadius.circular(12),
                                    // Increase the hit target area with padding
                                    child: Padding(
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 8.0),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            'Destination',
                                            style: TextStyle(
                                              fontSize: 12,
                                              fontWeight: FontWeight.w500,
                                              color: Theme.of(context)
                                                          .brightness ==
                                                      Brightness.dark
                                                  ? AppThemes.primaryLightColor
                                                  : AppTheme.textSecondaryColor,
                                            ),
                                          ),
                                          const SizedBox(height: 2),
                                          Text(
                                            _destinationController.text.isEmpty
                                                ? 'Enter destination'
                                                : _destinationController.text,
                                            style: TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.w600,
                                              color: _destinationController
                                                      .text.isEmpty
                                                  ? (Theme.of(context)
                                                              .brightness ==
                                                          Brightness.dark
                                                      ? AppThemes.primaryColor
                                                          .withAlpha(128)
                                                      : AppTheme.textLightColor)
                                                  : (Theme.of(context)
                                                              .brightness ==
                                                          Brightness.dark
                                                      ? Colors.white
                                                      : AppTheme
                                                          .textPrimaryColor),
                                            ),
                                            maxLines: 1,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              if (_destinationController.text.isNotEmpty)
                                TweenAnimationBuilder<double>(
                                  tween: Tween<double>(begin: 0.0, end: 1.0),
                                  duration: const Duration(milliseconds: 200),
                                  curve: Curves.easeOut,
                                  builder: (context, value, child) {
                                    return Opacity(
                                      opacity: value,
                                      child: Transform.scale(
                                        scale: 0.8 + (0.2 * value),
                                        child: child,
                                      ),
                                    );
                                  },
                                  child: IconButton(
                                    icon: Icon(
                                      Icons.close_rounded,
                                      color: Theme.of(context).brightness ==
                                              Brightness.dark
                                          ? AppThemes.darkTextSecondary
                                          : AppTheme.textSecondaryColor,
                                      size: 20,
                                    ),
                                    onPressed: () {
                                      HapticFeedback.selectionClick();
                                      setState(() {
                                        _destinationController.clear();
                                        _isSheetExpanded = false;
                                      });
                                    },
                                    padding: EdgeInsets.zero,
                                    constraints: const BoxConstraints(),
                                    splashRadius: 24,
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Swap button with enhanced animation
                  if (_destinationController.text.isNotEmpty)
                    Positioned(
                      right: 16,
                      top: 0,
                      bottom: 0,
                      child: TweenAnimationBuilder<double>(
                        tween: Tween<double>(begin: 0.0, end: 1.0),
                        duration: const Duration(milliseconds: 300),
                        curve: Curves.elasticOut,
                        builder: (context, value, child) {
                          return Transform.scale(
                            scale: value,
                            child: child,
                          );
                        },
                        child: Container(
                          width: 36,
                          height: 36,
                          decoration: BoxDecoration(
                            color: AppTheme.secondaryColor,
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: AppTheme.secondaryColor.withAlpha(60),
                                blurRadius: 8,
                                spreadRadius: 0,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Material(
                            color: Colors.transparent,
                            shape: const CircleBorder(),
                            clipBehavior: Clip.antiAlias,
                            child: InkWell(
                              onTap: () {
                                HapticFeedback.mediumImpact();
                                setState(() {
                                  // Swap current location and destination with animation
                                  final temp = _currentLocationController.text;
                                  _currentLocationController.text =
                                      _destinationController.text;
                                  _destinationController.text = temp;

                                  // Recalculate route if needed
                                  if (_isSheetExpanded) {
                                    _calculateRoute();
                                  }
                                });
                              },
                              splashColor: Colors.white.withAlpha(50),
                              child: const Center(
                                child: Icon(
                                  Icons.swap_vert_rounded,
                                  color: Colors.white,
                                  size: 20,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // Enhance the animation in the location picker
  void _showLocationPicker({required bool isDestination}) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      transitionAnimationController: AnimationController(
        vsync: Navigator.of(context),
        duration: const Duration(milliseconds: 300),
      ),
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setModalState) {
            return AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              height: MediaQuery.of(context).size.height * 0.7,
              decoration: BoxDecoration(
                color: Theme.of(context).brightness == Brightness.dark
                    ? AppThemes.darkSurface
                    : Colors.white,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
              ),
              child: Column(
                children: [
                  // Handle bar
                  Container(
                    width: 40,
                    height: 5,
                    margin: const EdgeInsets.symmetric(vertical: 10),
                    decoration: BoxDecoration(
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.grey[600]
                          : Colors.grey[400],
                      borderRadius: BorderRadius.circular(2.5),
                    ),
                  ),

                  // Search bar with increased touch area
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Material(
                      color: Theme.of(context).brightness == Brightness.dark
                          ? AppThemes.darkCard
                          : Colors.grey[200],
                      borderRadius: BorderRadius.circular(10),
                      child: InkWell(
                        borderRadius: BorderRadius.circular(10),
                        splashColor: isDestination
                            ? Colors.red.withAlpha(30)
                            : AppTheme.secondaryColor.withAlpha(30),
                        highlightColor: isDestination
                            ? Colors.red.withAlpha(20)
                            : AppTheme.secondaryColor.withAlpha(20),
                        onTap: () {
                          // The TextField will handle the tap since it's on top
                        },
                        child: Stack(
                          children: [
                            // Larger hit target area
                            SizedBox(
                              height: 56,
                              width: double.infinity,
                            ),
                            // Actual TextField
                            TextField(
                              autofocus: true,
                              decoration: InputDecoration(
                                hintText: isDestination
                                    ? 'Search destination'
                                    : 'Search location',
                                prefixIcon: Icon(
                                  isDestination
                                      ? Icons.location_on
                                      : Icons.my_location,
                                  color: isDestination
                                      ? Colors.red
                                      : AppTheme.secondaryColor,
                                ),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(10),
                                  borderSide: BorderSide.none,
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(10),
                                  borderSide: BorderSide.none,
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(10),
                                  borderSide: BorderSide.none,
                                ),
                                filled: true,
                                fillColor: Colors
                                    .transparent, // Make transparent to show the Material underneath
                                contentPadding: const EdgeInsets.symmetric(
                                    vertical: 16, horizontal: 12),
                              ),
                              onChanged: (value) {
                                // In a real app, you would filter suggestions based on input
                                setModalState(() {});
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),

                  // Example location suggestions
                  Expanded(
                    child: ListView(
                      children: [
                        _buildLocationSuggestion(
                          'Current Location',
                          'Using GPS',
                          Icons.my_location,
                          Colors.blue,
                          () {
                            if (isDestination) {
                              setState(() {
                                _destinationController.text =
                                    'Current Location';
                                _calculateRoute();
                              });
                            }
                            Navigator.pop(context);
                          },
                        ),
                        _buildLocationSuggestion(
                          'Home',
                          '123 Main Street',
                          Icons.home,
                          Colors.green,
                          () {
                            if (isDestination) {
                              setState(() {
                                _destinationController.text = 'Home';
                                _calculateRoute();
                              });
                            } else {
                              setState(() {
                                _currentLocationController.text = 'Home';
                              });
                            }
                            Navigator.pop(context);
                          },
                        ),
                        _buildLocationSuggestion(
                          'Work',
                          '456 Office Park',
                          Icons.work,
                          Colors.orange,
                          () {
                            if (isDestination) {
                              setState(() {
                                _destinationController.text = 'Work';
                                _calculateRoute();
                              });
                            } else {
                              setState(() {
                                _currentLocationController.text = 'Work';
                              });
                            }
                            Navigator.pop(context);
                          },
                        ),
                        _buildLocationSuggestion(
                          'Shopping Mall',
                          '789 Retail Road',
                          Icons.shopping_cart,
                          Colors.purple,
                          () {
                            if (isDestination) {
                              setState(() {
                                _destinationController.text = 'Shopping Mall';
                                _calculateRoute();
                              });
                            } else {
                              setState(() {
                                _currentLocationController.text =
                                    'Shopping Mall';
                              });
                            }
                            Navigator.pop(context);
                          },
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  // Helper method to build location suggestion items
  Widget _buildLocationSuggestion(
    String title,
    String subtitle,
    IconData icon,
    Color iconColor,
    VoidCallback onTap,
  ) {
    return ListTile(
      leading: Icon(icon, color: iconColor),
      title: Text(title),
      subtitle: Text(subtitle),
      onTap: onTap,
    );
  }

  // Enhanced bottom sheet with modern design and animations
  Widget _buildBottomSheet() {
    // When no destination is set and not navigating, nothing is shown.
    if (!_isSheetExpanded && !_isNavigating) {
      return const SizedBox.shrink();
    }

    // If navigating, show a compact navigation bar at the bottom with enhanced design
    if (_isNavigating) {
      return Align(
        alignment: Alignment.bottomCenter,
        child: TweenAnimationBuilder<double>(
          tween: Tween<double>(begin: 0.0, end: 1.0),
          duration: const Duration(milliseconds: 400),
          curve: Curves.easeOutQuart,
          builder: (context, value, child) {
            return Transform.translate(
              offset: Offset(0, 100 * (1 - value)),
              child: Opacity(
                opacity: value,
                child: child,
              ),
            );
          },
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
            decoration: BoxDecoration(
              color: Theme.of(context).brightness == Brightness.dark
                  ? AppThemes.darkCard
                  : Colors.white,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.black.withAlpha(60)
                      : Colors.black.withAlpha(20),
                  blurRadius: 10,
                  spreadRadius: 0,
                  offset: const Offset(0, -2),
                ),
              ],
              border: Border.all(
                color: Theme.of(context).brightness == Brightness.dark
                    ? AppThemes.primaryColor.withAlpha(77)
                    : Colors.transparent,
                width: 1.5,
              ),
            ),
            child: Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Theme.of(context).brightness == Brightness.dark
                        ? AppThemes.secondaryDarkColor.withAlpha(77)
                        : AppTheme.secondaryLightColor,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Center(
                    child: Icon(
                      Icons.navigation_rounded,
                      color: AppTheme.secondaryColor,
                      size: 22,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        'Navigating to',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? AppThemes.primaryLightColor
                              : AppTheme.textSecondaryColor,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        _destinationController.text,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.white
                              : AppTheme.textPrimaryColor,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                Material(
                  color: Colors.transparent,
                  shape: const CircleBorder(),
                  clipBehavior: Clip.antiAlias,
                  child: IconButton(
                    icon: const Icon(Icons.close_rounded,
                        color: AppTheme.errorColor),
                    onPressed: () {
                      // Cancel navigation with haptic feedback
                      HapticFeedback.mediumImpact();
                      setState(() {
                        _isNavigating = false;
                        _isSheetExpanded = true;
                      });
                    },
                    splashColor: AppTheme.errorColor.withAlpha(30),
                    highlightColor: AppTheme.errorColor.withAlpha(20),
                    tooltip: 'Cancel navigation',
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    // Enhanced bottom sheet with trip summary and station list
    return Align(
      alignment: Alignment.bottomCenter,
      child: NotificationListener<DraggableScrollableNotification>(
        onNotification: (notification) {
          setState(() {
            _isSheetExpanded = notification.extent > 0.15;
          });
          return true;
        },
        child: DraggableScrollableSheet(
          controller: _draggableScrollableController,
          initialChildSize: 0.5,
          minChildSize: 0.1,
          maxChildSize: 0.85,
          snap: true,
          snapSizes: const [0.1, 0.5, 0.85],
          // Enhanced snap behavior for better physics feel
          builder: (BuildContext context, ScrollController scrollController) {
            return GestureDetector(
                // Make the entire sheet draggable
                onVerticalDragUpdate: (details) {
                  // Manually control the sheet position based on drag
                  if (_draggableScrollableController.isAttached) {
                    _draggableScrollableController.jumpTo(
                        _draggableScrollableController.size -
                            (details.delta.dy /
                                MediaQuery.of(context).size.height));
                  }
                },
                onVerticalDragEnd: (details) {
                  if (!_draggableScrollableController.isAttached) {
                    return;
                  }

                  final velocity = details.primaryVelocity ?? 0;
                  if (velocity < -300) {
                    // Swipe up - expand
                    _draggableScrollableController.animateTo(
                      0.85, // Max sheet size
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeOut,
                    );
                  } else if (velocity > 300) {
                    // Swipe down - collapse
                    _draggableScrollableController.animateTo(
                      0.1, // Min sheet size
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeOut,
                    );
                  }
                },
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  decoration: BoxDecoration(
                    color: Theme.of(context).brightness == Brightness.dark
                        ? AppThemes.darkSurface
                        : Colors.white,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(24),
                      topRight: Radius.circular(24),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Colors.black.withAlpha(60)
                            : Colors.black.withAlpha(20),
                        blurRadius: 10,
                        spreadRadius: 0,
                        offset: const Offset(0, -4),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Empty space instead of handle indicator
                      SizedBox(height: 16),

                      // Trip summary section with enhanced design
                      if (_isSheetExpanded &&
                          _destinationController.text.isNotEmpty)
                        Column(
                          children: [
                            Padding(
                              padding: const EdgeInsets.fromLTRB(20, 0, 20, 16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // Trip summary header with animation
                                  TweenAnimationBuilder<double>(
                                    tween: Tween<double>(begin: 0.0, end: 1.0),
                                    duration: const Duration(milliseconds: 400),
                                    curve: Curves.easeOutCubic,
                                    builder: (context, value, child) {
                                      return Opacity(
                                        opacity: value,
                                        child: Transform.translate(
                                          offset: Offset(0, 10 * (1 - value)),
                                          child: child,
                                        ),
                                      );
                                    },
                                    child: Row(
                                      children: [
                                        Container(
                                          width: 36,
                                          height: 36,
                                          decoration: BoxDecoration(
                                            color: Theme.of(context)
                                                        .brightness ==
                                                    Brightness.dark
                                                ? AppThemes.primaryDarkColor
                                                    .withAlpha(77)
                                                : AppTheme.primaryLightColor,
                                            borderRadius:
                                                BorderRadius.circular(10),
                                          ),
                                          child: Center(
                                            child: Icon(
                                              Icons.route_rounded,
                                              color: AppTheme.primaryColor,
                                              size: 20,
                                            ),
                                          ),
                                        ),
                                        const SizedBox(width: 12),
                                        Text(
                                          'Trip Summary',
                                          style: TextStyle(
                                            fontSize: 18,
                                            fontWeight: FontWeight.bold,
                                            color:
                                                Theme.of(context).brightness ==
                                                        Brightness.dark
                                                    ? Colors.white
                                                    : AppTheme.textPrimaryColor,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),

                                  const SizedBox(height: 20),

                                  // Trip details with animation
                                  TweenAnimationBuilder<double>(
                                    tween: Tween<double>(begin: 0.0, end: 1.0),
                                    duration: const Duration(milliseconds: 500),
                                    curve: Curves.easeOutCubic,
                                    builder: (context, value, child) {
                                      return Opacity(
                                        opacity: value,
                                        child: Transform.translate(
                                          offset: Offset(0, 15 * (1 - value)),
                                          child: child,
                                        ),
                                      );
                                    },
                                    child: Container(
                                      padding: const EdgeInsets.all(16),
                                      decoration: BoxDecoration(
                                        color: Theme.of(context).brightness ==
                                                Brightness.dark
                                            ? AppThemes.darkCard
                                            : AppTheme.backgroundColor,
                                        borderRadius: BorderRadius.circular(16),
                                      ),
                                      child: Row(
                                        children: [
                                          // Time column
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Row(
                                                  children: [
                                                    Icon(
                                                      Icons.access_time_rounded,
                                                      size: 16,
                                                      color: Theme.of(context)
                                                                  .brightness ==
                                                              Brightness.dark
                                                          ? Colors
                                                              .lightBlueAccent
                                                          : AppTheme
                                                              .secondaryColor,
                                                    ),
                                                    const SizedBox(width: 8),
                                                    Text(
                                                      'Est. Time',
                                                      style: TextStyle(
                                                        fontSize: 12,
                                                        color: Theme.of(context)
                                                                    .brightness ==
                                                                Brightness.dark
                                                            ? AppThemes
                                                                .darkTextSecondary
                                                            : AppTheme
                                                                .textSecondaryColor,
                                                        fontWeight:
                                                            FontWeight.w500,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                                const SizedBox(height: 4),
                                                Text(
                                                  '45 min',
                                                  style: TextStyle(
                                                    fontSize: 18,
                                                    fontWeight: FontWeight.bold,
                                                    color: Theme.of(context)
                                                                .brightness ==
                                                            Brightness.dark
                                                        ? AppThemes
                                                            .darkTextPrimary
                                                        : AppTheme
                                                            .textPrimaryColor,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),

                                          // Vertical divider
                                          Container(
                                            height: 40,
                                            width: 1,
                                            color:
                                                Theme.of(context).brightness ==
                                                        Brightness.dark
                                                    ? Colors.grey.withAlpha(80)
                                                    : Colors.grey.withAlpha(40),
                                          ),

                                          // Distance column
                                          Expanded(
                                            child: Padding(
                                              padding: const EdgeInsets.only(
                                                  left: 16),
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Row(
                                                    children: [
                                                      Icon(
                                                        Icons
                                                            .straighten_rounded,
                                                        size: 16,
                                                        color: Theme.of(context)
                                                                    .brightness ==
                                                                Brightness.dark
                                                            ? Colors.amberAccent
                                                            : AppTheme
                                                                .accentColor,
                                                      ),
                                                      const SizedBox(width: 8),
                                                      Text(
                                                        'Distance',
                                                        style: TextStyle(
                                                          fontSize: 12,
                                                          color: Theme.of(context)
                                                                      .brightness ==
                                                                  Brightness
                                                                      .dark
                                                              ? AppThemes
                                                                  .darkTextSecondary
                                                              : AppTheme
                                                                  .textSecondaryColor,
                                                          fontWeight:
                                                              FontWeight.w500,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                  const SizedBox(height: 4),
                                                  Text(
                                                    '35 km',
                                                    style: TextStyle(
                                                      fontSize: 18,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      color: Theme.of(context)
                                                                  .brightness ==
                                                              Brightness.dark
                                                          ? AppThemes
                                                              .darkTextPrimary
                                                          : AppTheme
                                                              .textPrimaryColor,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),

                                  const SizedBox(height: 20),

                                  // Start navigation button with animation
                                  TweenAnimationBuilder<double>(
                                    tween: Tween<double>(begin: 0.0, end: 1.0),
                                    duration: const Duration(milliseconds: 600),
                                    curve: Curves.easeOutCubic,
                                    builder: (context, value, child) {
                                      return Opacity(
                                        opacity: value,
                                        child: Transform.translate(
                                          offset: Offset(0, 20 * (1 - value)),
                                          child: child,
                                        ),
                                      );
                                    },
                                    child: SizedBox(
                                      width: double.infinity,
                                      height: 50,
                                      child: ElevatedButton.icon(
                                        onPressed: () {
                                          HapticFeedback.mediumImpact();
                                          _startNavigation();
                                        },
                                        icon: const Icon(
                                            Icons.navigation_rounded),
                                        label: const Text('Start Navigation'),
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor:
                                              Theme.of(context).brightness ==
                                                      Brightness.dark
                                                  ? AppThemes.primaryDarkColor
                                                  : AppTheme.primaryColor,
                                          foregroundColor: Colors.white,
                                          elevation: 0,
                                          shape: RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(12),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),

                                  const Padding(
                                    padding: EdgeInsets.symmetric(vertical: 20),
                                    child: Divider(height: 1),
                                  ),

                                  // Charging stations header with animation
                                  TweenAnimationBuilder<double>(
                                    tween: Tween<double>(begin: 0.0, end: 1.0),
                                    duration: const Duration(milliseconds: 700),
                                    curve: Curves.easeOutCubic,
                                    builder: (context, value, child) {
                                      return Opacity(
                                        opacity: value,
                                        child: Transform.translate(
                                          offset: Offset(0, 10 * (1 - value)),
                                          child: child,
                                        ),
                                      );
                                    },
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Row(
                                          children: [
                                            Container(
                                              width: 36,
                                              height: 36,
                                              decoration: BoxDecoration(
                                                color:
                                                    AppTheme.primaryLightColor,
                                                borderRadius:
                                                    BorderRadius.circular(10),
                                              ),
                                              child: Center(
                                                child: Icon(
                                                  Icons.ev_station_rounded,
                                                  color: AppTheme.primaryColor,
                                                  size: 20,
                                                ),
                                              ),
                                            ),
                                            const SizedBox(width: 12),
                                            const Text(
                                              'Charging Stations',
                                              style: TextStyle(
                                                fontSize: 18,
                                                fontWeight: FontWeight.bold,
                                                color:
                                                    AppTheme.textPrimaryColor,
                                              ),
                                            ),
                                          ],
                                        ),
                                        Container(
                                          padding: const EdgeInsets.symmetric(
                                            horizontal: 10,
                                            vertical: 6,
                                          ),
                                          decoration: BoxDecoration(
                                            color: Theme.of(context)
                                                        .brightness ==
                                                    Brightness.dark
                                                ? AppThemes.primaryDarkColor
                                                    .withAlpha(77)
                                                : AppTheme.primaryLightColor,
                                            borderRadius:
                                                BorderRadius.circular(12),
                                          ),
                                          child: Text(
                                            '${_stationsAlongRoute.length} found',
                                            style: TextStyle(
                                              color: AppTheme.primaryColor,
                                              fontWeight: FontWeight.w600,
                                              fontSize: 12,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),

                            // If sheet is collapsed, show just the title with enhanced design
                            if (!_isSheetExpanded)
                              Padding(
                                padding:
                                    const EdgeInsets.fromLTRB(20, 8, 20, 8),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Row(
                                      children: [
                                        Container(
                                          width: 32,
                                          height: 32,
                                          decoration: BoxDecoration(
                                            color: Theme.of(context)
                                                        .brightness ==
                                                    Brightness.dark
                                                ? AppThemes.primaryDarkColor
                                                    .withAlpha(77)
                                                : AppTheme.primaryLightColor,
                                            borderRadius:
                                                BorderRadius.circular(8),
                                          ),
                                          child: Center(
                                            child: Icon(
                                              Icons.ev_station_rounded,
                                              color: AppTheme.primaryColor,
                                              size: 18,
                                            ),
                                          ),
                                        ),
                                        const SizedBox(width: 12),
                                        Text(
                                          'Charging Stations',
                                          style: TextStyle(
                                            fontSize: 16,
                                            fontWeight: FontWeight.bold,
                                            color:
                                                Theme.of(context).brightness ==
                                                        Brightness.dark
                                                    ? AppThemes.darkTextPrimary
                                                    : AppTheme.textPrimaryColor,
                                          ),
                                        ),
                                      ],
                                    ),
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 8,
                                        vertical: 4,
                                      ),
                                      decoration: BoxDecoration(
                                        color: AppTheme.primaryLightColor,
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: Text(
                                        '${_stationsAlongRoute.length} found',
                                        style: TextStyle(
                                          color: AppTheme.primaryColor,
                                          fontWeight: FontWeight.w600,
                                          fontSize: 12,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                          ],
                        ),

                      // List of charging stations with enhanced design
                      Expanded(
                        child: _isLoadingStations
                            ? Center(
                                child: CircularProgressIndicator(
                                  color: AppTheme.primaryColor,
                                  strokeWidth: 3,
                                ),
                              )
                            : NotificationListener<ScrollNotification>(
                                onNotification: (notification) {
                                  // Handle all scroll notifications to implement physics-based behavior
                                  if (notification is ScrollEndNotification) {
                                    // When user stops scrolling, check if there's empty space at the bottom
                                    if (scrollController.hasClients) {
                                      final maxScroll = scrollController
                                          .position.maxScrollExtent;
                                      final currentScroll =
                                          scrollController.position.pixels;
                                      final viewportDimension = scrollController
                                          .position.viewportDimension;

                                      // If we're not at the top and there's empty space (content doesn't fill viewport)
                                      if (currentScroll > 0 &&
                                          maxScroll < viewportDimension) {
                                        // Snap to the bottom with physics-based animation
                                        scrollController.animateTo(
                                          0, // Scroll to top since content is shorter than viewport
                                          duration:
                                              const Duration(milliseconds: 300),
                                          curve: Curves.easeOutCubic,
                                        );
                                      } else if (currentScroll < maxScroll) {
                                        // If we're not at the bottom and user released, snap to show the last card
                                        final remainingScrollableDistance =
                                            maxScroll - currentScroll;

                                        // If the remaining distance is less than 20% of viewport, snap to bottom
                                        if (remainingScrollableDistance <
                                            viewportDimension * 0.2) {
                                          scrollController.animateTo(
                                            maxScroll,
                                            duration: const Duration(
                                                milliseconds: 300),
                                            curve: Curves.easeOutCubic,
                                          );
                                        }
                                      }
                                    }
                                  }
                                  return false;
                                },
                                child: ListView.builder(
                                  physics: const BouncingScrollPhysics(
                                      parent:
                                          AlwaysScrollableScrollPhysics()), // Enhanced physics for better scrolling feel
                                  controller: scrollController,
                                  // Remove bottom padding to eliminate empty space
                                  padding:
                                      const EdgeInsets.only(top: 8, bottom: 0),
                                  itemCount: _stationsAlongRoute.length,
                                  itemBuilder: (context, index) {
                                    final station = _stationsAlongRoute[index];
                                    // Add staggered animation to list items
                                    // Enhanced floating animation for station cards
                                    return AnimatedBuilder(
                                      animation: Tween<double>(
                                        begin: 0.0,
                                        end: 1.0,
                                      ).animate(
                                        CurvedAnimation(
                                          parent: _animationController,
                                          curve: Interval(
                                            0.1 * index.clamp(0, 9),
                                            0.1 * index.clamp(0, 9) + 0.5,
                                            curve: Curves.easeOutQuart,
                                          ),
                                        ),
                                      ),
                                      builder: (context, child) {
                                        // Create a floating effect with opacity and translation
                                        return TweenAnimationBuilder<double>(
                                          tween: Tween<double>(
                                              begin: 0.95, end: 1.0),
                                          duration: Duration(
                                              milliseconds:
                                                  1500 + (index * 100)),
                                          curve: Curves.easeInOut,
                                          builder:
                                              (context, floatValue, child) {
                                            return Opacity(
                                              opacity:
                                                  _animationController.value,
                                              child: Transform.translate(
                                                offset: Offset(
                                                  0,
                                                  20 *
                                                          (1 -
                                                              _animationController
                                                                  .value) +
                                                      (1 - floatValue) * 8,
                                                ),
                                                child: Transform.scale(
                                                  scale: floatValue,
                                                  child: child,
                                                ),
                                              ),
                                            );
                                          },
                                          child: child,
                                        );
                                      },
                                      child: _buildStationCard(station),
                                    );
                                  },
                                )),
                      ),
                    ],
                  ),
                ));
          },
        ),
      ),
    );
  }

  // Enhanced station card with modern design using real API data
  Widget _buildStationCard(Station station) {
    // Use the new dynamic StationCard widget with real API data
    // NO MORE HARDCODED VALUES - All data comes from Station model
    return StationCard(
      station: station,
      onTap: () {
        // Navigate to station details with proper UID validation
        final stationUid = station.uid ?? station.id;
        if (stationUid.isNotEmpty) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => StationDetailsPage(
                uid: stationUid,
                station: station,
              ),
            ),
          );
        } else {
          // Show error if no valid UID
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Station ID not available'),
              backgroundColor: Colors.red,
            ),
          );
        }
      },
    );
  }

  // Helper method to get connector color based on type
  Color _getConnectorColor(String type) {
    switch (type.toUpperCase()) {
      case 'CCS':
      case 'CCS2':
        return const Color(0xFF4CAF50); // Green
      case 'CHADEMO':
        return const Color(0xFF2196F3); // Blue
      case 'TYPE2':
        return const Color(0xFF9C27B0); // Purple
      case 'GB/T':
        return const Color(0xFFFF9800); // Orange
      default:
        return const Color(0xFF607D8B); // Blue Grey
    }
  }
}
