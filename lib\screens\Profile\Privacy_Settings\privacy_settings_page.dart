import 'package:flutter/material.dart';

class PrivacySettingsPage extends StatefulWidget {
  const PrivacySettingsPage({super.key});

  @override
  State<PrivacySettingsPage> createState() => _PrivacySettingsPageState();
}

class _PrivacySettingsPageState extends State<PrivacySettingsPage> {
  bool _locationPermission = true;
  bool _notificationPermission = true;
  bool _dataCollection = true;
  bool _marketingEmails = false;

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      backgroundColor:
          isDarkMode ? const Color(0xFF121212) : const Color(0xFFF5F7FA),
      appBar: AppBar(
        title: const Text(
          'Privacy Settings',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Permissions section
            _buildSectionHeader('App Permissions', Icons.security),
            const SizedBox(height: 8),
            _buildSettingsCard(
              children: [
                _buildSwitchTile(
                  title: 'Location Services',
                  subtitle: 'Allow app to access your location',
                  value: _locationPermission,
                  onChanged: (value) {
                    setState(() {
                      _locationPermission = value;
                    });
                  },
                ),
                _buildDivider(),
                _buildSwitchTile(
                  title: 'Notifications',
                  subtitle: 'Allow app to send you notifications',
                  value: _notificationPermission,
                  onChanged: (value) {
                    setState(() {
                      _notificationPermission = value;
                    });
                  },
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Data & Privacy section
            _buildSectionHeader('Data & Privacy', Icons.data_usage),
            const SizedBox(height: 8),
            _buildSettingsCard(
              children: [
                _buildSwitchTile(
                  title: 'Usage Data Collection',
                  subtitle:
                      'Allow app to collect usage data to improve services',
                  value: _dataCollection,
                  onChanged: (value) {
                    setState(() {
                      _dataCollection = value;
                    });
                  },
                ),
                _buildDivider(),
                _buildSwitchTile(
                  title: 'Marketing Emails',
                  subtitle: 'Receive promotional emails and offers',
                  value: _marketingEmails,
                  onChanged: (value) {
                    setState(() {
                      _marketingEmails = value;
                    });
                  },
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Account section
            _buildSectionHeader('Account', Icons.account_circle),
            const SizedBox(height: 8),
            _buildSettingsCard(
              children: [
                _buildActionTile(
                  title: 'Download My Data',
                  subtitle: 'Get a copy of your personal data',
                  icon: Icons.download,
                  onTap: () {
                    _showDownloadDataDialog();
                  },
                ),
                _buildDivider(),
                _buildActionTile(
                  title: 'Request Data Deletion',
                  subtitle: 'Request to delete your account data',
                  icon: Icons.delete_outline,
                  iconColor: Colors.red,
                  onTap: () {
                    _showDeleteDataDialog();
                  },
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Legal section
            _buildSectionHeader('Legal', Icons.gavel),
            const SizedBox(height: 8),
            _buildSettingsCard(
              children: [
                _buildActionTile(
                  title: 'Privacy Policy',
                  subtitle: 'Read our privacy policy',
                  icon: Icons.policy,
                  onTap: () {
                    // Navigate to privacy policy
                  },
                ),
                _buildDivider(),
                _buildActionTile(
                  title: 'Terms of Service',
                  subtitle: 'Read our terms of service',
                  icon: Icons.description,
                  onTap: () {
                    // Navigate to terms of service
                  },
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Danger Zone
            _buildSectionHeader('Danger Zone', Icons.warning_amber_rounded,
                color: Colors.red),
            const SizedBox(height: 8),
            _buildSettingsCard(
              children: [
                _buildActionTile(
                  title: 'Delete Account',
                  subtitle: 'Permanently delete your account and all data',
                  icon: Icons.delete_forever,
                  iconColor: Colors.red,
                  onTap: () {
                    _showDeleteAccountDialog();
                  },
                ),
              ],
            ),

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title, IconData icon, {Color? color}) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: Row(
        children: [
          Icon(
            icon,
            size: 20,
            color: color ?? (isDarkMode ? Colors.white70 : Colors.black54),
          ),
          const SizedBox(width: 8),
          Text(
            title,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color ?? (isDarkMode ? Colors.white : Colors.black87),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsCard({required List<Widget> children}) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: children,
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return SwitchListTile(
      title: Text(
        title,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: isDarkMode ? Colors.white : Colors.black87,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 14,
          color: isDarkMode ? Colors.white70 : Colors.black54,
        ),
      ),
      value: value,
      onChanged: onChanged,
      activeColor: const Color(0xFF67C44C),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
    );
  }

  Widget _buildActionTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
    Color? iconColor,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return ListTile(
      title: Text(
        title,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: isDarkMode ? Colors.white : Colors.black87,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 14,
          color: isDarkMode ? Colors.white70 : Colors.black54,
        ),
      ),
      leading: Icon(
        icon,
        color: iconColor ?? (isDarkMode ? Colors.white70 : Colors.black54),
      ),
      trailing: Icon(
        Icons.arrow_forward_ios,
        size: 16,
        color: isDarkMode ? Colors.white54 : Colors.black45,
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      onTap: onTap,
    );
  }

  Widget _buildDivider() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Divider(
      height: 1,
      thickness: 1,
      color:
          isDarkMode ? Colors.white.withAlpha(25) : Colors.grey.withAlpha(51),
      indent: 16,
      endIndent: 16,
    );
  }

  void _showDownloadDataDialog() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Download Your Data'),
        content: const Text(
          'We will prepare your data and send it to your registered email address within 48 hours.',
        ),
        backgroundColor: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
        titleTextStyle: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: isDarkMode ? Colors.white : Colors.black87,
        ),
        contentTextStyle: TextStyle(
          fontSize: 14,
          color: isDarkMode ? Colors.white70 : Colors.black54,
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showDataRequestSubmitted();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF67C44C),
            ),
            child: const Text('Request Data'),
          ),
        ],
      ),
    );
  }

  void _showDeleteDataDialog() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Request Data Deletion'),
        content: const Text(
          'We will delete all your personal data except what we are legally required to keep. This process cannot be undone.',
        ),
        backgroundColor: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
        titleTextStyle: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: isDarkMode ? Colors.white : Colors.black87,
        ),
        contentTextStyle: TextStyle(
          fontSize: 14,
          color: isDarkMode ? Colors.white70 : Colors.black54,
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showDataDeletionRequestSubmitted();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            child: const Text('Request Deletion'),
          ),
        ],
      ),
    );
  }

  void _showDeleteAccountDialog() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Account'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Are you sure you want to delete your account? This action cannot be undone and will:',
            ),
            const SizedBox(height: 16),
            _buildBulletPoint('Delete all your personal information'),
            _buildBulletPoint('Cancel any active subscriptions'),
            _buildBulletPoint('Remove your payment methods'),
            _buildBulletPoint('Delete your charging history'),
            const SizedBox(height: 16),
            const Text(
              'To confirm, please type "DELETE" below:',
            ),
            const SizedBox(height: 8),
            TextField(
              decoration: InputDecoration(
                hintText: 'Type DELETE',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
              ),
              style: TextStyle(
                color: isDarkMode ? Colors.white : Colors.black87,
              ),
            ),
          ],
        ),
        backgroundColor: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
        titleTextStyle: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: isDarkMode ? Colors.white : Colors.black87,
        ),
        contentTextStyle: TextStyle(
          fontSize: 14,
          color: isDarkMode ? Colors.white70 : Colors.black54,
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              // TODO: Implement account deletion logic
              Navigator.of(context).pop();
              _showAccountDeletionSuccess();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            child: const Text('Delete Account'),
          ),
        ],
      ),
    );
  }

  Widget _buildBulletPoint(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('• ', style: TextStyle(fontWeight: FontWeight.bold)),
          Expanded(child: Text(text)),
        ],
      ),
    );
  }

  void _showDataRequestSubmitted() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text(
            'Data request submitted successfully. Check your email in 48 hours.'),
        duration: Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showDataDeletionRequestSubmitted() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Data deletion request submitted successfully.'),
        duration: Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showAccountDeletionSuccess() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text(
            'Account deletion process initiated. You will be logged out shortly.'),
        duration: Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        backgroundColor: Colors.red,
      ),
    );
  }

  // Removed unused refund request methods
}
