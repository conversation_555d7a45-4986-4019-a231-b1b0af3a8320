import 'package:flutter/material.dart';

class LinkRfidCardSheet extends StatefulWidget {
  const LinkRfidCardSheet({super.key});

  @override
  State<LinkRfidCardSheet> createState() => _LinkRfidCardSheetState();
}

class _LinkRfidCardSheetState extends State<LinkRfidCardSheet> {
  final TextEditingController _controller = TextEditingController();
  bool _isValid = false; // Tracks whether the entered RFID is valid.
  bool _isLoading = false;
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    // Add listener to adjust view when keyboard appears
    _focusNode.addListener(() {
      if (_focusNode.hasFocus) {
        // Small delay to ensure the keyboard is fully shown
        Future.delayed(const Duration(milliseconds: 300), () {
          if (mounted) {
            // Ensure the widget is still in the tree
            Scrollable.ensureVisible(
              context,
              alignment: 0.5,
              duration: const Duration(milliseconds: 300),
            );
          }
        });
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _validateRfid(String value) {
    // Check if exactly 10 digits.
    final isExactly10Digits = RegExp(r'^\d{10}$').hasMatch(value);

    setState(() {
      _isValid = isExactly10Digits;
    });

    // Auto-dismiss keyboard when 10 digits are entered
    if (isExactly10Digits && _focusNode.hasFocus) {
      FocusScope.of(context).unfocus();
    }
  }

  Future<void> _onLinkCard() async {
    // If user attempts to link without a valid 10-digit number, show error immediately.
    if (!_isValid) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter a valid 10-digit RFID number'),
        ),
      );
      return;
    }

    // Otherwise, proceed with the linking logic.
    setState(() => _isLoading = true);
    // Simulate an API call.
    await Future.delayed(const Duration(seconds: 2));
    setState(() => _isLoading = false);

    // Show success if still valid.
    if (_isValid && mounted) {
      Navigator.of(context).pop();
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('RFID Linked Successfully!')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    // Changed from DraggableScrollableSheet to a fixed Container
    return Container(
      height: MediaQuery.of(context).size.height *
          0.9, // Set to 90% of screen height
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
      decoration: const BoxDecoration(
        color: Color(0xFF1A1A1A), // Slightly lighter dark gray
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(color: Colors.black26, blurRadius: 10, spreadRadius: 0),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Drag handle
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[600],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 16),

          // Title + close button row.
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Link RFID Card',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: const Icon(Icons.close, color: Colors.white),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // RFID input field
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Field label.
              const Text(
                'RFID Number',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),

              // Enhanced text field with animation
              AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                decoration: BoxDecoration(
                  color: const Color(0xFF2A2A2A),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: _isValid
                        ? Colors.green
                        : (_focusNode.hasFocus
                            ? Colors.blue
                            : const Color(0xFF3A3A3A)),
                    width: _focusNode.hasFocus || _isValid ? 2 : 1.5,
                  ),
                  boxShadow: _focusNode.hasFocus
                      ? [
                          BoxShadow(
                            color: _isValid
                                ? Colors.green.withAlpha(77) // 0.3 * 255 = ~77
                                : Colors.blue.withAlpha(51), // 0.2 * 255 = ~51
                            blurRadius: 8,
                            spreadRadius: 1,
                          ),
                        ]
                      : [],
                ),
                child: TextField(
                  controller: _controller,
                  focusNode: _focusNode,
                  keyboardType: TextInputType.number,
                  maxLength: 10,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    letterSpacing: 1.2,
                  ),
                  decoration: InputDecoration(
                    hintText: 'Enter your 10-digit RFID number',
                    hintStyle: const TextStyle(color: Colors.white38),
                    counterText: '', // Hide the character counter.
                    filled: false,
                    prefixIcon: const Icon(
                      Icons.credit_card,
                      color: Colors.white54,
                    ),
                    suffixIcon: _controller.text.isNotEmpty
                        ? IconButton(
                            icon: Icon(
                              _isValid ? Icons.check_circle : Icons.cancel,
                              color: _isValid
                                  ? Colors.green
                                  : Colors.red
                                      .withAlpha(179), // 0.7 * 255 = ~179
                            ),
                            onPressed: () {
                              _controller.clear();
                              setState(() => _isValid = false);
                            },
                          )
                        : null,
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(
                      vertical: 16,
                    ),
                  ),
                  onChanged: _validateRfid,
                ),
              ),

              // Status message with animation
              AnimatedCrossFade(
                firstChild: _controller.text.isNotEmpty && !_isValid
                    ? Padding(
                        padding: const EdgeInsets.only(
                          top: 8.0,
                          left: 4.0,
                        ),
                        child: Row(
                          children: const [
                            Icon(
                              Icons.error_outline,
                              color: Colors.red,
                              size: 16,
                            ),
                            SizedBox(width: 8),
                            Text(
                              'Please enter a valid 10-digit RFID number',
                              style: TextStyle(
                                color: Colors.red,
                                fontSize: 13,
                              ),
                            ),
                          ],
                        ),
                      )
                    : const SizedBox.shrink(),
                secondChild: _controller.text.isNotEmpty && _isValid
                    ? Padding(
                        padding: const EdgeInsets.only(
                          top: 8.0,
                          left: 4.0,
                        ),
                        child: Row(
                          children: const [
                            Icon(
                              Icons.check_circle,
                              color: Colors.green,
                              size: 16,
                            ),
                            SizedBox(width: 8),
                            Text(
                              'Valid RFID Number',
                              style: TextStyle(
                                color: Colors.green,
                                fontSize: 13,
                              ),
                            ),
                          ],
                        ),
                      )
                    : const SizedBox.shrink(),
                crossFadeState: _isValid
                    ? CrossFadeState.showSecond
                    : CrossFadeState.showFirst,
                duration: const Duration(milliseconds: 200),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Main content area - using Expanded to push button to bottom
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Information about RFID cards with improved design
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: const Color(0xFF2A2A2A),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: const Color(0xFF3A3A3A),
                        width: 1,
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: const [
                            Icon(
                              Icons.info_outline,
                              color: Colors.blue,
                              size: 20,
                            ),
                            SizedBox(width: 8),
                            Text(
                              'About RFID Cards',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        const Text(
                          'Linking your RFID card allows you to start charging sessions without using the app. Simply tap your card on compatible charging stations.',
                          style: TextStyle(
                            color: Colors.white70,
                            height: 1.5,
                          ),
                        ),
                        const SizedBox(height: 16),
                        // Added benefits list
                        ...[
                          'Quick authentication',
                          'Contactless payment',
                          'Secure transactions',
                        ].map(
                          (benefit) => Padding(
                            padding: const EdgeInsets.only(
                              bottom: 8.0,
                            ),
                            child: Row(
                              children: [
                                const Icon(
                                  Icons.check_circle,
                                  color: Colors.green,
                                  size: 16,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  benefit,
                                  style: const TextStyle(
                                    color: Colors.white70,
                                    fontSize: 14,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Added FAQ section
                  const Text(
                    'Frequently Asked Questions',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  _buildFaqItem(
                    'Where can I find my RFID number?',
                    'Your RFID number is printed on the back of your card, usually a 10-digit number.',
                  ),
                  _buildFaqItem(
                    'Can I link multiple cards?',
                    'Yes, you can link multiple RFID cards to your account from the cards management section.',
                  ),
                  _buildFaqItem(
                    'What if I lose my card?',
                    'You can deactivate your card immediately from the app to prevent unauthorized use.',
                  ),
                  // Add padding at the bottom to ensure content doesn't get cut off
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),

          // Link Card Button - now outside the scrollable area and at the bottom
          SizedBox(
            width: double.infinity,
            height: 54,
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                boxShadow: _isValid && !_isLoading
                    ? [
                        BoxShadow(
                          color:
                              Colors.green.withAlpha(102), // 0.4 * 255 = ~102
                          blurRadius: 8,
                          spreadRadius: 0,
                          offset: const Offset(0, 2),
                        ),
                      ]
                    : [],
              ),
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor:
                      _isValid ? Colors.green : Colors.grey.shade700,
                  foregroundColor: Colors.black,
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                onPressed: _isLoading || !_isValid ? null : _onLinkCard,
                child: _isLoading
                    ? const SizedBox(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(
                          color: Colors.black,
                          strokeWidth: 3,
                        ),
                      )
                    : Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.link,
                            color: _isValid ? Colors.black : Colors.white54,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Link Card',
                            style: TextStyle(
                              color: _isValid ? Colors.black : Colors.white54,
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// Helper method to build FAQ items
Widget _buildFaqItem(String question, String answer) {
  return Container(
    margin: const EdgeInsets.only(bottom: 12),
    decoration: BoxDecoration(
      color: const Color(0xFF2A2A2A),
      borderRadius: BorderRadius.circular(12),
      border: Border.all(color: const Color(0xFF3A3A3A), width: 1),
    ),
    child: ExpansionTile(
      title: Text(
        question,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 14,
          fontWeight: FontWeight.w500,
        ),
      ),
      iconColor: Colors.white,
      collapsedIconColor: Colors.white70,
      childrenPadding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      children: [
        Text(
          answer,
          style: const TextStyle(
            color: Colors.white70,
            fontSize: 14,
            height: 1.5,
          ),
        ),
      ],
    ),
  );
}
