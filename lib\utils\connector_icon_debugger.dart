import 'package:flutter/foundation.dart' show debugPrint;

/// A utility class to debug connector icon issues
class ConnectorIconDebugger {
  /// Trace connector icon URLs through the app
  static void traceConnectorIcons(dynamic data, {String source = 'Unknown'}) {
    try {
      debugPrint('=== CONNECTOR ICON TRACE (Source: $source) ===');
      
      // Handle different data types
      if (data is Map<String, dynamic>) {
        _traceMapData(data, source: source);
      } else if (data is List) {
        _traceListData(data, source: source);
      } else {
        debugPrint('Unsupported data type for icon tracing: ${data.runtimeType}');
      }
      
      debugPrint('=== END CONNECTOR ICON TRACE ===');
    } catch (e) {
      debugPrint('Error tracing connector icons: $e');
    }
  }
  
  /// Trace connector icons in a map
  static void _traceMapData(Map<String, dynamic> data, {String source = 'Unknown'}) {
    // Check for direct connector data
    if (data.containsKey('icon') || 
        data.containsKey('connector_image') || 
        data.containsKey('imageUrl')) {
      
      final String? iconUrl = data['icon'] ?? data['connector_image'] ?? data['imageUrl'];
      final String connectorType = data['type'] ?? data['connectorType'] ?? 'Unknown';
      
      debugPrint('Found connector of type: $connectorType');
      if (iconUrl != null && iconUrl.isNotEmpty) {
        debugPrint('✅ Icon URL found: $iconUrl');
      } else {
        debugPrint('❌ No icon URL found for connector type: $connectorType');
      }
    }
    
    // Check for evses structure
    if (data.containsKey('evses')) {
      debugPrint('Found evses structure, checking for connectors...');
      final evses = data['evses'];
      
      if (evses is Map<String, dynamic>) {
        evses.forEach((key, value) {
          if (value is Map<String, dynamic> && value.containsKey('connectors')) {
            final connectors = value['connectors'];
            if (connectors is List) {
              debugPrint('Found ${connectors.length} connectors in EVSE $key');
              _traceListData(connectors, source: 'EVSE $key');
            }
          }
        });
      } else if (evses is List) {
        _traceListData(evses, source: 'EVSEs List');
      }
    }
    
    // Check for connectors array
    if (data.containsKey('connectors')) {
      final connectors = data['connectors'];
      if (connectors is List) {
        debugPrint('Found ${connectors.length} connectors in direct connectors array');
        _traceListData(connectors, source: 'Direct Connectors');
      }
    }
    
    // Check for data field (common in API responses)
    if (data.containsKey('data')) {
      final responseData = data['data'];
      if (responseData is Map<String, dynamic>) {
        _traceMapData(responseData, source: '$source.data');
      } else if (responseData is List) {
        _traceListData(responseData, source: '$source.data');
      }
    }
  }
  
  /// Trace connector icons in a list
  static void _traceListData(List data, {String source = 'Unknown'}) {
    for (int i = 0; i < data.length; i++) {
      final item = data[i];
      if (item is Map<String, dynamic>) {
        // Check if this item is a connector
        if (item.containsKey('type') || item.containsKey('connectorType')) {
          final String? iconUrl = item['icon'] ?? item['connector_image'] ?? item['imageUrl'];
          final String connectorType = item['type'] ?? item['connectorType'] ?? 'Unknown';
          
          debugPrint('[$source] Item $i: Connector of type: $connectorType');
          if (iconUrl != null && iconUrl.isNotEmpty) {
            debugPrint('[$source] Item $i: ✅ Icon URL found: $iconUrl');
          } else {
            debugPrint('[$source] Item $i: ❌ No icon URL found for connector type: $connectorType');
          }
        }
        
        // Recursively check this item
        _traceMapData(item, source: '$source[$i]');
      }
    }
  }
}
