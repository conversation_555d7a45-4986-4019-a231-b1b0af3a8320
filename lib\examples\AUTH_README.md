# EcoPlug Authentication API Guide

This guide explains how to use the simplified `AuthApiService` for handling login and OTP verification in the EcoPlug app.

## Overview

The `AuthApiService` provides a simple, centralized way to handle authentication with the EcoPlug API. It includes methods for:

- Sending OTP to a phone number
- Verifying OTP
- Checking if a user is logged in
- Getting user data
- Logging out

## How to Use

### 1. Create an instance of the service

```dart
final AuthApiService _authService = AuthApiService();
```

### 2. Send OTP

```dart
// Call the API service to send OTP
final response = await _authService.sendOtp(phoneNumber);

// Handle the response
if (response['success'] == true) {
  // OTP sent successfully
  // Show OTP input field
} else {
  // OTP sending failed
  // Show error message
  final errorMessage = response['message'] ?? 'Failed to send OTP';
}
```

### 3. Verify OTP

```dart
// Call the API service to verify OTP
final response = await _authService.verifyOtp(phoneNumber, otp);

// Handle the response
if (response['success'] == true) {
  // OTP verified successfully
  
  // Check if user is new
  final userData = response['user'];
  final bool isNewUser = userData['name'] == null || 
                        userData['name'] == '' || 
                        userData['email'] == null || 
                        userData['email'] == '';
  
  // Navigate to the appropriate screen
  if (isNewUser) {
    // Navigate to onboarding screen
  } else {
    // Navigate to home screen
  }
} else {
  // OTP verification failed
  // Show error message
  final errorMessage = response['message'] ?? 'Failed to verify OTP';
}
```

### 4. Check if user is logged in

```dart
final bool isLoggedIn = await _authService.isLoggedIn();

if (isLoggedIn) {
  // User is logged in
  // Navigate to home screen
} else {
  // User is not logged in
  // Navigate to login screen
}
```

### 5. Check if user is new

```dart
final bool isNewUser = await _authService.isNewUser();

if (isNewUser) {
  // User is new
  // Navigate to onboarding screen
} else {
  // User is not new
  // Navigate to home screen
}
```

### 6. Get user data

```dart
final userData = await _authService.getUserData();

if (userData != null) {
  // Use user data
  final String? name = userData['name'];
  final String? email = userData['email'];
  final String? phoneNumber = userData['mobile_number'];
}
```

### 7. Logout

```dart
await _authService.logout();
// Navigate to login screen
```

## Response Format

### Send OTP Response

```json
{
  "success": true,
  "message": "OTP sent successfully"
}
```

### Verify OTP Response

```json
{
  "success": true,
  "message": "OTP verified successfully",
  "user": {
    "id": 1234,
    "uid": "user-uid",
    "mobile_number": "1234567890",
    "name": "User Name",
    "email": "<EMAIL>",
    "domain": "eeil.online",
    "token": "auth-token"
  }
}
```

## Error Handling

The service handles errors and returns a response with `success: false` and an error message:

```json
{
  "success": false,
  "message": "Error message"
}
```

## Special Cases

The EcoPlug API returns "OTP Verify" as the message for successful verification, which might be confusing. The service handles this special case and converts it to a proper success response.

## Complete Example

See the `auth_example.dart` file for a complete example of how to use the `AuthApiService` in a Flutter screen.
