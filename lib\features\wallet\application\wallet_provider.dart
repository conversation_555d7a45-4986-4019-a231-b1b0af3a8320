import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../models/wallet/wallet_model.dart';
import '../../../models/wallet/transaction_model.dart';
import '../../../services/wallet_service.dart';
import '../domain/wallet_state.dart';

/// Provider for wallet state management
final StateNotifierProvider<WalletNotifier, AsyncValue<WalletState>>
    walletProvider =
    StateNotifierProvider<WalletNotifier, AsyncValue<WalletState>>((ref) {
  final walletService = WalletService();
  return WalletNotifier(walletService);
});

/// Wallet state notifier
class WalletNotifier extends StateNotifier<AsyncValue<WalletState>> {
  final WalletService _walletService;

  WalletNotifier(this._walletService) : super(const AsyncValue.loading()) {
    // Initialize the wallet state by fetching data
    fetchWallet();
  }

  /// Fetch wallet data - always fetches fresh data from the server without caching
  /// This method is called when the wallet screen is shown
  Future<void> fetchWallet() async {
    // Clear any previous state to ensure we always show fresh data
    state = const AsyncValue.loading();

    try {
      debugPrint('Fetching wallet data from API...');
      final walletData = await _walletService.getWalletInfo();

      if (walletData != null) {
        // Create a wallet model from the wallet info
        final wallet = WalletModel(
          balance: walletData.balance,
          rewardPoints: walletData.rewardPoints,
          lastUpdated: DateTime.now(),
          currency: 'INR',
        );

        // Convert Transaction to TransactionModel
        final transactions = walletData.recentTransactions
            .map((transaction) => TransactionModel(
                  id: transaction.id,
                  title: transaction.title,
                  description: transaction.description,
                  amount: transaction.amount,
                  timestamp: transaction.timestamp,
                  type: transaction.type,
                  status: transaction.status,
                  transactionReference: transaction.transactionReference,
                ))
            .toList();

        debugPrint(
            'Successfully fetched wallet data with balance: ${wallet.balance}');

        // Update state with loaded data
        state = AsyncValue.data(
          WalletState.loaded(
            wallet: wallet,
            transactions: transactions,
          ),
        );
      } else {
        debugPrint('Wallet data is null');
        state = AsyncValue.error(
          'Failed to fetch wallet data',
          StackTrace.current,
        );
      }
    } catch (e, stackTrace) {
      debugPrint('Error fetching wallet: $e');
      state = AsyncValue.error(e.toString(), stackTrace);
    }
  }

  /// Add money to wallet
  Future<void> addMoney(double amount) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      try {
        final response = await _walletService.addMoney(amount);

        if (response.success) {
          // Fetch updated wallet data
          return await _fetchUpdatedWallet();
        }

        return WalletState.error(response.message);
      } catch (e) {
        debugPrint('Error adding money to wallet: $e');
        return WalletState.error(e.toString());
      }
    });
  }

  /// Helper method to fetch updated wallet data
  Future<WalletState> _fetchUpdatedWallet() async {
    try {
      debugPrint('Fetching updated wallet data from API...');
      final walletData = await _walletService.getWalletInfo();

      if (walletData != null) {
        // Create a wallet model from the wallet info
        final wallet = WalletModel(
          balance: walletData.balance,
          rewardPoints: walletData.rewardPoints,
          lastUpdated: DateTime.now(),
          currency: 'INR',
        );

        // Convert Transaction to TransactionModel
        final transactions = walletData.recentTransactions
            .map((transaction) => TransactionModel(
                  id: transaction.id,
                  title: transaction.title,
                  description: transaction.description,
                  amount: transaction.amount,
                  timestamp: transaction.timestamp,
                  type: transaction.type,
                  status: transaction.status,
                  transactionReference: transaction.transactionReference,
                ))
            .toList();

        debugPrint(
            'Successfully fetched updated wallet data with balance: ${wallet.balance}');

        return WalletState.loaded(
          wallet: wallet,
          transactions: transactions,
        );
      }

      debugPrint('Updated wallet data is null');
      return WalletState.error('Failed to fetch updated wallet data');
    } catch (e) {
      debugPrint('Error fetching updated wallet: $e');
      return WalletState.error(e.toString());
    }
  }
}
