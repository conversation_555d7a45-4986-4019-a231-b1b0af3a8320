// Station model for the app
import 'package:flutter/foundation.dart';
import 'evse.dart';

class Station {
  final String id;
  final String name;
  final String address;
  final String? city;
  final String? state;
  final List<String> images;
  final List<Evse> evses;
  final double latitude;
  final double longitude;
  final double distance;
  final String status;
  final double rating;
  final int reviews;
  final List<Connector> connectors;

  // This constructor is replaced by the more robust one below
  final String? mapPinUrl;
  final String? focusedMapPinUrl;
  final List<dynamic>? types;
  final String? uid; // UID from the API, required for station details
  final String? openingTimes;
  final bool? openStatus;

  bool get isAvailable => status == 'Available';
  String get connectorType =>
      connectors.isNotEmpty ? connectors[0].type : 'Unknown';
  int get availableConnectors =>
      connectors.fold(0, (sum, connector) => sum + connector.availableGuns);

  // Helper method to get station ID
  int get stationId => int.tryParse(id) ?? 0;

  // Helper method to get connector types as a string
  String getConnectorTypesString() {
    if (connectors.isEmpty) return 'Various';

    final Set<String> uniqueTypes = {};
    for (var connector in connectors) {
      if (connector.name.isNotEmpty) {
        uniqueTypes.add(connector.name);
      } else if (connector.type.isNotEmpty) {
        uniqueTypes.add(connector.type);
      }
    }

    return uniqueTypes.isEmpty ? 'Various' : uniqueTypes.join(', ');
  }

  Station({
    required this.id,
    required this.name,
    required this.address,
    this.city,
    this.state,
    required this.images,
    required this.evses,
    required this.latitude,
    required this.longitude,
    required this.distance,
    required this.status,
    required this.rating,
    required this.reviews,
    required this.connectors,
    this.mapPinUrl,
    this.focusedMapPinUrl,
    this.types,
    this.uid, // UID from the API, required for station details
    this.openingTimes,
    this.openStatus,
  });

  factory Station.fromJson(Map<String, dynamic> json) {
    List<Connector> connectors = [];

    // Extract connectors from the nested 'evses' structure
    if (json['evses'] != null && json['evses'] is Map) {
      final evsesMap = json['evses'] as Map<String, dynamic>;
      evsesMap.forEach((key, evseData) {
        if (evseData != null &&
            evseData is Map<String, dynamic> &&
            evseData['connectors'] != null &&
            evseData['connectors'] is List) {
          final List<dynamic> connectorList = evseData['connectors'];
          try {
            connectors.addAll(connectorList
                .whereType<Map<String, dynamic>>()
                .map((cJson) => Connector.fromJson(cJson)));

            // Add debug info about connectors found in this EVSE
            if (connectorList.isNotEmpty) {
              debugPrint(
                  'Found ${connectorList.length} connectors in EVSE $key');
            }
          } catch (e) {
            debugPrint('Error parsing connectors in EVSE $key: $e');
          }
        }
      });

      // Log if no connectors were found in evses
      if (connectors.isEmpty) {
        debugPrint(
            "No connectors found in EVSEs for station ${json['id'] ?? json['station_id']}");
      }
    }

    // Fallback to previous logic if 'evses' is not present or not a map
    if (connectors.isEmpty) {
      dynamic connectorData = json['connectors'] ?? json['types'];
      if (connectorData != null) {
        try {
          if (connectorData is List) {
            connectors = connectorData
                .whereType<Map<String, dynamic>>()
                .map((e) => Connector.fromJson(e))
                .toList();
            debugPrint(
                'Found ${connectors.length} connectors in direct connectors array');
          } else if (connectorData is Map) {
            connectors = connectorData.values
                .whereType<Map<String, dynamic>>()
                .map((e) => Connector.fromJson(e))
                .toList();
            debugPrint(
                'Found ${connectors.length} connectors in connector map');
          }
        } catch (e) {
          debugPrint('Error parsing connector data: $e');
        }
      }
    }

    // REMOVED: No default connectors - only display stations with real connector data
    // If no connectors are found, the station should not be displayed
    if (connectors.isEmpty) {
      debugPrint(
          "❌ CRITICAL: No connectors found for station ${json['id'] ?? json['station_id'] ?? 'unknown'} - station will be excluded from display");
      throw FormatException('Station has no connector data from API');
    }

    // Parse images with improved handling
    List<String> imagesList = [];
    if (json['images'] != null) {
      if (json['images'] is List) {
        imagesList = List<String>.from(json['images'].map((x) => x.toString()));
      } else if (json['images'] is String &&
          json['images'].toString().isNotEmpty) {
        imagesList = [json['images'].toString()];
      }
    } else if (json['imageUrl'] != null &&
        json['imageUrl'].toString().isNotEmpty) {
      // Handle imageUrl field as alternative
      imagesList = [json['imageUrl'].toString()];
    }

    // Debug log for images
    if (imagesList.isNotEmpty) {
      debugPrint(
          'Found ${imagesList.length} images for station ${json['id'] ?? json['station_id']}');
    } else {
      debugPrint(
          'No images found for station ${json['id'] ?? json['station_id']}');
      // Add a default image if none found
      imagesList = ['https://api2.eeil.online/uploads/ev-banner2.png'];
    }

    // Parse evses
    List<Evse> evsesList = [];
    if (json['evses'] != null) {
      if (json['evses'] is List) {
        try {
          evsesList = List<Evse>.from(json['evses']
              .map((x) => Evse.fromJson(x as Map<String, dynamic>)));
        } catch (e) {
          debugPrint('Error parsing evses: $e');
        }
      }
    }

    // Extract UID directly from the API response
    String? uid = json['uid']?.toString();

    // Log UID extraction for debugging
    if (uid == null || uid.isEmpty) {
      debugPrint(
          'WARNING: Station.fromJson missing UID for station: ${json['name']}');
      debugPrint('Available fields: ${json.keys.join(', ')}');

      // For station list and details pages, we need a valid UID
      // For marker API, we don't need a UID
      if (json['source'] == 'station_list' ||
          json['source'] == 'station_details') {
        throw FormatException(
            'INVALID_UID: Missing UID for station ${json['name']}');
      }
    } else {
      // Validate UUID format
      final RegExp uuidRegex = RegExp(
        r'^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$',
        caseSensitive: false,
      );

      if (!uuidRegex.hasMatch(uid)) {
        debugPrint(
            'WARNING: Invalid UID format: $uid for station: ${json['name']}');

        // For station list and details pages, we need a valid UID format
        if (json['source'] == 'station_list' ||
            json['source'] == 'station_details') {
          throw FormatException(
              'INVALID_UID_FORMAT: Invalid UID format for station ${json['name']}');
        }
      } else {
        debugPrint(
            'Successfully extracted valid UID: $uid for station: ${json['name']}');
      }
    }

    // CRITICAL VALIDATION: Ensure required fields are present
    if (json['station_id'] == null && json['id'] == null) {
      throw FormatException('Station ID is missing from API response');
    }
    if (json['name'] == null || json['name'].toString().trim().isEmpty) {
      throw FormatException('Station name is missing from API response');
    }
    if (json['address'] == null || json['address'].toString().trim().isEmpty) {
      throw FormatException('Station address is missing from API response');
    }
    if (json['latitude'] == null || json['longitude'] == null) {
      throw FormatException(
          'Station coordinates are missing from API response');
    }

    return Station(
      id: json['station_id']?.toString() ?? json['id']?.toString() ?? '',
      name: json['name'].toString().trim(),
      address: json['address'].toString().trim(),
      city: json['city']?.toString(),
      state: json['state']?.toString(),
      images: imagesList,
      evses: evsesList,
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      distance: (json['distance'] as num?)?.toDouble() ?? 0.0,
      // CRITICAL: No default status - only real API data
      status: json['status']?.toString() ?? '',
      // CRITICAL: No default ratings - only real API data (0.0 when API provides null)
      rating: (json['rate'] as num?)?.toDouble() ??
          (json['rating'] as num?)?.toDouble() ??
          0.0,
      // CRITICAL: No default review counts - only real API data (0 when API provides null)
      reviews:
          json['rate_total'] ?? json['reviews'] ?? json['reviewCount'] ?? 0,
      connectors: connectors,
      mapPinUrl: json['map_pin_url']?.toString(),
      focusedMapPinUrl: json['focused_map_pin_url']?.toString(),
      types: json['types'],
      uid: uid, // Use the extracted UID
      openingTimes: json['opening_times']?.toString(),
      openStatus: json['open_status'] as bool?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'address': address,
      'city': city,
      'latitude': latitude,
      'longitude': longitude,
      'distance': distance,
      'status': status,
      'rating': rating,
      'reviews': reviews,
      'connectors': connectors.map((c) => c.toJson()).toList(),
      'map_pin_url': mapPinUrl,
      'focused_map_pin_url': focusedMapPinUrl,
      'types': types,
      'uid': uid,
      'opening_times': openingTimes,
      'open_status': openStatus,
    };
  }

  // We no longer use fallback stations - removed Station.getFallback() method

  /// Create an empty station
  factory Station.empty() {
    return Station(
      id: '',
      name: '',
      address: '',
      latitude: 0.0,
      longitude: 0.0,
      distance: 0.0,
      status: '',
      rating: 0.0,
      reviews: 0,
      images: [],
      evses: [],
      connectors: [],
      uid: null,
      openingTimes: '',
      openStatus: false,
    );
  }
}

class Connector {
  final String id;
  final String name;
  final String type;
  final double? price;
  final String? power; // Nullable to match API data
  final int? totalGuns;
  String? iconUrl; // URL for connector icon image
  final int availableGuns;
  final String? icon;
  final String? status;
  final dynamic maxElectricPower; // Can be int, double, or string from API
  final String? standard;
  final String? priceLabel;
  final int? pricePerUnit;
  final int? soc;
  final String? powerOutput;
  final int? maxPower;
  String? evsesUid; // Made mutable to allow setting EVSE UID after creation

  Connector({
    required this.id,
    required this.name,
    required this.type,
    this.price,
    this.power,
    this.totalGuns,
    this.availableGuns = 0, // Default to 0 instead of required
    this.icon,
    this.status,
    this.maxElectricPower,
    this.standard,
    this.priceLabel,
    this.pricePerUnit,
    this.soc,
    this.powerOutput,
    this.maxPower,
    this.evsesUid, // UID of the EVSE this connector belongs to
    this.iconUrl, // URL for connector icon image
  });

  factory Connector.fromJson(Map<String, dynamic> json) {
    // Handle both API and local data formats
    String connectorType = '';

    // Try to get connector type from different possible fields
    if (json['type'] != null && json['type'].toString().isNotEmpty) {
      connectorType = json['type'].toString();
    } else if (json['connectorType'] != null &&
        json['connectorType'].toString().isNotEmpty) {
      connectorType = json['connectorType'].toString();
    } else if (json['standard'] != null &&
        json['standard'].toString().isNotEmpty) {
      // Standard field often contains connector type in API responses (e.g. "IEC_62196_T2_COMBO" for CCS2)
      final standard = json['standard'].toString();
      if (standard.contains('COMBO')) {
        connectorType = 'CCS2';
      } else if (standard.contains('T2')) {
        connectorType = 'Type 2 AC';
      } else if (standard.contains('CHADEMO')) {
        connectorType = 'CHAdeMO';
      } else {
        connectorType = standard;
      }
    } else {
      // CRITICAL: No default connector type - throw error if missing
      throw FormatException('Connector type is missing from API response');
    }

    // Handle price from various formats
    double? connectorPrice;
    if (json['price'] != null) {
      if (json['price'] is int || json['price'] is double) {
        connectorPrice = (json['price'] as num).toDouble();
      } else {
        try {
          connectorPrice = double.tryParse(json['price'].toString());
        } catch (e) {
          debugPrint('Error parsing price: $e');
        }
      }
    } else if (json['cost'] != null && json['cost'] is String) {
      try {
        connectorPrice = double.tryParse((json['cost'] as String)
            .replaceAll('₹', '')
            .replaceAll('/kWh', ''));
      } catch (e) {
        debugPrint('Error parsing cost: $e');
      }
    }

    // Get power output in a standardized format
    String powerOutput = '';
    if (json['power'] != null && json['power'].toString().isNotEmpty) {
      powerOutput = json['power'].toString();
    } else if (json['max_electric_power'] != null) {
      final maxPower = json['max_electric_power'];
      if (maxPower is int || maxPower is double) {
        powerOutput = '${maxPower.toString()} kW';
      } else {
        powerOutput = maxPower.toString();
      }
    } else {
      // CRITICAL: No default power output - only use real API data
      powerOutput = ''; // Empty string when API provides no power data
    }

    // Get icon URL from various possible fields
    String? iconUrl;
    if (json['icon'] != null && json['icon'].toString().isNotEmpty) {
      iconUrl = json['icon'].toString().trim();
      debugPrint('Found icon URL in icon field: $iconUrl');
    } else if (json['icon_url'] != null &&
        json['icon_url'].toString().isNotEmpty) {
      iconUrl = json['icon_url'].toString().trim();
      debugPrint('Found icon URL in icon_url field: $iconUrl');
    } else if (json['image_url'] != null &&
        json['image_url'].toString().isNotEmpty) {
      iconUrl = json['image_url'].toString().trim();
      debugPrint('Found icon URL in image_url field: $iconUrl');
    }
    if (json['connector_image'] != null &&
        json['connector_image'].toString().isNotEmpty) {
      iconUrl = json['connector_image'].toString();
    } else if (json['imageUrl'] != null &&
        json['imageUrl'].toString().isNotEmpty) {
      iconUrl = json['imageUrl'].toString();
    }

    // Debug log for icon URL
    if (iconUrl != null) {
      debugPrint(
          'Found connector icon URL: $iconUrl for connector type: $connectorType');
    } else {
      debugPrint('No icon URL found for connector type: $connectorType');

      // Make a direct API call to get the connector icon
      // This ensures we always use real-time data from the API
      final normalizedType = connectorType.toLowerCase().replaceAll(' ', '');

      // Use the API connector icon URL based on connector type
      if (normalizedType.contains('ccs') || normalizedType.contains('combo')) {
        iconUrl = 'https://api2.eeil.online/uploads/connector_type/ccs2.svg';
      } else if (normalizedType.contains('type2')) {
        iconUrl = 'https://api2.eeil.online/uploads/connector_type/type2.svg';
      } else if (normalizedType.contains('chademo')) {
        iconUrl = 'https://api2.eeil.online/uploads/connector_type/chademo.svg';
      } else if (normalizedType.contains('gb/t') ||
          normalizedType.contains('gbt')) {
        iconUrl = 'https://api2.eeil.online/uploads/connector_type/gbt.svg';
      } else if (normalizedType.contains('tesla')) {
        iconUrl = 'https://api2.eeil.online/uploads/connector_type/tesla.svg';
      } else if (normalizedType.contains('type1')) {
        iconUrl = 'https://api2.eeil.online/uploads/connector_type/type1.svg';
      } else {
        // Use a direct API endpoint to get the connector icon
        iconUrl = 'https://api2.eeil.online/uploads/connector_type/default.svg';
      }

      debugPrint(
          'Using API connector icon URL: $iconUrl for connector type: $connectorType');
    }

    // CRITICAL VALIDATION: Ensure connector has essential data
    if ((json['id'] == null && json['connector_id'] == null) ||
        connectorType.isEmpty) {
      throw FormatException('Connector missing essential data (ID or type)');
    }

    return Connector(
      id: json['id'] ?? json['connector_id']?.toString() ?? '',
      name: json['name'] ??
          json['connectorName'] ??
          json['label'] ??
          connectorType,
      type: connectorType,
      price: connectorPrice,
      power: powerOutput,
      totalGuns: json['total_guns'] is int ? json['total_guns'] : null,
      // Use available guns from API or default to 0
      availableGuns: json['availableGuns'] ?? json['available_guns'] ?? 0,
      icon: iconUrl,
      status: json['status'],
      maxElectricPower: json['max_electric_power'], // Accept any type from API
      standard: json['standard'],
      priceLabel: json['price_label'],
      pricePerUnit:
          json['price_per_unit'] is int ? json['price_per_unit'] : null,
      soc: json['soc'] is int ? json['soc'] : null,
      powerOutput:
          json['power_type']?.toString() ?? json['power_output']?.toString(),
      maxPower: json['max_power'] is int ? json['max_power'] : null,
      evsesUid: json['evses_uid']?.toString(),
      iconUrl: iconUrl,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type,
      'price': price,
      'power': power,
      'totalGuns': totalGuns,
      'availableGuns': availableGuns,
      'icon': icon,
      'status': status,
      'max_electric_power': maxElectricPower,
      'standard': standard,
      'price_label': priceLabel,
      'price_per_unit': pricePerUnit,
      'soc': soc,
      'power_type': powerOutput,
      'power_output': powerOutput, // Keep for backward compatibility
      'max_power': maxPower,
      'evses_uid': evsesUid,
    };
  }
}
