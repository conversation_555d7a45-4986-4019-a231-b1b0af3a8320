import 'dart:convert';
import 'dart:io';

void main() async {
  // Test the station pagination API
  await testStationPaginationAPI();
}

Future<void> testStationPaginationAPI() async {
  try {
    print('Testing station pagination API...');
    
    // Create HTTP client
    final client = HttpClient();
    
    // Create request
    final request = await client.getUrl(
      Uri.parse('https://api2.eeil.online/api/v1/user/station/paginate?page=1&limit=10')
    );
    
    // Add headers (you might need to add authorization token)
    request.headers.add('Content-Type', 'application/json');
    request.headers.add('Accept', 'application/json');
    
    // Send request
    final response = await request.close();
    
    print('Response status: ${response.statusCode}');
    
    // Read response
    final responseBody = await response.transform(utf8.decoder).join();
    print('Response body: $responseBody');
    
    // Parse JSON
    if (response.statusCode == 200) {
      final jsonData = json.decode(responseBody);
      print('Parsed JSON: $jsonData');
      
      if (jsonData is Map<String, dynamic>) {
        print('Success: ${jsonData['success']}');
        print('Message: ${jsonData['message']}');
        if (jsonData['data'] != null) {
          print('Data type: ${jsonData['data'].runtimeType}');
          if (jsonData['data'] is List) {
            print('Number of stations: ${(jsonData['data'] as List).length}');
          }
        }
      }
    } else {
      print('API call failed with status: ${response.statusCode}');
    }
    
    client.close();
  } catch (e) {
    print('Error testing API: $e');
  }
}
