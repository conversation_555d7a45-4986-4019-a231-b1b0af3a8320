import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../models/api_response.dart';
import '../../services/service_locator.dart';
import '../../services/sync_service.dart';
import '../../utils/error_handler.dart';
import '../../widgets/navigation_bar.dart';

class UserOnboardingScreen extends StatefulWidget {
  final String userId;
  final String phoneNumber;
  final String token;

  const UserOnboardingScreen({
    super.key,
    required this.userId,
    required this.phoneNumber,
    required this.token,
  });

  @override
  UserOnboardingScreenState createState() => UserOnboardingScreenState();
}

class UserOnboardingScreenState extends State<UserOnboardingScreen>
    with SingleTickerProviderStateMixin {
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  late AnimationController _animationController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;

  bool _isLoading = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    // Create slide animation
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.2),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOutQuint,
      ),
    );

    // Create fade animation
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeIn,
      ),
    );

    // Start the animation
    _animationController.forward();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  // Validate email format
  bool _isValidEmail(String email) {
    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );
    return emailRegex.hasMatch(email);
  }

  // Submit user profile
  Future<void> _submitProfile() async {
    // Validate form
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Show loading state
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    // Hide keyboard
    FocusScope.of(context).unfocus();

    // Show a temporary snackbar to indicate the request is being processed
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Row(
          children: [
            SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                color: Colors.white,
              ),
            ),
            SizedBox(width: 16),
            Text('Saving your profile...'),
          ],
        ),
        backgroundColor: const Color(0xFF34C759),
        duration: const Duration(seconds: 1),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        margin: const EdgeInsets.all(10),
      ),
    );

    // Always save user data to shared preferences first to avoid data loss
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('user_name', _nameController.text);
    await prefs.setString('user_email', _emailController.text);
    await prefs.setString('user_id', widget.userId);
    await prefs.setString('user_token', widget.token);
    await prefs.setString(
        'user_phone', widget.phoneNumber); // Save phone number

    // Queue the profile update for syncing
    final syncService = SyncService();
    await syncService.queueProfileUpdate(
      userId: widget.userId,
      name: _nameController.text,
      email: _emailController.text,
      phone: widget.phoneNumber,
    );

    // Save token to API service for future requests
    final apiService = ServiceLocator().apiService;
    await apiService.saveToken(widget.token);
    debugPrint('\n=== SAVED TOKEN TO API SERVICE ===');
    debugPrint('Token: ${widget.token}');

    // Implement retry mechanism for network issues
    int maxRetries = 3;
    int currentRetry = 0;
    ApiResponse<bool>? response;

    while (currentRetry <= maxRetries) {
      try {
        final userRepository = ServiceLocator().userRepository;

        // Log the data being sent
        debugPrint(
            '\n=== SUBMITTING PROFILE DATA (Attempt ${currentRetry + 1}) ===');
        debugPrint('User ID: ${widget.userId}');
        debugPrint('Name: ${_nameController.text}');
        debugPrint('Email: ${_emailController.text}');
        debugPrint('Token: ${widget.token}');

        // Update user profile
        response = await userRepository.updateProfile(
          widget.userId,
          _nameController.text,
          _emailController.text,
        );

        // Log the response
        debugPrint('\n=== PROFILE UPDATE RESPONSE ===');
        debugPrint('Success: ${response.success}');
        debugPrint('Message: ${response.message}');

        // If successful, break out of the retry loop
        if (response.success) {
          break;
        }

        // If we get a non-network error, break out of the retry loop
        if (!response.message.contains('Network connection') &&
            !response.message.contains('Connection error') &&
            !response.message.contains('timed out') &&
            !response.message.contains('reset by peer')) {
          break;
        }

        // Otherwise, retry for network-related errors
        currentRetry++;
        if (currentRetry > maxRetries) {
          break;
        }

        // Wait before retrying
        await Future.delayed(Duration(seconds: 1 * currentRetry));
      } catch (e) {
        debugPrint('Error in profile update (Attempt ${currentRetry + 1}): $e');
        currentRetry++;
        if (currentRetry > maxRetries) {
          break;
        }
        await Future.delayed(Duration(seconds: 1 * currentRetry));
      }
    }

    // Check if we should proceed to dashboard despite errors
    bool proceedToDashboard = (response?.success ?? false) ||
        (response?.message != null &&
            response!.message.contains('Network connection')) ||
        (response?.message != null &&
            response!.message.contains('Connection error')) ||
        (response?.message != null &&
            response!.message.contains('timed out')) ||
        (response?.message != null &&
            response!.message.contains('reset by peer'));

    if (proceedToDashboard) {
      if (response != null && !response.success) {
        // Set pending update flag if there was an error
        await prefs.setString('pending_profile_update', 'true');

        // Show a toast message about network issues
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                  'Profile will be updated when you reconnect to the internet'),
              backgroundColor: Colors.orange,
              behavior: SnackBarBehavior.floating,
              duration: Duration(seconds: 3),
            ),
          );
        }

        debugPrint('Network error but continuing to dashboard');
      }

      // Navigate to home screen
      if (mounted) {
        Navigator.pushReplacement(
          context,
          PageRouteBuilder(
            pageBuilder: (context, animation, secondaryAnimation) =>
                const MainNavigation(),
            transitionsBuilder:
                (context, animation, secondaryAnimation, child) {
              const begin = Offset(1.0, 0.0);
              const end = Offset.zero;
              const curve = Curves.easeOutQuint;

              var tween =
                  Tween(begin: begin, end: end).chain(CurveTween(curve: curve));
              var offsetAnimation = animation.drive(tween);

              return SlideTransition(
                position: offsetAnimation,
                child: child,
              );
            },
            transitionDuration: const Duration(milliseconds: 500),
          ),
        );
      }
    } else {
      // Log the error details
      debugPrint('\n=== PROFILE UPDATE FAILED ===');
      debugPrint('Error message: ${response?.message ?? "Unknown error"}');

      setState(() {
        _isLoading = false;
        _errorMessage = ErrorHandler.getUserFriendlyMessage(
            response?.message ?? "Failed to update profile");
      });

      // Show error in snackbar for better visibility
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_errorMessage ?? 'Failed to update profile'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
    final screenSize = MediaQuery.of(context).size;
    final screenHeight = screenSize.height;
    final screenWidth = screenSize.width;

    return Scaffold(
      backgroundColor: Colors.white,
      body: Stack(
        children: [
          // Background circles
          Positioned(
            top: -screenWidth * 0.4,
            left: -screenWidth * 0.4,
            child: Container(
              width: screenWidth * 0.8,
              height: screenWidth * 0.8,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: const Color(0xFF34C759).withAlpha(13),
              ),
            ),
          ),
          Positioned(
            top: screenHeight * 0.1,
            right: -screenWidth * 0.25,
            child: Container(
              width: screenWidth * 0.5,
              height: screenWidth * 0.5,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: const Color(0xFF34C759).withAlpha(18),
              ),
            ),
          ),
          Positioned(
            bottom: -screenWidth * 0.35,
            right: -screenWidth * 0.35,
            child: Container(
              width: screenWidth * 0.7,
              height: screenWidth * 0.7,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: const Color(0xFF34C759).withAlpha(13),
              ),
            ),
          ),

          // Main content
          SafeArea(
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: ConstrainedBox(
                  constraints: BoxConstraints(
                    minHeight:
                        screenHeight - MediaQuery.of(context).padding.top,
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      const SizedBox(height: 10),

                      // Logo and title
                      FadeTransition(
                        opacity: _fadeAnimation,
                        child: Image.asset(
                          'assets/images/ecoplug_logo.png',
                          width: 80,
                          height: 80,
                          errorBuilder: (context, error, stackTrace) {
                            return const Icon(
                              Icons.electric_car,
                              size: 80,
                              color: Color(0xFF34C759),
                            );
                          },
                        ),
                      ),
                      const SizedBox(height: 10),

                      FadeTransition(
                        opacity: _fadeAnimation,
                        child: const Text(
                          'Welcome to Ecoplug!',
                          style: TextStyle(
                            fontSize: 28,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF34C759),
                          ),
                        ),
                      ),
                      const SizedBox(height: 8),

                      FadeTransition(
                        opacity: _fadeAnimation,
                        child: const Text(
                          'Let\'s set up your profile',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.grey,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      const SizedBox(height: 10),

                      // Profile form
                      SlideTransition(
                        position: _slideAnimation,
                        child: FadeTransition(
                          opacity: _fadeAnimation,
                          child: Form(
                            key: _formKey,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Phone number display
                                Container(
                                  width: double.infinity,
                                  margin: const EdgeInsets.only(bottom: 20),
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 16, vertical: 12),
                                  decoration: BoxDecoration(
                                    color:
                                        const Color(0xFF34C759).withAlpha(20),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Row(
                                    children: [
                                      Container(
                                        padding: const EdgeInsets.all(8),
                                        decoration: BoxDecoration(
                                          color: Colors.white,
                                          borderRadius:
                                              BorderRadius.circular(12),
                                          boxShadow: [
                                            BoxShadow(
                                              color: Colors.black.withAlpha(13),
                                              blurRadius: 4,
                                              offset: const Offset(0, 2),
                                            ),
                                          ],
                                        ),
                                        child: const Icon(
                                          Icons.phone,
                                          color: Color(0xFF34C759),
                                          size: 16,
                                        ),
                                      ),
                                      const SizedBox(width: 12),
                                      Text(
                                        '+91 ${widget.phoneNumber}',
                                        style: const TextStyle(
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),

                                // Name input
                                const Text(
                                  'Your Name',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                    color: Colors.black87,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Container(
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(16),
                                    border: Border.all(
                                      color:
                                          const Color(0xFF34C759).withAlpha(77),
                                      width: 1.5,
                                    ),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withAlpha(13),
                                        blurRadius: 10,
                                        offset: const Offset(0, 4),
                                      ),
                                    ],
                                  ),
                                  child: TextFormField(
                                    controller: _nameController,
                                    decoration: const InputDecoration(
                                      hintText: 'Enter your full name',
                                      hintStyle: TextStyle(
                                        color: Colors.grey,
                                      ),
                                      prefixIcon: Icon(
                                        Icons.person_outline,
                                        color: Color(0xFF34C759),
                                      ),
                                      border: InputBorder.none,
                                      contentPadding: EdgeInsets.symmetric(
                                        horizontal: 16,
                                        vertical: 16,
                                      ),
                                    ),
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'Please enter your name';
                                      }
                                      if (value.length < 3) {
                                        return 'Name must be at least 3 characters';
                                      }
                                      return null;
                                    },
                                  ),
                                ),
                                const SizedBox(height: 20),

                                // Email input
                                const Text(
                                  'Your Email',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                    color: Colors.black87,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Container(
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(16),
                                    border: Border.all(
                                      color:
                                          const Color(0xFF34C759).withAlpha(77),
                                      width: 1.5,
                                    ),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withAlpha(13),
                                        blurRadius: 10,
                                        offset: const Offset(0, 4),
                                      ),
                                    ],
                                  ),
                                  child: TextFormField(
                                    controller: _emailController,
                                    keyboardType: TextInputType.emailAddress,
                                    decoration: const InputDecoration(
                                      hintText: 'Enter your email address',
                                      hintStyle: TextStyle(
                                        color: Colors.grey,
                                      ),
                                      prefixIcon: Icon(
                                        Icons.email_outlined,
                                        color: Color(0xFF34C759),
                                      ),
                                      border: InputBorder.none,
                                      contentPadding: EdgeInsets.symmetric(
                                        horizontal: 16,
                                        vertical: 16,
                                      ),
                                    ),
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'Please enter your email';
                                      }
                                      if (!_isValidEmail(value)) {
                                        return 'Please enter a valid email address';
                                      }
                                      return null;
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),

                      const SizedBox(height: 24),

                      // Error message
                      if (_errorMessage != null)
                        FadeTransition(
                          opacity: _fadeAnimation,
                          child: Container(
                            width: double.infinity,
                            padding: const EdgeInsets.all(12),
                            margin: const EdgeInsets.only(bottom: 16),
                            decoration: BoxDecoration(
                              color: Colors.red.shade50,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  Icons.error_outline,
                                  color: Colors.red.shade700,
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    _errorMessage!,
                                    style: TextStyle(
                                      color: Colors.red.shade700,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),

                      // Submit button
                      SlideTransition(
                        position: _slideAnimation,
                        child: FadeTransition(
                          opacity: _fadeAnimation,
                          child: SizedBox(
                            width: double.infinity,
                            height: 56,
                            child: ElevatedButton(
                              style: ElevatedButton.styleFrom(
                                backgroundColor: const Color(0xFF34C759),
                                foregroundColor: Colors.white,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(16),
                                ),
                              ),
                              onPressed: _isLoading ? null : _submitProfile,
                              child: _isLoading
                                  ? const SizedBox(
                                      width: 24,
                                      height: 24,
                                      child: CircularProgressIndicator(
                                        color: Colors.white,
                                        strokeWidth: 2,
                                      ),
                                    )
                                  : const Text(
                                      'Complete Profile',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                            ),
                          ),
                        ),
                      ),

                      const SizedBox(height: 24),

                      // Terms and privacy
                      FadeTransition(
                        opacity: _fadeAnimation,
                        child: const Text(
                          'By continuing, you agree to our Terms of Service and Privacy Policy',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey,
                          ),
                        ),
                      ),

                      const SizedBox(height: 24),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
