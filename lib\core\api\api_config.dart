/// Configuration for API endpoints and settings
/// Centralized configuration for all API-related constants
class ApiConfig {
  // Base URL for the API
  static const String baseUrl = 'https://api2.eeil.online/api';
  
  // Debug flag - set to true to enable verbose logging
  static const bool debugMode = true;

  // API version
  static const String apiVersion = 'v1';

  // Full API URL with version
  static const String apiUrl = '$baseUrl/$apiVersion';

  // API endpoints - Auth
  static const String login = '/user/login';
  static const String verifyOtp = '/user/verify-otp';
  static const String logout = '/auth/logout';
  static const String refreshToken = '/auth/refresh-token';

  // API endpoints - User
  static const String userProfile = '/user/profile';
  static const String profile = '/user/profile'; // Alias for userProfile
  static const String userDetails = '/user/user-details';
  static const String updateProfile = '/user/user/update';
  static const String updateUser =
      '/user/user/update'; // Alias for updateProfile
  static const String updateProfileAlternative = '/user/update-profile';
  static const String deleteAccount = '/user/delete';
  static const String register = '/user/register';

  // API endpoints - Station
  static const String markers = '/user/stations/markers';
  static const String nearestStations = '/user/stations/nearest';
  static const String stationDetail = '/user/station/detail';
  static const String stationDetails =
      '/user/stations/details'; // FIXED: Correct endpoint is plural 'stations'
  static const String stationSearch = '/user/station/search';
  // Ensure this matches the expected endpoint: https://api2.eeil.online/api/v1/user/station/paginate
  static const String stationPaginate = '/user/station/paginate';
  static const String saveBookmark = '/bookmarks/save';
  static const String getBookmarkStations = '/bookmarks/stations';
  static const String saveReview = '/reviews/save';
  static const String getReviews = '/reviews/get';

  // API endpoints - Wallet
  static const String walletInfo = '/user/wallet/info';
  static const String addMoney = '/wallet/add-money';
  static const String walletAdd = '/wallet/add';
  static const String startTransaction = '/transactions/start';
  static const String stopTransaction = '/transactions/stop';
  static const String billingDetails = '/billing/details';
  static const String ongoingList = '/transactions/ongoing';
  static const String chargingSessionsList = '/transactions/history';
  static const String walletTransactions = '/user/wallet/transactions';

  // API endpoints - Vehicle
  static const String vehicles = '/vehicles';
  static const String saveVehicle = '/vehicles/save';
  static const String defaultVehicle = '/vehicles/default';
  static const String deleteVehicle = '/vehicles/delete';

  // API endpoints - Promocode
  static const String promocodes = '/promocodes';
  static const String promocodeVerify = '/promocodes/verify';

  // Payment integration endpoints
  static const String initiatePayment = '/payment/initiate';
  static const String verifyPayment = '/payment/verify';

  // API timeout durations (in milliseconds)
  static const Duration connectionTimeout = Duration(seconds: 10);
  static const Duration receiveTimeout = Duration(seconds: 10);
  static const Duration sendTimeout = Duration(seconds: 10);

  // Maximum number of retries for network errors
  static const int maxRetries = 3;

  // Retry delay in milliseconds
  static const Duration retryDelay = Duration(seconds: 1);

  // API headers
  static Map<String, String> getHeaders({String? token}) {
    final headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Connection':
          'close', // Force connection close to prevent connection reset issues
    };

    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }

    return headers;
  }

  // Public endpoints that don't require authentication
  static final List<String> publicEndpoints = [
    login,
    verifyOtp,
    register,
    initiatePayment,
  ];

  // Check if an endpoint is public (doesn't require authentication)
  static bool isPublicEndpoint(String endpoint) {
    return publicEndpoints.any((e) => endpoint.contains(e));
  }
}
