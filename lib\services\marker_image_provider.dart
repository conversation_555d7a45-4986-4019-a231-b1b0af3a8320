import 'dart:io';
import 'dart:async';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:flutter/services.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:path_provider/path_provider.dart';
import 'package:crypto/crypto.dart';
import 'dart:convert';
import '../core/api/api_service.dart';

class MarkerImageProvider {
  // Singleton instance
  static final MarkerImageProvider _instance = MarkerImageProvider._internal();
  factory MarkerImageProvider() => _instance;
  MarkerImageProvider._internal();

  // Cache for marker images and descriptors
  final Map<String, Uint8List> _imageCache = {};
  final Map<String, BitmapDescriptor> _descriptorCache = {};

  // We'll use the ApiService for authentication instead of hardcoding a token
  final ApiService _apiService = ApiService();

  // Directory for persistent cache
  Directory? _cacheDir;

  // Get cache directory
  Future<Directory> get _getCacheDir async {
    if (_cacheDir != null) return _cacheDir!;

    final appDir = await getApplicationDocumentsDirectory();
    _cacheDir = Directory('${appDir.path}/marker_cache');

    if (!await _cacheDir!.exists()) {
      await _cacheDir!.create(recursive: true);
    }

    return _cacheDir!;
  }

  // Generate a filename for caching
  String _getCacheFilename(String url) {
    final bytes = utf8.encode(url);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  // Check if image exists in persistent cache
  Future<File?> _getImageFileFromCache(String url) async {
    try {
      final cacheDir = await _getCacheDir;
      final filename = _getCacheFilename(url);
      final file = File('${cacheDir.path}/$filename');

      if (await file.exists()) {
        return file;
      }
    } catch (e) {
      debugPrint('Error checking persistent cache: $e');
    }

    return null;
  }

  // Save image to persistent cache
  Future<void> _saveImageToCache(String url, Uint8List imageBytes) async {
    try {
      final cacheDir = await _getCacheDir;
      final filename = _getCacheFilename(url);
      final file = File('${cacheDir.path}/$filename');

      await file.writeAsBytes(imageBytes);
      debugPrint('Saved image to persistent cache: $url');
    } catch (e) {
      debugPrint('Error saving to persistent cache: $e');
    }
  }

  // Get marker image from URL or cache with optimized performance and retry mechanism
  Future<Uint8List?> getMarkerImage(String url) async {
    debugPrint('Getting marker image for URL: $url');

    // Check if image is already in memory cache
    if (_imageCache.containsKey(url)) {
      debugPrint('Found marker image in memory cache for: $url');
      return _imageCache[url];
    }

    // Normalize URL to handle variations
    String normalizedUrl = url;
    if (url.contains('api2.eeil.online') && !url.startsWith('http')) {
      normalizedUrl = 'https://$url';
      debugPrint('Normalized URL to: $normalizedUrl');
    }

    // Check if normalized URL is in memory cache
    if (normalizedUrl != url && _imageCache.containsKey(normalizedUrl)) {
      debugPrint(
          'Found marker image in memory cache for normalized URL: $normalizedUrl');
      return _imageCache[normalizedUrl];
    }

    // Check persistent cache
    try {
      // Check for original URL
      File? cachedFile = await _getImageFileFromCache(url);

      // If not found, check for normalized URL
      if (cachedFile == null && normalizedUrl != url) {
        cachedFile = await _getImageFileFromCache(normalizedUrl);
      }

      if (cachedFile != null) {
        debugPrint('Loading marker image from persistent cache: $url');
        final imageBytes = await cachedFile.readAsBytes();

        if (imageBytes.isNotEmpty) {
          // Cache in memory
          _imageCache[url] = imageBytes;
          if (normalizedUrl != url) {
            _imageCache[normalizedUrl] = imageBytes;
          }

          return imageBytes;
        }
      }
    } catch (e) {
      debugPrint('Error reading from persistent cache: $e');
      // Continue to download if cache read fails
    }

    // Implement retry mechanism
    int maxRetries = 3;
    int currentRetry = 0;
    int baseDelayMs = 500; // Start with 500ms delay

    while (currentRetry < maxRetries) {
      try {
        debugPrint(
            'Downloading marker image: $normalizedUrl (attempt ${currentRetry + 1}/$maxRetries)');

        // Add authorization header for API2 URLs
        Map<String, String> headers = {};
        if (normalizedUrl.contains('api2.eeil.online')) {
          final token = await _apiService.getToken();
          if (token != null) {
            headers['Authorization'] = 'Bearer $token';
          }
        }

        // Use a shorter timeout for better UX
        final response = await http
            .get(Uri.parse(normalizedUrl), headers: headers)
            .timeout(const Duration(seconds: 5));

        if (response.statusCode == 200 && response.bodyBytes.isNotEmpty) {
          debugPrint(
              'Successfully downloaded marker image: $normalizedUrl (${response.bodyBytes.length} bytes)');

          // Use original image bytes without resizing to maintain quality
          final Uint8List imageBytes = response.bodyBytes;

          // Cache the original image in memory
          _imageCache[url] = imageBytes;
          if (normalizedUrl != url) {
            _imageCache[normalizedUrl] = imageBytes;
          }

          // Save to persistent cache
          _saveImageToCache(url, imageBytes);
          if (normalizedUrl != url) {
            _saveImageToCache(normalizedUrl, imageBytes);
          }

          return imageBytes;
        } else {
          debugPrint(
              'Failed to download marker image with status code: ${response.statusCode}');

          // Increment retry counter and delay
          currentRetry++;
          if (currentRetry < maxRetries) {
            // Exponential backoff
            final delay = baseDelayMs * (1 << currentRetry);
            debugPrint('Retrying after $delay ms...');
            await Future.delayed(Duration(milliseconds: delay));
          }
        }
      } catch (e) {
        debugPrint('Error fetching marker image: $e');

        // Increment retry counter and delay
        currentRetry++;
        if (currentRetry < maxRetries) {
          // Exponential backoff
          final delay = baseDelayMs * (1 << currentRetry);
          debugPrint('Retrying after $delay ms...');
          await Future.delayed(Duration(milliseconds: delay));
        }
      }
    }

    debugPrint('All retries failed for marker image: $url');
    return null;
  }

  // We're no longer resizing marker images to maintain original quality

  // Get marker image for a specific station status
  Future<Uint8List?> getMarkerImageForStatus(String status,
      {bool focused = false}) async {
    String url;

    if (status.toLowerCase().contains('unavailable')) {
      url = 'https://api2.eeil.online/mapicons/ecoplug_unavailable.png';
    } else if (status.toLowerCase().contains('in use') ||
        status.toLowerCase().contains('charging')) {
      url = focused
          ? 'https://api2.eeil.online/mapicons/ecoplug_charging_focus_new.png'
          : 'https://api2.eeil.online/mapicons/ecoplug_charging_default_new.png';
    } else {
      url = focused
          ? 'https://api2.eeil.online/mapicons/ecoplug_focus.png'
          : 'https://api2.eeil.online/mapicons/ecoplug_available.png'; // Use available.png instead of default.png
    }

    return getMarkerImage(url);
  }

  /// This method is deprecated. Use getBitmapDescriptorFromUrl instead.
  @Deprecated("Use getBitmapDescriptorFromUrl instead")
  Future<BitmapDescriptor?> fetchMarkerImage(String url) async {
    return getBitmapDescriptorFromUrl(url);
  }

  // Get BitmapDescriptor for a marker URL - using API icons
  Future<BitmapDescriptor?> getBitmapDescriptorFromUrl(
    String url, {
    double width = 25.0,
    double height = 37.0,
  }) async {
    debugPrint('Creating BitmapDescriptor for URL: $url');

    // Check if descriptor is already in cache with the same size
    final String sizeKey = '${width}x$height';
    final String cacheKey = '${url}_$sizeKey';
    if (_descriptorCache.containsKey(cacheKey)) {
      debugPrint(
          'Found descriptor in cache for: $url with size ${width}x$height');
      return _descriptorCache[cacheKey]!;
    }

    try {
      // Normalize URL to handle variations
      String normalizedUrl = url;
      if (url.contains('api2.eeil.online') && !url.startsWith('http')) {
        normalizedUrl = 'https://$url';
        debugPrint('Normalized URL to: $normalizedUrl');
      }

      // Check if normalized URL is in cache with the same size
      final String normalizedCacheKey = '${normalizedUrl}_$sizeKey';
      if (normalizedUrl != url &&
          _descriptorCache.containsKey(normalizedCacheKey)) {
        debugPrint(
            'Found descriptor in cache for normalized URL: $normalizedUrl with size ${width}x$height');
        return _descriptorCache[normalizedCacheKey]!;
      }

      // Download the image
      final Uint8List? imageData = await getMarkerImage(normalizedUrl);
      if (imageData == null || imageData.isEmpty) {
        debugPrint('Failed to download image from: $normalizedUrl');
        return _getFallbackMarker(normalizedUrl);
      }

      // Create a BitmapDescriptor from the image data with custom size
      // Using width and height parameters to control the size of the marker
      final descriptor = BitmapDescriptor.bytes(
        imageData,
        width: width, // Use the provided width parameter
        height: height, // Use the provided height parameter
      );
      debugPrint(
          'Successfully created custom marker from API image: $normalizedUrl');

      // Cache the descriptor with size information in the key
      _descriptorCache[cacheKey] = descriptor;
      _descriptorCache[normalizedCacheKey] =
          descriptor; // Cache both URL versions

      return descriptor;
    } catch (e) {
      debugPrint('Error creating BitmapDescriptor: $e');
      return _getFallbackMarker(url);
    }
  }

  // Get a fallback marker based on URL - NEVER use default Google markers
  Future<BitmapDescriptor> _getFallbackMarker(String url) async {
    try {
      // Create a custom marker programmatically
      Color markerColor;

      if (url.contains('unavailable')) {
        markerColor = Colors.red;
      } else if (url.contains('charging') || url.contains('in_use')) {
        markerColor = Colors.orange;
      } else if (url.contains('focus')) {
        markerColor = Colors.blue;
      } else {
        markerColor = Colors.green;
      }

      // Create a completely custom marker
      return await _createCustomMarker(markerColor);
    } catch (e) {
      debugPrint('Error creating custom fallback marker: $e');
      // Even in case of error, create a simple custom marker
      return await _createSimpleMarker(
          url.contains('unavailable') ? Colors.red : Colors.green);
    }
  }

  // Create a simple circular marker
  Future<BitmapDescriptor> _createSimpleMarker(Color color) async {
    try {
      final ui.PictureRecorder recorder = ui.PictureRecorder();
      final Canvas canvas = Canvas(recorder);
      final Paint paint = Paint()
        ..color = color
        ..style = PaintingStyle.fill;

      // Create a simple circle
      canvas.drawCircle(const Offset(24, 24), 12, paint);

      // Add white border
      final borderPaint = Paint()
        ..color = Colors.white
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2;
      canvas.drawCircle(const Offset(24, 24), 12, borderPaint);

      final ui.Image img = await recorder.endRecording().toImage(48, 48);
      final ByteData? data =
          await img.toByteData(format: ui.ImageByteFormat.png);

      if (data == null) {
        // If even this fails, create a 2x2 colored pixel
        return BitmapDescriptor.bytes(Uint8List.fromList([
          color.red.toInt(),
          color.green.toInt(),
          color.blue.toInt(),
          255,
          color.red.toInt(),
          color.green.toInt(),
          color.blue.toInt(),
          255,
          color.red.toInt(),
          color.green.toInt(),
          color.blue.toInt(),
          255,
          color.red.toInt(),
          color.green.toInt(),
          color.blue.toInt(),
          255,
        ]));
      }

      return BitmapDescriptor.bytes(data.buffer.asUint8List());
    } catch (e) {
      debugPrint('Error creating simple marker: $e');
      // Last resort - create a minimal colored pixel
      return BitmapDescriptor.bytes(Uint8List.fromList([
        color.red.toInt(),
        color.green.toInt(),
        color.blue.toInt(),
        255,
        color.red.toInt(),
        color.green.toInt(),
        color.blue.toInt(),
        255,
        color.red.toInt(),
        color.green.toInt(),
        color.blue.toInt(),
        255,
        color.red.toInt(),
        color.green.toInt(),
        color.blue.toInt(),
        255,
      ]));
    }
  }

  // Create a custom pin-shaped marker
  Future<BitmapDescriptor> _createCustomMarker(Color color) async {
    try {
      final ui.PictureRecorder recorder = ui.PictureRecorder();
      final Canvas canvas = Canvas(recorder);
      final double width = 48.0;
      final double height = 48.0;

      // Create a pin shape
      final Path pinPath = Path();
      pinPath.moveTo(width * 0.5, height * 0.1); // Top center
      pinPath.addOval(Rect.fromCircle(
        center: Offset(width * 0.5, height * 0.3),
        radius: width * 0.2,
      ));

      // Add the pin stem
      pinPath.moveTo(width * 0.5, height * 0.5);
      pinPath.lineTo(width * 0.4, height * 0.8);
      pinPath.lineTo(width * 0.6, height * 0.8);
      pinPath.close();

      // Fill the pin
      final Paint fillPaint = Paint()
        ..color = color
        ..style = PaintingStyle.fill;
      canvas.drawPath(pinPath, fillPaint);

      // Add border
      final Paint borderPaint = Paint()
        ..color = Colors.white
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2;
      canvas.drawPath(pinPath, borderPaint);

      // Complete the image
      final ui.Image img =
          await recorder.endRecording().toImage(width.toInt(), height.toInt());
      final ByteData? data =
          await img.toByteData(format: ui.ImageByteFormat.png);

      if (data == null) {
        // Fall back to simple marker if this fails
        return await _createSimpleMarker(color);
      }

      return BitmapDescriptor.bytes(data.buffer.asUint8List());
    } catch (e) {
      debugPrint('Error creating custom marker: $e');
      return await _createSimpleMarker(color);
    }
  }

  // Get marker descriptor based on station status
  Future<BitmapDescriptor?> getMarkerDescriptorForStatus(String status,
      {bool focused = false}) async {
    String url;

    if (focused) {
      url = 'https://api2.eeil.online/mapicons/ecoplug_focus.png';
    } else {
      if (status == 'Unavailable') {
        url =
            'https://api2.eeil.online/mapicons/ecoplug_unavailable.png'; // Changed from /icons/ to /mapicons/ for consistency
      } else if (status == 'In Use') {
        url =
            'https://api2.eeil.online/mapicons/ecoplug_charging_default_new.png';
      } else {
        url = 'https://api2.eeil.online/mapicons/ecoplug_default.png';
      }
    }

    return getBitmapDescriptorFromUrl(url);
  }

  // Preload common marker images to avoid delays - optimized for mobile devices
  Future<void> preloadCommonMarkers() async {
    // List of common marker URLs - prioritized by importance
    final commonUrls = [
      // Most important markers first
      'https://api2.eeil.online/mapicons/ecoplug_available.png',
      'https://api2.eeil.online/mapicons/ecoplug_unavailable.png',
      'https://api2.eeil.online/mapicons/ecoplug_charging_default_new.png',
      // Less important markers
      'https://api2.eeil.online/mapicons/ecoplug_default.png',
      'https://api2.eeil.online/mapicons/ecoplug_focus.png',
      'https://api2.eeil.online/mapicons/ecoplug_charging_focus_new.png',
      'https://api2.eeil.online/mapicons/ecoplug_charging.png',
      'https://api2.eeil.online/icons/unavailable.png',
    ];

    debugPrint('=== PRELOADING MARKER IMAGES ===');
    debugPrint('Preloading ${commonUrls.length} common marker images');

    // Check if we already have most of the images cached
    int cachedCount = 0;
    for (final url in commonUrls) {
      if (_imageCache.containsKey(url)) {
        cachedCount++;
      }
    }

    // If we have most images cached, just return quickly
    if (cachedCount >= commonUrls.length - 2) {
      debugPrint(
          'Most marker images already cached ($cachedCount/${commonUrls.length})');
      return;
    }

    // Preload sequentially for better reliability on mobile devices
    // This avoids overwhelming the network and is more reliable
    for (int i = 0; i < commonUrls.length; i++) {
      final url = commonUrls[i];
      if (!_imageCache.containsKey(url)) {
        try {
          // Add a larger delay between each request for mobile devices
          await Future.delayed(const Duration(milliseconds: 100));

          // Load the image
          final data = await getMarkerImage(url);
          if (data != null) {
            // Also create and cache the BitmapDescriptor
            await getBitmapDescriptorFromUrl(url);
            debugPrint('Preloaded marker image: $url');
          }
        } catch (e) {
          // Ignore errors in preloading
          debugPrint('Error preloading marker image $url: $e');
        }

        // Check if we should stop preloading (e.g., if we've loaded the most important ones)
        if (i >= 2 && _imageCache.length >= 3) {
          debugPrint('Loaded most important markers, continuing with app');
          break;
        }
      }
    }

    debugPrint('Finished preloading common marker images');
  }

  // No local marker generation as per user's request

  // Clear the cache
  void clearCache() {
    _imageCache.clear();
    _descriptorCache.clear();
    debugPrint('Marker image cache cleared');
  }
}
