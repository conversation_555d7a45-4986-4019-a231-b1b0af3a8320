# Project Refactoring Plan: Riverpod Migration, Service Providers, and Feature Modularization

**Goal:** Migrate the `ecoplug` project to use Riverpod for state management, refactor services into providers, and modularize features with a presentation, application, and domain layered structure, ensuring no errors are introduced and the existing UI/UX is preserved.

**Key Changes:**

1.  **Riverpod Integration:** Replace the `provider` package and `ChangeNotifier` with Riverpod's provider system.
2.  **Service Layer as Providers:** Convert existing service classes into Riverpod providers to manage their lifecycle and dependencies.
3.  **Feature Modularization:** Restructure the `lib` directory to group code by feature, with subdirectories for `presentation`, `application`, and `domain` layers within each feature.
4.  **Dependency Injection:** Leverage Riverpod's automatic dependency injection for managing dependencies between providers and within the application.
5.  **Data Consistency & Reactivity:** Utilize Riverpod's capabilities (like `AsyncValue`, `ref.watch`, `ref.read`, `ref.listen`) to ensure data is reactive, consistent, and properly invalidated (especially for user/session, marker, and station data).

**Phased Approach:**

This refactoring will be done iteratively, focusing on one area or feature at a time to minimize disruption and facilitate debugging.

**Phase 1: Riverpod Setup and Core Provider Migration**

*   **Step 1: Add Riverpod Dependency:** Add the necessary Riverpod packages (`flutter_riverpod`, `riverpod_annotation`, `custom_lint`, `riverpod_generator`) to `pubspec.yaml`.
*   **Step 2: Set up `ProviderScope`:** Wrap the root of the application (`MyApp` in `main.dart`) with `ProviderScope`.
*   **Step 3: Migrate `ThemeProvider`:**
    *   Convert `ThemeProvider` from `ChangeNotifier` to a Riverpod `Notifier` or `AsyncNotifier` (depending on whether asynchronous operations are involved beyond initial loading).
    *   Replace `Provider.of<ThemeProvider>(context)` usages with `ref.watch(themeProvider)`.
*   **Step 4: Migrate `MapMarkerProvider`:**
    *   Convert `MapMarkerProvider` from `ChangeNotifier` to a Riverpod `AsyncNotifier` as it involves asynchronous data fetching.
    *   Refactor the `loadMarkers` and `refreshMarkers` logic to fit the `AsyncNotifier` pattern, managing loading and error states using `AsyncValue`.
    *   Replace `Provider.of<MapMarkerProvider>(context)` usages with `ref.watch(mapMarkerProvider)`.
*   **Step 5: Remove `provider` Package:** Once all usages are migrated, remove the `provider` package dependency.

**Phase 2: Refactoring Services into Providers**

*   **Step 6: Identify Services:** Review the `lib/services` directory and identify classes that represent services (e.g., `ApiService`, `AuthService`, `MapMarkerService`, `MarkerImageProvider`, `StationService`, etc.).
*   **Step 7: Convert Services to Providers:**
    *   For each service, create a corresponding Riverpod provider (e.g., `Provider`, `Notifier`, `AsyncNotifier`, `StreamProvider`, `FutureProvider`) based on its functionality and state management needs.
    *   Inject dependencies into service providers using `ref.read()`.
    *   Refactor service methods to return data reactively where appropriate (e.g., using `AsyncValue`).
*   **Step 8: Update Service Usages:** Replace direct instantiation or singleton access of services with accessing them via `ref.read()` or `ref.watch()` in widgets and other providers.
**Phase 3: Feature Modularization and Layering**

*   **Step 9: Identify Features:** Analyze the existing screen and service structure to identify distinct features (e.g., Authentication [COMPLETED], Dashboard/Map, Station Details [IN PROGRESS], Profile [COMPLETED], Wallet [COMPLETED], Onboarding, Manage Vehicles).
*   **Step 10: Create Feature Directories:** Create a new directory structure within `lib` for each identified feature (e.g., `lib/features/auth`, `lib/features/dashboard`, `lib/features/station`, etc.).
*   **Step 11: Implement Layered Structure:** Within each feature directory, create subdirectories for `presentation`, `application`, and `domain`.
    *   **`domain`:** Contains core business logic, models, and repositories (interfaces).
    *   **`application`:** Contains use cases and application services that orchestrate domain logic.
    *   **`presentation`:** Contains UI widgets and screens.
*   **Step 12: Move Existing Code:** Move relevant files from the existing `lib/screens`, `lib/services`, `lib/repositories`, and `lib/models` directories into the appropriate feature and layer directories.
*   **Step 13: Update Imports:** Update import paths throughout the codebase to reflect the new file locations.

**Phase 4: Refinement and Error Handling**

*   **Step 14: Review and Refactor:** Review the refactored code for adherence to Riverpod best practices, clean architecture principles, and code quality.
*   **Step 15: Implement Cache Invalidation:** Ensure that data sensitive to user/session changes (like user profile, wallet, or user-specific station data) is properly invalidated and refreshed upon login, logout, or session changes using Riverpod's mechanisms.
*   **Step 16: Enhance Data Reactivity:** Use `ref.watch` where UI elements need to react to state changes, and `ref.read` for one-off access.
*   **Step 17: Error Handling:** Implement consistent error handling using `AsyncValue.when` or custom error states within providers.
*   **Step 18: Testing:** Update existing tests and add new tests for providers and application logic.

**Iterative Development and Verification:**

After each significant step (e.g., migrating a provider, refactoring a service, moving a feature), the application will be built and tested to identify and fix any errors immediately. This loop of "Implement -> Build -> Test -> Fix Errors" will continue until the entire migration is complete and the application is stable with no errors and the UI/UX is unchanged.

**Architectural Visualization (Mermaid Diagram):**

```mermaid
graph TD
    A[main.dart] --> B(ProviderScope)
    B --> C(Features)
    C --> D[Auth Feature]
    C --> E[Dashboard Feature]
    C --> F[Station Feature]
    C --> G[Profile Feature]
    C --> H[Wallet Feature]
    C --> I[Onboarding Feature]
    C --> J[Manage Vehicles Feature]

    D --> D1(Presentation Layer)
    D --> D2(Application Layer)
    D --> D3(Domain Layer)

    E --> E1(Presentation Layer)
    E --> E2(Application Layer)
    E --> E3(Domain Layer)

    F --> F1(Presentation Layer)
    F --> F2(Application Layer)
    F --> F3(Domain Layer)

    G --> G1(Presentation Layer)
    G --> G2(Application Layer)
    G --> G3(Domain Layer)

    H --> H1(Presentation Layer)
    H --> H2(Application Layer)
    H --> H3(Domain Layer)

    I --> I1(Presentation Layer)
    I --> I2(Application Layer)
    I --> I3(Domain Layer)

    J --> J1(Presentation Layer)
    J --> J2(Application Layer)
    J --> J3(Domain Layer)

    D2 --> K(Auth Providers)
    E2 --> L(Dashboard Providers)
    F2 --> M(Station Providers)
    G2 --> N(Profile Providers)
    H2 --> O(Wallet Providers)
    I2 --> P(Onboarding Providers)
    J2 --> Q(Manage Vehicles Providers)

    K --> R(Auth Repository)
    L --> S(Map Marker Service/Provider)
    M --> T(Station Service/Provider)
    N --> U(User Repository)
    O --> V(Wallet Repository)
    Q --> W(Vehicle Repository)

    R --> X(API Service/Provider)
    S --> X
    T --> X
    U --> X
    V --> X
    W --> X

    style D1 fill:#f9f,stroke:#333,stroke-width:2px
    style D2 fill:#ccf,stroke:#333,stroke-width:2px
    style D3 fill:#cfc,stroke:#333,stroke-width:2px
    style E1 fill:#f9f,stroke:#333,stroke-width:2px
    style E2 fill:#ccf,stroke:#333,stroke-width:2px
    style E3 fill:#cfc,stroke:#333,stroke-width:2px
    style F1 fill:#f9f,stroke:#333,stroke-width:2px
    style F2 fill:#ccf,stroke:#333,stroke-width:2px
    style F3 fill:#cfc,stroke:#333,stroke-width:2px
    style G1 fill:#f9f,stroke:#333,stroke-width:2px
    style G2 fill:#ccf,stroke:#333,stroke-width:2px
    style G3 fill:#cfc,stroke:#333,stroke-width:2px
    style H1 fill:#f9f,stroke:#333,stroke-width:2px
    style H2 fill:#ccf,stroke:#333,stroke-width:2px
    style H3 fill:#cfc,stroke:#333,stroke-width:2px
    style I1 fill:#f9f,stroke:#333,stroke-width:2px
    style I2 fill:#ccf,stroke:#333,stroke-width:2px
    style I3 fill:#cfc,stroke:#333,stroke-width:2px
    style J1 fill:#f9f,stroke:#333,stroke-width:2px
    style J2 fill:#ccf,stroke:#333,stroke-width:2px
    style J3 fill:#cfc,stroke:#333,stroke-width:2px
```