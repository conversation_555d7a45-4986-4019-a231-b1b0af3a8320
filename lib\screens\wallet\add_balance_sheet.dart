import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../utils/app_themes.dart';

/// A bottom sheet for adding balance to the wallet
/// Shows amount input field, quick selection options, and payment gateway selection
class AddBalanceSheet extends StatefulWidget {
  /// Callback function when user confirms adding balance
  final Function(double amount, {String source}) onAddBalance;

  const AddBalanceSheet({
    super.key,
    required this.onAddBalance,
  });

  @override
  State<AddBalanceSheet> createState() => _AddBalanceSheetState();
}

class _AddBalanceSheetState extends State<AddBalanceSheet>
    with SingleTickerProviderStateMixin {
  // Text controller for the amount input
  final TextEditingController _amountController = TextEditingController();

  // Animation controller for the sheet animations
  late AnimationController _animationController;

  // Quick selection amounts
  final List<int> _quickAmounts = [500, 1000, 2000, 5000];

  // Currently selected quick amount (null if custom amount)
  int? _selectedAmount;

  // PayU is the only payment gateway (removed selection UI)
  final String _selectedGateway = 'payu';

  // Focus node for the text field
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );

    // Start the animation
    _animationController.forward();

    // Add listener to update the text field when quick amount is selected
    _amountController.addListener(_onAmountChanged);
  }

  @override
  void dispose() {
    _amountController.removeListener(_onAmountChanged);
    _amountController.dispose();
    _animationController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  // Handle amount changes from the text field
  void _onAmountChanged() {
    if (_amountController.text.isEmpty) return;

    // Parse the amount from the text field
    final enteredAmount = int.tryParse(_amountController.text);
    if (enteredAmount == null) return;

    // Check if the entered amount matches any quick selection amount
    if (_quickAmounts.contains(enteredAmount)) {
      setState(() {
        _selectedAmount = enteredAmount;
      });
    } else {
      // If not, clear the selection
      setState(() {
        _selectedAmount = null;
      });
    }
  }

  // Handle quick amount selection
  void _selectQuickAmount(int amount) {
    setState(() {
      _selectedAmount = amount;
      _amountController.text = amount.toString();
    });

    // Unfocus the text field when a quick amount is selected
    _focusNode.unfocus();
  }

  // Handle add balance button press
  void _handleAddBalance() {
    // Validate the amount
    if (_amountController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter an amount'),
          behavior: SnackBarBehavior.floating,
        ),
      );
      return;
    }

    final amount = double.tryParse(_amountController.text);
    if (amount == null || amount <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter a valid amount'),
          behavior: SnackBarBehavior.floating,
        ),
      );
      return;
    }

    // Validate minimum amount
    const minimumAmount = 100.0;
    if (amount < minimumAmount) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Minimum amount is ₹100'),
          behavior: SnackBarBehavior.floating,
        ),
      );
      return;
    }

    // Call the callback function with the selected payment gateway
    widget.onAddBalance(amount, source: _selectedGateway);

    // Close the bottom sheet
    Navigator.pop(context);
  }

  // Payment gateway selection removed - PayU is the only option

  @override
  Widget build(BuildContext context) {
    // Define gradient colors
    final gradientColors = [const Color(0xFF4776E6), const Color(0xFF8E54E9)];

    // Check if dark mode is enabled
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      decoration: BoxDecoration(
        color: isDarkMode ? AppThemes.darkSurface : Colors.white,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
        border: isDarkMode
            ? Border(top: BorderSide(color: AppThemes.darkBorder, width: 1))
            : null,
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 20.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Sheet handle
              Center(
                child: Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: isDarkMode
                        ? Colors.grey.shade700
                        : Colors.grey.shade300,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),
              const SizedBox(height: 24),

              // Title with animation
              SlideTransition(
                position: Tween<Offset>(
                  begin: const Offset(0, -0.2),
                  end: Offset.zero,
                ).animate(CurvedAnimation(
                  parent: _animationController,
                  curve: const Interval(0.0, 0.6, curve: Curves.easeOutCubic),
                )),
                child: FadeTransition(
                  opacity: Tween<double>(begin: 0.0, end: 1.0).animate(
                    CurvedAnimation(
                      parent: _animationController,
                      curve:
                          const Interval(0.0, 0.6, curve: Curves.easeOutCubic),
                    ),
                  ),
                  child: Text(
                    'Add Balance',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: isDarkMode
                          ? AppThemes.darkTextPrimary
                          : const Color(0xFF333333),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
              const SizedBox(height: 32),

              // Amount input field with animation
              SlideTransition(
                position: Tween<Offset>(
                  begin: const Offset(0, 0.5),
                  end: Offset.zero,
                ).animate(CurvedAnimation(
                  parent: _animationController,
                  curve: const Interval(0.2, 0.7, curve: Curves.easeOutCubic),
                )),
                child: FadeTransition(
                  opacity: Tween<double>(begin: 0.0, end: 1.0).animate(
                    CurvedAnimation(
                      parent: _animationController,
                      curve:
                          const Interval(0.2, 0.7, curve: Curves.easeOutCubic),
                    ),
                  ),
                  child: Container(
                    decoration: BoxDecoration(
                      color: isDarkMode
                          ? AppThemes.darkCard
                          : Colors.grey.shade100,
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: isDarkMode
                            ? AppThemes.darkBorder
                            : Colors.grey.shade300,
                        width: 1,
                      ),
                    ),
                    child: TextField(
                      controller: _amountController,
                      focusNode: _focusNode,
                      keyboardType: TextInputType.number,
                      textAlign: TextAlign.start,
                      inputFormatters: [
                        FilteringTextInputFormatter.digitsOnly,
                      ],
                      decoration: InputDecoration(
                        hintText: 'Enter amount',
                        hintStyle: TextStyle(
                          color: isDarkMode
                              ? Colors.grey.shade500
                              : Colors.grey.shade500,
                          fontSize: 16,
                        ),
                        prefixIcon: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16.0),
                          child: Text(
                            '₹',
                            style: TextStyle(
                              color: isDarkMode
                                  ? AppThemes.darkTextPrimary
                                  : Colors.grey.shade800,
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        prefixIconConstraints: const BoxConstraints(
                          minWidth: 0,
                          minHeight: 0,
                        ),
                        border: InputBorder.none,
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 16,
                        ),
                      ),
                      style: TextStyle(
                        color: isDarkMode
                            ? AppThemes.darkTextPrimary
                            : Colors.grey.shade800,
                        fontSize: 20,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 24),

              // Quick amount selection with animation
              SlideTransition(
                position: Tween<Offset>(
                  begin: const Offset(0, 0.8),
                  end: Offset.zero,
                ).animate(CurvedAnimation(
                  parent: _animationController,
                  curve: const Interval(0.3, 0.8, curve: Curves.easeOutCubic),
                )),
                child: FadeTransition(
                  opacity: Tween<double>(begin: 0.0, end: 1.0).animate(
                    CurvedAnimation(
                      parent: _animationController,
                      curve:
                          const Interval(0.3, 0.8, curve: Curves.easeOutCubic),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: _quickAmounts.map((amount) {
                      final isSelected = _selectedAmount == amount;
                      return _buildQuickAmountButton(
                        amount: amount,
                        isSelected: isSelected,
                        onTap: () => _selectQuickAmount(amount),
                        gradientColors: gradientColors,
                      );
                    }).toList(),
                  ),
                ),
              ),
              const SizedBox(height: 24),

              // PayU payment info section
              SlideTransition(
                position: Tween<Offset>(
                  begin: const Offset(0, 0.8),
                  end: Offset.zero,
                ).animate(CurvedAnimation(
                  parent: _animationController,
                  curve: const Interval(0.35, 0.85, curve: Curves.easeOutCubic),
                )),
                child: FadeTransition(
                  opacity: Tween<double>(begin: 0.0, end: 1.0).animate(
                    CurvedAnimation(
                      parent: _animationController,
                      curve: const Interval(0.35, 0.85,
                          curve: Curves.easeOutCubic),
                    ),
                  ),
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color:
                          isDarkMode ? AppThemes.darkCard : Colors.blue.shade50,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isDarkMode
                            ? AppThemes.darkBorder
                            : Colors.blue.shade200,
                        width: 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.payment,
                          color: isDarkMode
                              ? AppThemes.darkTextPrimary
                              : Colors.blue.shade700,
                          size: 24,
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Payment via PayU',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: isDarkMode
                                      ? AppThemes.darkTextPrimary
                                      : Colors.blue.shade700,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                'Secure payment with UPI, Cards & Net Banking',
                                style: TextStyle(
                                  fontSize: 13,
                                  color: isDarkMode
                                      ? AppThemes.darkTextSecondary
                                      : Colors.blue.shade600,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 32),

              // Add balance button with animation
              SlideTransition(
                position: Tween<Offset>(
                  begin: const Offset(0, 1.0),
                  end: Offset.zero,
                ).animate(CurvedAnimation(
                  parent: _animationController,
                  curve: const Interval(0.4, 0.9, curve: Curves.easeOutCubic),
                )),
                child: FadeTransition(
                  opacity: Tween<double>(begin: 0.0, end: 1.0).animate(
                    CurvedAnimation(
                      parent: _animationController,
                      curve:
                          const Interval(0.4, 0.9, curve: Curves.easeOutCubic),
                    ),
                  ),
                  child: Container(
                    height: 56,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: gradientColors,
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight,
                      ),
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: gradientColors[1]
                              .withAlpha(77), // 0.3 * 255 = ~77
                          blurRadius: 12,
                          offset: const Offset(0, 6),
                        ),
                      ],
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        onTap: _handleAddBalance,
                        borderRadius: BorderRadius.circular(16),
                        splashColor:
                            Colors.white.withAlpha(26), // 0.1 * 255 = ~26
                        highlightColor:
                            Colors.white.withAlpha(26), // 0.1 * 255 = ~26
                        child: Center(
                          child: Text(
                            'Add Balance',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
    );
  }

  // Helper method to build quick amount selection buttons
  Widget _buildQuickAmountButton({
    required int amount,
    required bool isSelected,
    required VoidCallback onTap,
    required List<Color> gradientColors,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        width: 70,
        height: 40,
        decoration: BoxDecoration(
          gradient: isSelected
              ? LinearGradient(
                  colors: gradientColors,
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                )
              : null,
          color: isSelected
              ? null
              : (isDarkMode ? AppThemes.darkCard : Colors.grey.shade100),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected
                ? Colors.transparent
                : (isDarkMode ? AppThemes.darkBorder : Colors.grey.shade300),
            width: 1,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: gradientColors[1].withAlpha(77), // 0.3 * 255 = ~77
                    blurRadius: 8,
                    offset: const Offset(0, 3),
                  ),
                ]
              : null,
        ),
        child: Center(
          child: Text(
            '₹$amount',
            style: TextStyle(
              color: isSelected
                  ? Colors.white
                  : (isDarkMode
                      ? AppThemes.darkTextPrimary
                      : Colors.grey.shade800),
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ),
    );
  }

  // Payment gateway selection UI removed - PayU is the only payment method
}
