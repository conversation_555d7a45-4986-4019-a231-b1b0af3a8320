import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class GSTInputWidget extends StatefulWidget {
  final String? initialGstNo;
  final String? initialBusinessName;
  final Function(String? gstNo, String? businessName) onChanged;
  final bool isExpanded;
  final VoidCallback onTap;

  const GSTInputWidget({
    super.key,
    this.initialGstNo,
    this.initialBusinessName,
    required this.onChanged,
    required this.isExpanded,
    required this.onTap,
  });

  @override
  State<GSTInputWidget> createState() => _GSTInputWidgetState();
}

class _GSTInputWidgetState extends State<GSTInputWidget>
    with TickerProviderStateMixin {
  late TextEditingController _gstController;
  late TextEditingController _businessNameController;
  late AnimationController _expandController;
  late AnimationController _fadeController;
  late Animation<double> _expandAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  bool _gstError = false;
  String? _gstErrorText;

  @override
  void initState() {
    super.initState();

    _gstController = TextEditingController(text: widget.initialGstNo ?? '');
    _businessNameController =
        TextEditingController(text: widget.initialBusinessName ?? '');

    // Animation controllers
    _expandController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // Animations
    _expandAnimation = CurvedAnimation(
      parent: _expandController,
      curve: Curves.easeInOut,
    );

    _fadeAnimation = CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeIn,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, -0.5),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _expandController,
      curve: Curves.easeOutBack,
    ));

    // Listen for changes
    _gstController.addListener(_onGstChanged);
    _businessNameController.addListener(_onBusinessNameChanged);
  }

  @override
  void didUpdateWidget(GSTInputWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (widget.isExpanded != oldWidget.isExpanded) {
      if (widget.isExpanded) {
        _expandController.forward();
        _fadeController.forward();
      } else {
        _expandController.reverse();
        _fadeController.reverse();
      }
    }
  }

  void _onGstChanged() {
    final gstNo = _gstController.text;
    _validateGST(gstNo);
    widget.onChanged(
        gstNo.isEmpty ? null : gstNo,
        _businessNameController.text.isEmpty
            ? null
            : _businessNameController.text);
  }

  void _onBusinessNameChanged() {
    widget.onChanged(
        _gstController.text.isEmpty ? null : _gstController.text,
        _businessNameController.text.isEmpty
            ? null
            : _businessNameController.text);
  }

  void _validateGST(String gstNo) {
    if (gstNo.isEmpty) {
      setState(() {
        _gstError = false;
        _gstErrorText = null;
      });
      return;
    }

    // GST number validation (15 characters, alphanumeric)
    final gstRegex =
        RegExp(r'^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$');

    if (gstNo.length != 15) {
      setState(() {
        _gstError = true;
        _gstErrorText = 'GST number must be 15 characters';
      });
    } else if (!gstRegex.hasMatch(gstNo)) {
      setState(() {
        _gstError = true;
        _gstErrorText = 'Invalid GST number format';
      });
    } else {
      setState(() {
        _gstError = false;
        _gstErrorText = null;
      });
    }
  }

  @override
  void dispose() {
    _gstController.dispose();
    _businessNameController.dispose();
    _expandController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Main GST input trigger
        GestureDetector(
          onTap: widget.onTap,
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: widget.isExpanded
                    ? const Color(0xFF67C44C)
                    : Colors.grey.shade300,
                width: widget.isExpanded ? 2 : 1,
              ),
              boxShadow: widget.isExpanded
                  ? [
                      BoxShadow(
                        color: const Color(0xFF67C44C).withValues(alpha: 0.1),
                        blurRadius: 8,
                        spreadRadius: 2,
                      ),
                    ]
                  : [],
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
              child: Row(
                children: [
                  Icon(
                    Icons.receipt_long_outlined,
                    color: widget.isExpanded
                        ? const Color(0xFF67C44C)
                        : Colors.grey[600],
                    size: 22,
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Text(
                      widget.isExpanded
                          ? 'GST Details (Tap to collapse)'
                          : (_gstController.text.isNotEmpty
                              ? 'GST: ${_gstController.text}'
                              : 'Add GST Details (Optional)'),
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: widget.isExpanded
                            ? const Color(0xFF67C44C)
                            : Colors.grey[700],
                      ),
                    ),
                  ),
                  AnimatedRotation(
                    turns: widget.isExpanded ? 0.5 : 0,
                    duration: const Duration(milliseconds: 300),
                    child: Icon(
                      Icons.keyboard_arrow_down,
                      color: widget.isExpanded
                          ? const Color(0xFF67C44C)
                          : Colors.grey[400],
                      size: 20,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),

        // Expanded GST input fields
        SizeTransition(
          sizeFactor: _expandAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: Container(
                margin: const EdgeInsets.only(top: 16),
                child: Column(
                  children: [
                    // GST Number Input
                    _buildGSTTextField(
                      controller: _gstController,
                      label: 'GST Number',
                      hintText: 'Enter 15-digit GST number',
                      icon: Icons.numbers_outlined,
                      isError: _gstError,
                      errorText: _gstErrorText,
                      inputFormatters: [
                        LengthLimitingTextInputFormatter(15),
                        FilteringTextInputFormatter.allow(RegExp(r'[A-Z0-9]')),
                      ],
                      textCapitalization: TextCapitalization.characters,
                    ),

                    const SizedBox(height: 16),

                    // Business Name Input
                    _buildGSTTextField(
                      controller: _businessNameController,
                      label: 'Business Name',
                      hintText: 'Enter your business name',
                      icon: Icons.business_outlined,
                      textCapitalization: TextCapitalization.words,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildGSTTextField({
    required TextEditingController controller,
    required String label,
    required String hintText,
    required IconData icon,
    bool isError = false,
    String? errorText,
    List<TextInputFormatter>? inputFormatters,
    TextCapitalization textCapitalization = TextCapitalization.none,
  }) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isError ? Colors.red : Colors.grey.shade300,
          width: isError ? 1.5 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: isError
                ? Colors.red.withValues(alpha: 0.1)
                : Colors.grey.withValues(alpha: 0.05),
            blurRadius: 4,
            spreadRadius: 1,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextField(
            controller: controller,
            textCapitalization: textCapitalization,
            inputFormatters: inputFormatters,
            decoration: InputDecoration(
              contentPadding: const EdgeInsets.symmetric(vertical: 16),
              border: InputBorder.none,
              prefixIcon: Icon(
                icon,
                color: isError ? Colors.red : const Color(0xFF67C44C),
              ),
              labelText: label,
              hintText: hintText,
              labelStyle: TextStyle(
                color: isError ? Colors.red : Colors.grey[600],
                fontSize: 14,
              ),
              hintStyle: TextStyle(
                color: Colors.grey[400],
                fontSize: 14,
              ),
              floatingLabelBehavior: FloatingLabelBehavior.never,
            ),
          ),
          if (isError && errorText != null)
            Padding(
              padding: const EdgeInsets.only(left: 16, right: 16, bottom: 8),
              child: Text(
                errorText,
                style: const TextStyle(
                  color: Colors.red,
                  fontSize: 12,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
