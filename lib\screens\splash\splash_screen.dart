import 'package:flutter/material.dart';
import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../services/auth_manager.dart';
import '../../widgets/navigation_bar.dart';
import '../auth/auth_screen.dart';

import '../../services/marker_image_provider.dart';
import '../../services/station_data_cache.dart';
import '../../repositories/station_repository.dart';
import '../../features/auth/application/auth_notifier.dart';

class SplashScreen extends ConsumerStatefulWidget {
  const SplashScreen({super.key});

  @override
  ConsumerState<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends ConsumerState<SplashScreen>
    with SingleTickerProviderStateMixin {
  // Animation controller
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  // Auth manager
  final AuthManager _authManager = AuthManager();

  // API service for preloading data
  // Removed direct ApiService instance as repository is used
  final StationRepository _stationRepository = StationRepository();
  final StationDataCache _stationDataCache = StationDataCache();

  // Marker image provider
  final MarkerImageProvider _markerImageProvider = MarkerImageProvider();

  @override
  void initState() {
    super.initState();

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    // Create animations
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.5, curve: Curves.easeIn),
      ),
    );

    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.5, curve: Curves.easeOutQuart),
      ),
    );

    // Start the animation
    _animationController.forward();

    // Preload data and check authentication status
    _preloadDataAndCheckAuth();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// Preload data and check authentication status
  Future<void> _preloadDataAndCheckAuth() async {
    // Start preloading marker images and station data
    _preloadMarkerImages();

    // Wait at least 5 seconds for splash screen
    await Future.delayed(const Duration(seconds: 5));

    // Check authentication status
    await _checkAuthStatus();
  }

  /// Preload marker images and station data with improved caching
  Future<void> _preloadMarkerImages() async {
    try {
      debugPrint('\n=== PRELOADING RESOURCES DURING SPLASH SCREEN ===');

      // Use the improved preloadCommonMarkers method
      debugPrint('Preloading common marker images...');
      await _markerImageProvider.preloadCommonMarkers();

      // Also try to fetch station markers to cache them
      if (await _authManager.isLoggedIn()) {
        debugPrint('User is logged in, preloading station data...');

        try {
          // Use the improved station repository with caching
          debugPrint('Fetching station markers with improved caching...');
          final response = await _stationRepository.getStationMarkers();

          if (response.success &&
              response.data != null &&
              response.data!.isNotEmpty) {
            debugPrint(
                'Successfully preloaded ${response.data!.length} station markers');

            // Ensure the data is cached
            await _stationDataCache.saveStationMarkers(response.data!);
            debugPrint('Station markers saved to cache for offline use');

            // Preload marker images for these stations
            for (final marker in response.data!) {
              if (marker.mapPinUrl != null && marker.mapPinUrl!.isNotEmpty) {
                _markerImageProvider.getMarkerImage(marker.mapPinUrl!);
              }
              if (marker.focusedMapPinUrl != null &&
                  marker.focusedMapPinUrl!.isNotEmpty) {
                _markerImageProvider.getMarkerImage(marker.focusedMapPinUrl!);
              }
            }
          } else {
            debugPrint('Station markers response was not successful or empty');
            debugPrint(
                'Response: success=${response.success}, message=${response.message}');

            // Try to use cached data if available
            final cachedMarkers =
                await _stationDataCache.getCachedStationMarkers();
            if (cachedMarkers != null && cachedMarkers.isNotEmpty) {
              debugPrint(
                  'Using ${cachedMarkers.length} cached station markers');
            } else {
              debugPrint('No cached station markers available');
            }
          }

          // Fetch nearest stations using the direct URL method
          try {
            // Use coordinates from the example URL
            final double lat = 27.608162;
            final double lng = 76.613853;
            debugPrint(
                'Preloading nearest stations using coordinates: Lat $lat, Lng $lng');

            // The direct URL method already has retry logic built in
            // Use the station repository to fetch nearest stations
            final response =
                await _stationRepository.getNearestStations(lat, lng);

            if (response.data != null && response.data!.isNotEmpty) {
              debugPrint(
                  'Successfully preloaded ${response.data!.length} nearest stations');
              // Log the first few stations
              for (int i = 0;
                  i < (response.data!.length > 3 ? 3 : response.data!.length);
                  i++) {
                final station = response.data![i];
                debugPrint(
                    '  ${i + 1}. ${station.name} (ID: ${station.stationId})');
              }
            } else {
              debugPrint('Preloaded nearest stations response was empty');
            }
          } catch (e) {
            debugPrint('Error preloading nearest stations: $e');
            // Continue even if this fails - don't block splash screen
          }
        } catch (e) {
          debugPrint('Error in station data preloading: $e');
          // Continue even if this fails
        }
      } else {
        debugPrint('User is not logged in, skipping station data preloading');
      }

      debugPrint('Finished preloading resources during splash screen');
    } catch (e) {
      debugPrint('Error preloading resources: $e');
      // Continue even if preloading fails
    }
  }

  /// Check if the user is already logged in
  Future<void> _checkAuthStatus() async {
    try {
      // Use the Riverpod auth provider to check the auth state
      final authState = ref.read(authProvider);

      // Wait for the auth state to be loaded
      await authState.when(
        data: (state) async {
          // Check if the user is logged in
          if (state.isAuthenticated) {
            // User is logged in, navigate to main screen
            if (mounted) {
              Navigator.of(context).pushReplacement(
                MaterialPageRoute(builder: (context) => const MainNavigation()),
              );
            }
          } else {
            // User is not logged in, navigate to auth screen
            if (mounted) {
              Navigator.of(context).pushReplacement(
                MaterialPageRoute(builder: (context) => const AuthScreen()),
              );
            }
          }
        },
        loading: () async {
          // Wait a bit longer for the auth state to load
          await Future.delayed(const Duration(seconds: 2));
          // Try again
          _checkAuthStatus();
        },
        error: (error, stackTrace) {
          // If there's an error, navigate to auth screen
          debugPrint('Error checking auth state: $error');
          if (mounted) {
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(builder: (context) => const AuthScreen()),
            );
          }
        },
      );
    } catch (e) {
      // If there's an error, navigate to auth screen
      debugPrint('Error checking auth status: $e');
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const AuthScreen()),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Always use dark theme for splash screen

    return Scaffold(
      backgroundColor: const Color(0xFF121212), // Always use dark theme
      body: Center(
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: ScaleTransition(
            scale: _scaleAnimation,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Logo with dark theme
                Image.asset(
                  'assets/images/ecoplug_logo_dark_fixed.png',
                  width: 150,
                  height: 150,
                ),

                const SizedBox(height: 24),

                // App name
                const Text(
                  'EcoPlug',
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF34C759), // Green color
                  ),
                ),

                const SizedBox(height: 8),

                // Tagline
                const Text(
                  'Charging the future, sustainably',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey,
                  ),
                ),

                const SizedBox(height: 48),

                // Loading indicator
                const SizedBox(
                  width: 40,
                  height: 40,
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(
                        Color(0xFF34C759)), // Green color
                    strokeWidth: 3,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
