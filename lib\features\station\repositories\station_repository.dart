import 'dart:async';
import 'package:flutter/foundation.dart';
import '../../../core/api/api_service.dart';
import '../../../core/api/api_exception.dart';
import '../../../core/models/api_response.dart';
import '../models/station_models.dart';

/// Repository for station-related operations
class StationRepository {
  final ApiService _apiService;

  StationRepository(this._apiService);

  /// Get station markers for map display
  Future<ApiResponse<List<StationMarker>>> getStationMarkers() async {
    try {
      final response = await _apiService.get('/user/stations/markers');

      if (response['success'] == true && response['data'] != null) {
        final List<dynamic> markersData = response['data'];
        final markers = markersData
            .map((markerJson) => StationMarker.fromJson(markerJson))
            .toList();

        return ApiResponse<List<StationMarker>>(
          success: true,
          message:
              response['message'] ?? 'Station markers retrieved successfully',
          data: markers,
        );
      } else {
        return ApiResponse<List<StationMarker>>(
          success: false,
          message: response['message'] ?? 'Failed to get station markers',
        );
      }
    } on ApiException catch (e) {
      debugPrint('API Exception during get station markers: ${e.message}');
      return ApiResponse<List<StationMarker>>(
        success: false,
        message: e.message,
      );
    } catch (e) {
      debugPrint('Error during get station markers: $e');
      return ApiResponse<List<StationMarker>>(
        success: false,
        message: 'An unexpected error occurred. Please try again.',
      );
    }
  }

  /// Get nearest stations based on location
  Future<ApiResponse<List<NearestStation>>> getNearestStations(
      double latitude, double longitude) async {
    try {
      final response = await _apiService.get(
        '/user/stations/nearest',
        queryParams: {
          'latitude': latitude.toString(),
          'longitude': longitude.toString(),
        },
      );

      if (response['success'] == true && response['data'] != null) {
        final List<dynamic> stationsData = response['data'];
        final stations = stationsData
            .map((stationJson) => NearestStation.fromJson(stationJson))
            .toList();

        return ApiResponse<List<NearestStation>>(
          success: true,
          message:
              response['message'] ?? 'Nearest stations retrieved successfully',
          data: stations,
        );
      } else {
        return ApiResponse<List<NearestStation>>(
          success: false,
          message: response['message'] ?? 'Failed to get nearest stations',
        );
      }
    } on ApiException catch (e) {
      debugPrint('API Exception during get nearest stations: ${e.message}');
      return ApiResponse<List<NearestStation>>(
        success: false,
        message: e.message,
      );
    } catch (e) {
      debugPrint('Error during get nearest stations: $e');
      return ApiResponse<List<NearestStation>>(
        success: false,
        message: 'An unexpected error occurred. Please try again.',
      );
    }
  }

  /// Get station details by ID
  Future<ApiResponse<StationDetail>> getStationDetails(String stationId) async {
    // Enforce strict UID validation: reject empty or invalid UID format
    if (stationId.trim().isEmpty) {
      return ApiResponse<StationDetail>(
        success: false,
        message: 'Invalid station UID provided.',
      );
    }

    try {
      final response = await _apiService.get(
        '/user/station/detail',
        queryParams: {'station_id': stationId},
      );

      if (response['success'] == true && response['data'] != null) {
        final stationDetail = StationDetail.fromJson(response['data']);

        return ApiResponse<StationDetail>(
          success: true,
          message:
              response['message'] ?? 'Station details retrieved successfully',
          data: stationDetail,
        );
      } else {
        return ApiResponse<StationDetail>(
          success: false,
          message: response['message'] ?? 'Failed to get station details',
        );
      }
    } on ApiException catch (e) {
      debugPrint('API Exception during get station details: ${e.message}');
      return ApiResponse<StationDetail>(
        success: false,
        message: e.message,
      );
    } catch (e) {
      debugPrint('Error during get station details: $e');
      return ApiResponse<StationDetail>(
        success: false,
        message: 'An unexpected error occurred. Please try again.',
      );
    }
  }

  /// Search stations by query
  Future<ApiResponse<List<StationMarker>>> searchStations(String query) async {
    try {
      final response = await _apiService.get(
        '/user/station/search',
        queryParams: {'search': query},
      );

      if (response['success'] == true && response['data'] != null) {
        final List<dynamic> stationsData = response['data'];
        final stations = stationsData
            .map((stationJson) => StationMarker.fromJson(stationJson))
            .toList();

        return ApiResponse<List<StationMarker>>(
          success: true,
          message:
              response['message'] ?? 'Stations search completed successfully',
          data: stations,
        );
      } else {
        return ApiResponse<List<StationMarker>>(
          success: false,
          message: response['message'] ?? 'Failed to search stations',
        );
      }
    } on ApiException catch (e) {
      debugPrint('API Exception during search stations: ${e.message}');
      return ApiResponse<List<StationMarker>>(
        success: false,
        message: e.message,
      );
    } catch (e) {
      debugPrint('Error during search stations: $e');
      return ApiResponse<List<StationMarker>>(
        success: false,
        message: 'An unexpected error occurred. Please try again.',
      );
    }
  }

  /// Get station reviews
  Future<ApiResponse<List<Review>>> getStationReviews(String stationId) async {
    try {
      final response = await _apiService.get(
        '/reviews/get',
        queryParams: {'station_id': stationId},
      );

      if (response['success'] == true && response['data'] != null) {
        final List<dynamic> reviewsData = response['data'];
        final reviews = reviewsData
            .map((reviewJson) => Review.fromJson(reviewJson))
            .toList();

        return ApiResponse<List<Review>>(
          success: true,
          message: response['message'] ?? 'Reviews retrieved successfully',
          data: reviews,
        );
      } else {
        return ApiResponse<List<Review>>(
          success: false,
          message: response['message'] ?? 'Failed to get reviews',
        );
      }
    } on ApiException catch (e) {
      debugPrint('API Exception during get station reviews: ${e.message}');
      return ApiResponse<List<Review>>(
        success: false,
        message: e.message,
      );
    } catch (e) {
      debugPrint('Error during get station reviews: $e');
      return ApiResponse<List<Review>>(
        success: false,
        message: 'An unexpected error occurred. Please try again.',
      );
    }
  }

  /// Save station review
  Future<ApiResponse<void>> saveStationReview(
      String stationId, double rating, String comment) async {
    try {
      final response = await _apiService.post(
        '/reviews/save',
        data: {
          'station_id': stationId,
          'rating': rating,
          'comment': comment,
        },
      );

      if (response['success'] == true) {
        return ApiResponse<void>(
          success: true,
          message: response['message'] ?? 'Review saved successfully',
        );
      } else {
        return ApiResponse<void>(
          success: false,
          message: response['message'] ?? 'Failed to save review',
        );
      }
    } on ApiException catch (e) {
      debugPrint('API Exception during save station review: ${e.message}');
      return ApiResponse<void>(
        success: false,
        message: e.message,
      );
    } catch (e) {
      debugPrint('Error during save station review: $e');
      return ApiResponse<void>(
        success: false,
        message: 'An unexpected error occurred.  Please try again.',
      );
    }
  }

  /// Save station bookmark
  Future<ApiResponse<void>> saveStationBookmark(String stationId) async {
    try {
      final response = await _apiService.post(
        '/bookmarks/save',
        data: {'station_id': stationId},
      );

      if (response['success'] == true) {
        return ApiResponse<void>(
          success: true,
          message: response['message'] ?? 'Bookmark saved successfully',
        );
      } else {
        return ApiResponse<void>(
          success: false,
          message: response['message'] ?? 'Failed to save bookmark',
        );
      }
    } on ApiException catch (e) {
      debugPrint('API Exception during save station bookmark: ${e.message}');
      return ApiResponse<void>(
        success: false,
        message: e.message,
      );
    } catch (e) {
      debugPrint('Error during save station bookmark: $e');
      return ApiResponse<void>(
        success: false,
        message: 'An unexpected error occurred. Please try again.',
      );
    }
  }

  /// Get bookmarked stations
  Future<ApiResponse<List<StationMarker>>> getBookmarkedStations() async {
    try {
      final response = await _apiService.get('/bookmarks/stations');

      if (response['success'] == true && response['data'] != null) {
        final List<dynamic> stationsData = response['data'];
        final stations = stationsData
            .map((stationJson) => StationMarker.fromJson(stationJson))
            .toList();

        return ApiResponse<List<StationMarker>>(
          success: true,
          message: response['message'] ??
              'Bookmarked stations retrieved successfully',
          data: stations,
        );
      } else {
        return ApiResponse<List<StationMarker>>(
          success: false,
          message: response['message'] ?? 'Failed to get bookmarked stations',
        );
      }
    } on ApiException catch (e) {
      debugPrint('API Exception during get bookmarked stations: ${e.message}');
      return ApiResponse<List<StationMarker>>(
        success: false,
        message: e.message,
      );
    } catch (e) {
      debugPrint('Error during get bookmarked stations: $e');
      return ApiResponse<List<StationMarker>>(
        success: false,
        message: 'An unexpected error occurred. Please try again.',
      );
    }
  }
}
