import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../models/api_vehicle_model.dart';
import '../../../utils/app_theme.dart';
import '../../../widgets/animated_button.dart';

class RegistrationForm extends StatefulWidget {
  final ApiVehicle selectedVehicle;
  final Function(String) onRegister;
  final bool isLoading;

  const RegistrationForm({
    super.key,
    required this.selectedVehicle,
    required this.onRegister,
    this.isLoading = false,
  });

  @override
  RegistrationFormState createState() => RegistrationFormState();
}

class RegistrationFormState extends State<RegistrationForm>
    with SingleTickerProviderStateMixin {
  final TextEditingController _registrationController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 400),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOut,
      ),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.2),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOutCubic,
      ),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _registrationController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, -5),
              ),
            ],
          ),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // Title
                const Text(
                  'Register Your Vehicle',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),

                const SizedBox(height: 8),

                // Selected vehicle info
                Row(
                  children: [
                    Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        color: AppTheme.primaryLightColor,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child:
                          widget.selectedVehicle.vehicleImage.contains('http')
                              ? ClipRRect(
                                  borderRadius: BorderRadius.circular(10),
                                  child: Image.network(
                                    widget.selectedVehicle.vehicleImage,
                                    fit: BoxFit.cover,
                                    errorBuilder: (context, error, stackTrace) {
                                      return const Icon(
                                        Icons.directions_car,
                                        color: AppTheme.primaryColor,
                                      );
                                    },
                                  ),
                                )
                              : const Icon(
                                  Icons.directions_car,
                                  color: AppTheme.primaryColor,
                                ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.selectedVehicle.name,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                              color: AppTheme.textPrimaryColor,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          Text(
                            'Battery: ${widget.selectedVehicle.batteryCapacity}',
                            style: TextStyle(
                              fontSize: 14,
                              color:
                                  AppTheme.textSecondaryColor.withOpacity(0.8),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 20),

                // Registration number field
                TextFormField(
                  controller: _registrationController,
                  decoration: AppTheme.inputDecoration(
                    labelText: 'Registration Number',
                    hintText: 'e.g. RJ02GJ9922',
                    prefixIcon: const Icon(Icons.credit_card,
                        color: AppTheme.primaryColor),
                  ),
                  textCapitalization: TextCapitalization.characters,
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'[A-Za-z0-9]')),
                    LengthLimitingTextInputFormatter(12),
                  ],
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter registration number';
                    }
                    if (value.length < 5) {
                      return 'Registration number is too short';
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 24),

                // Register button
                AnimatedButton(
                  text: 'Register Vehicle',
                  icon: Icons.check_circle,
                  onPressed: () {
                    if (_formKey.currentState!.validate()) {
                      widget.onRegister(
                          _registrationController.text.toUpperCase());
                    }
                  },
                  isLoading: widget.isLoading,
                  gradient: AppTheme.primaryGradient,
                  width: double.infinity,
                  height: 56,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
