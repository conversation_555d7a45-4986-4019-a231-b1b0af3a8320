import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/vehicle_model.dart';

class VehicleApiService {
  static const String _baseUrl = 'https://api2.eeil.online/api/v1';

  // Fetch all vehicles
  Future<Map<String, List<Vehicle>>> fetchVehicles() async {
    try {
      final response = await http.get(Uri.parse('$_baseUrl/user/vehicles'));

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = json.decode(response.body);

        if (responseData['success'] == true) {
          Map<String, List<Vehicle>> result = {};

          final data = responseData['data'] as Map<String, dynamic>;

          data.forEach((key, value) {
            if (value is List) {
              result[key] =
                  value.map((item) => Vehicle.fromJson(item)).toList();
            }
          });

          return result;
        }
      }
      throw Exception('Failed to load vehicles');
    } catch (e) {
      throw Exception('Error: $e');
    }
  }

  // Save selected vehicle
  Future<bool> saveVehicle(int vehicleId, String registrationNumber) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/user/vehicles/save'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'regno': registrationNumber,
          'vehicle_id': vehicleId,
        }),
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        return responseData['success'] == true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }
}
