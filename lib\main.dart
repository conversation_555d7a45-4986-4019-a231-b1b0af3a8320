import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart'; // Import Riverpod
import 'package:ecoplug/screens/auth/auth_screen.dart';
import 'package:ecoplug/screens/splash/splash_screen.dart';
import 'package:ecoplug/widgets/navigation_bar.dart';
import 'package:ecoplug/screens/Trip/trip_page.dart';
import 'package:ecoplug/screens/Profile/Profilescreen/profile_screen_riverpod.dart';
import 'screens/station/station_list_page.dart';
import 'screens/station/station_details_page_fixed.dart';
import 'screens/wallet/wallet_screen.dart';
// Removed unused import
import 'utils/app_themes.dart';
import 'providers/provider_observer.dart'; // Import the provider observer
import 'providers/providers.dart'; // Import all providers
import 'utils/route_observer.dart'; // Import route observer

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Fix green line issues by ensuring proper rendering
  // Disable layout bounds visual debug rendering
  // Remove when issue is resolved
  debugDisableShadows = false;

  // Google Maps API key is configured in AndroidManifest.xml and AppDelegate.swift

  runApp(
    ProviderScope(
      // Wrap the app with ProviderScope
      observers: [LoggingProviderObserver()], // Add provider observer
      child: MyApp(),
    ),
  );
} // <-- Ensure main() properly closes here

class MyApp extends ConsumerWidget {
  // Change to ConsumerWidget
  const MyApp({super.key}); // Remove apiBridge parameter

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Add WidgetRef ref
    // Access themeMode using Riverpod
    final themeMode = ref.watch(themeNotifierProvider);

    return MaterialApp(
      title: 'Ecoplug',
      debugShowCheckedModeBanner: false,
      theme: AppThemes.lightTheme,
      darkTheme: AppThemes.darkTheme,
      themeMode: themeMode, // Use Riverpod themeMode here
      navigatorObservers: [routeObserver], // Add route observer
      initialRoute: '/',
      routes: {
        '/': (context) => const SplashScreen(),
        '/auth': (context) => const AuthScreen(),
        '/dashboard': (context) => const MainNavigation(),
        '/stationList': (context) =>
            const StationListPage(), // This will be replaced by direct navigation
        '/wallet': (context) => const WalletPage(),
        '/trip': (context) => const TripPage(),
        '/profile': (context) => const ProfileScreenRiverpod(),
      },
      // Use onGenerateRoute for routes that need parameters
      onGenerateRoute: (settings) {
        if (settings.name == '/stationDetails') {
          // Extract the arguments
          final args = settings.arguments;

          if (args is Map<String, dynamic>) {
            // Check if we have a UID directly
            if (args.containsKey('uid') && args['uid'] is String) {
              final String uid = args['uid'] as String;
              if (uid.isNotEmpty) {
                return MaterialPageRoute(
                  builder: (context) {
                    if (uid.isEmpty) {
                      throw ArgumentError(
                          'UID cannot be empty when launching StationDetailsPage');
                    }
                    return StationDetailsPage(uid: uid);
                  },
                );
              }
            }

            // Extract UID from arguments directly
            // Extract UID from arguments
            final String uid = args['uid'] as String? ?? '';
            if (uid.isNotEmpty) {
              return MaterialPageRoute(
                builder: (context) {
                  return StationDetailsPage(uid: uid);
                },
              );
            } else {
              // Handle missing UID
              return MaterialPageRoute(
                builder: (context) => const Scaffold(
                  body: Center(
                    child: Text('Invalid station information'),
                  ),
                ),
              );
            }
          }
        }
        return null;
      },
    );
  }
}
