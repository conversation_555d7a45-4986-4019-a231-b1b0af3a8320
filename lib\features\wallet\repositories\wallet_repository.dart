import 'package:flutter/foundation.dart';
import '../../../core/api/api_service.dart';
import '../../../core/api/api_exception.dart';
import '../../../core/models/api_response.dart';
import '../models/wallet_models.dart';

/// Repository for wallet-related operations
class WalletRepository {
  final ApiService _apiService;

  WalletRepository(this._apiService);

  /// Get wallet information
  Future<ApiResponse<WalletInfo>> getWalletInfo() async {
    try {
      final response = await _apiService.get('/user/wallet/info');

      if (response['success'] == true && response['wallet'] != null) {
        final walletData = response['wallet'];

        // Extract payment history if available
        List<Transaction> transactions = [];
        if (walletData['payment_history'] != null) {
          final List<dynamic> historyData = walletData['payment_history'];
          transactions = historyData.map((item) {
            return Transaction(
              id: item['id']?.toString() ?? '',
              title: item['remark'] ?? 'Transaction',
              description: item['source'] ?? '',
              amount: (item['amount'] as num?)?.toDouble() ?? 0.0,
              timestamp: item['created_at'] != null
                  ? DateTime.parse(item['created_at'])
                  : DateTime.now(),
              type: item['type'] == 'cr' ? 'credit' : 'debit',
              status: item['status'] == 'COMPLETED' ? 'completed' : 'pending',
              transactionReference: item['id']?.toString(),
            );
          }).toList();
        }

        final walletInfo = WalletInfo(
          balance: (walletData['balance'] as num?)?.toDouble() ?? 0.0,
          rewardPoints: 0, // API doesn't provide reward points
          recentTransactions: transactions,
        );

        return ApiResponse<WalletInfo>(
          success: true,
          message: response['message'] ?? 'Wallet info retrieved successfully',
          data: walletInfo,
        );
      } else {
        return ApiResponse<WalletInfo>(
          success: false,
          message: response['message'] ?? 'Failed to get wallet info',
        );
      }
    } on ApiException catch (e) {
      debugPrint('API Exception during get wallet info: ${e.message}');
      return ApiResponse<WalletInfo>(
        success: false,
        message: e.message,
      );
    } catch (e) {
      debugPrint('Error during get wallet info: $e');
      return ApiResponse<WalletInfo>(
        success: false,
        message: 'An unexpected error occurred. Please try again.',
      );
    }
  }

  /// Initialize a payment with the backend to get order data.
  /// This method calls an endpoint that prepares a payment transaction
  /// and returns the necessary data (`orderData`) for the client to proceed
  /// with the actual payment provider (e.g., PayU, Stripe, etc.).
  Future<ApiResponse<Map<String, dynamic>>> initializePayment(
    double amount,
    String paymentGateway, // 'cashfree', 'razorpay', 'payu', 'phonepe'
    // String currency, // Currency might be handled by backend or fixed
    // String receiptId, // Receipt ID might be handled by backend or fixed by service layer
    {
    String? promoCode,
    // Map<String, dynamic>? notes, // Notes might not be needed for these specific endpoints
  }) async {
    try {
      String endpoint;
      switch (paymentGateway.toLowerCase()) {
        case 'razorpay':
          endpoint = '/payment/initiate-razorpay';
          break;

        case 'phonepe':
          endpoint = '/payment/initiate-phonepe';
          break;
        case 'cashfree':
        default: // Default to cashfree as per the JS snippet
          endpoint = '/payment/initiate-cashfree';
          break;
      }

      final Map<String, dynamic> data = {
        'amount': (amount * 100)
            .toInt(), // Amount in smallest currency unit (e.g., paise for INR)
        'source': 'app', // As per the backend snippet
      };

      if (promoCode != null && promoCode.isNotEmpty) {
        data['promocode'] =
            promoCode; // Key from backend snippet is 'promocode'
      }

      // The backend endpoint now varies based on paymentGateway
      final response = await _apiService.post(endpoint, data: data);

      if (response['success'] == true && response['data'] != null) {
        // Assuming the backend returns the orderData within a 'data' field.
        return ApiResponse<Map<String, dynamic>>(
          success: true,
          message: response['message'] ?? 'Payment initialized successfully.',
          data:
              response['data'] as Map<String, dynamic>, // This is the orderData
        );
      } else {
        return ApiResponse<Map<String, dynamic>>(
          success: false,
          message: response['message'] ?? 'Failed to initialize payment.',
        );
      }
    } on ApiException catch (e) {
      debugPrint('API Exception during payment initialization: ${e.message}');
      return ApiResponse<Map<String, dynamic>>(
        success: false,
        message: e.message,
      );
    } catch (e) {
      debugPrint('Error during payment initialization: $e');
      return ApiResponse<Map<String, dynamic>>(
        success: false,
        message: 'An unexpected error occurred during payment initialization.',
      );
    }
  }

  /// Add money to wallet
  Future<ApiResponse<Map<String, dynamic>>> addMoney(double amount,
      {String? promoCode}) async {
    try {
      // Explicitly define the map as Map<String, dynamic> to allow mixed types
      final Map<String, dynamic> data = <String, dynamic>{
        'amount': amount,
      };

      if (promoCode != null && promoCode.isNotEmpty) {
        data['promo_code'] = promoCode;
      }

      final response = await _apiService.post('/wallet/add-money', data: data);

      if (response['success'] == true) {
        return ApiResponse<Map<String, dynamic>>(
          success: true,
          message: response['message'] ?? 'Money added successfully',
          data: response,
        );
      } else {
        return ApiResponse<Map<String, dynamic>>(
          success: false,
          message: response['message'] ?? 'Failed to add money',
        );
      }
    } on ApiException catch (e) {
      debugPrint('API Exception during add money: ${e.message}');
      return ApiResponse<Map<String, dynamic>>(
        success: false,
        message: e.message,
      );
    } catch (e) {
      debugPrint('Error during add money: $e');
      return ApiResponse<Map<String, dynamic>>(
        success: false,
        message: 'An unexpected error occurred. Please try again.',
      );
    }
  }

  /// Get charging sessions history
  Future<ApiResponse<List<ChargingSession>>> getChargingSessions() async {
    try {
      final response = await _apiService.get('/transactions/history');

      if (response['success'] == true && response['data'] != null) {
        final List<dynamic> sessionsData = response['data'];
        final sessions = sessionsData.map((sessionJson) {
          return ChargingSession(
            id: sessionJson['id']?.toString() ?? '',
            stationName: sessionJson['station_name'] ?? 'Unknown Station',
            stationAddress: sessionJson['station_address'] ?? '',
            connectorType: sessionJson['connector_type'] ?? 'Unknown',
            energyConsumed:
                (sessionJson['energy_consumed'] as num?)?.toDouble() ?? 0.0,
            amount: (sessionJson['amount'] as num?)?.toDouble() ?? 0.0,
            startTime: sessionJson['start_time'] != null
                ? DateTime.parse(sessionJson['start_time'])
                : DateTime.now(),
            endTime: sessionJson['end_time'] != null
                ? DateTime.parse(sessionJson['end_time'])
                : null,
            status: sessionJson['status'] ?? 'completed',
          );
        }).toList();

        return ApiResponse<List<ChargingSession>>(
          success: true,
          message:
              response['message'] ?? 'Charging sessions retrieved successfully',
          data: sessions,
        );
      } else {
        return ApiResponse<List<ChargingSession>>(
          success: false,
          message: response['message'] ?? 'Failed to get charging sessions',
        );
      }
    } on ApiException catch (e) {
      debugPrint('API Exception during get charging sessions: ${e.message}');
      return ApiResponse<List<ChargingSession>>(
        success: false,
        message: e.message,
      );
    } catch (e) {
      debugPrint('Error during get charging sessions: $e');
      return ApiResponse<List<ChargingSession>>(
        success: false,
        message: 'An unexpected error occurred. Please try again.',
      );
    }
  }

  /// Get ongoing charging sessions
  Future<ApiResponse<List<ChargingSession>>> getOngoingSessions() async {
    try {
      final response = await _apiService.get('/transactions/ongoing');

      if (response['success'] == true && response['data'] != null) {
        final List<dynamic> sessionsData = response['data'];
        final sessions = sessionsData.map((sessionJson) {
          return ChargingSession(
            id: sessionJson['id']?.toString() ?? '',
            stationName: sessionJson['station_name'] ?? 'Unknown Station',
            stationAddress: sessionJson['station_address'] ?? '',
            connectorType: sessionJson['connector_type'] ?? 'Unknown',
            energyConsumed:
                (sessionJson['energy_consumed'] as num?)?.toDouble() ?? 0.0,
            amount: (sessionJson['amount'] as num?)?.toDouble() ?? 0.0,
            startTime: sessionJson['start_time'] != null
                ? DateTime.parse(sessionJson['start_time'])
                : DateTime.now(),
            endTime: null,
            status: 'ongoing',
          );
        }).toList();

        return ApiResponse<List<ChargingSession>>(
          success: true,
          message:
              response['message'] ?? 'Ongoing sessions retrieved successfully',
          data: sessions,
        );
      } else {
        return ApiResponse<List<ChargingSession>>(
          success: false,
          message: response['message'] ?? 'Failed to get ongoing sessions',
        );
      }
    } on ApiException catch (e) {
      debugPrint('API Exception during get ongoing sessions: ${e.message}');
      return ApiResponse<List<ChargingSession>>(
        success: false,
        message: e.message,
      );
    } catch (e) {
      debugPrint('Error during get ongoing sessions: $e');
      return ApiResponse<List<ChargingSession>>(
        success: false,
        message: 'An unexpected error occurred. Please try again.',
      );
    }
  }

  /// Get billing details for a transaction
  Future<ApiResponse<BillingDetails>> getBillingDetails(
      String transactionId) async {
    try {
      final response = await _apiService.get(
        '/billing/details',
        queryParams: {'transaction_id': transactionId},
      );

      if (response['success'] == true && response['data'] != null) {
        final billingData = response['data'];
        final billingDetails = BillingDetails(
          id: transactionId,
          totalAmount: (billingData['total_amount'] as num?)?.toDouble() ?? 0.0,
          energyConsumed:
              (billingData['energy_consumed'] as num?)?.toDouble() ?? 0.0,
          rate: (billingData['rate'] as num?)?.toDouble() ?? 0.0,
          startTime: billingData['start_time'] != null
              ? DateTime.parse(billingData['start_time'])
              : DateTime.now(),
          endTime: billingData['end_time'] != null
              ? DateTime.parse(billingData['end_time'])
              : DateTime.now(),
          stationName: billingData['station_name'] ?? 'Unknown Station',
          connectorType: billingData['connector_type'] ?? 'Unknown',
        );

        return ApiResponse<BillingDetails>(
          success: true,
          message:
              response['message'] ?? 'Billing details retrieved successfully',
          data: billingDetails,
        );
      } else {
        return ApiResponse<BillingDetails>(
          success: false,
          message: response['message'] ?? 'Failed to get billing details',
        );
      }
    } on ApiException catch (e) {
      debugPrint('API Exception during get billing details: ${e.message}');
      return ApiResponse<BillingDetails>(
        success: false,
        message: e.message,
      );
    } catch (e) {
      debugPrint('Error during get billing details: $e');
      return ApiResponse<BillingDetails>(
        success: false,
        message: 'An unexpected error occurred. Please try again.',
      );
    }
  }

  /// Verify promocode
  Future<ApiResponse<Map<String, dynamic>>> verifyPromocode(
      String promocode) async {
    try {
      final response = await _apiService.post(
        '/promocodes/verify',
        data: {'promo_code': promocode},
      );

      if (response['success'] == true) {
        return ApiResponse<Map<String, dynamic>>(
          success: true,
          message: response['message'] ?? 'Promocode verified successfully',
          data: response,
        );
      } else {
        return ApiResponse<Map<String, dynamic>>(
          success: false,
          message: response['message'] ?? 'Invalid promocode',
        );
      }
    } on ApiException catch (e) {
      debugPrint('API Exception during verify promocode: ${e.message}');
      return ApiResponse<Map<String, dynamic>>(
        success: false,
        message: e.message,
      );
    } catch (e) {
      debugPrint('Error during verify promocode: $e');
      return ApiResponse<Map<String, dynamic>>(
        success: false,
        message: 'An unexpected error occurred. Please try again.',
      );
    }
  }
}
