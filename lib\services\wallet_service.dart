import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../models/wallet/wallet_models.dart';
import '../models/api_response.dart';
import '../repositories/wallet_repository.dart';
import 'service_locator.dart';

import '../features/wallet/services/payment_gateway_service.dart';

/// Wallet service for handling wallet-related operations
class WalletService with ChangeNotifier {
  static final WalletService _instance = WalletService._internal();
  factory WalletService() => _instance;

  WalletService._internal();

  // Lazy initialization of repositories
  WalletRepository get _walletRepository => ServiceLocator().walletRepository;
  // Payment gateway services
  final PaymentGatewayService _paymentGatewayService = PaymentGatewayService();

  /// Get wallet information - always fetches fresh data from the server
  Future<WalletInfo?> getWalletInfo() async {
    try {
      final response = await _walletRepository.getWalletInfo();
      if (response.status == ApiStatus.success) {
        return response.data;
      } else {
        debugPrint('Failed to get wallet info: ${response.message}');
        return null;
      }
    } catch (e) {
      debugPrint('Error getting wallet info: $e');
      return null;
    }
  }

  /// Refresh wallet information - used to update wallet data after transactions
  Future<void> _refreshWalletInfo() async {
    try {
      await getWalletInfo();
      notifyListeners();
    } catch (e) {
      debugPrint('Error refreshing wallet info: $e');
    }
  }

  /// Start a charging transaction
  Future<ApiResponse<dynamic>> startTransaction(
      String stationId, String connectorId,
      {String? vehicleId, String? promocodeId}) async {
    try {
      return await _walletRepository.startTransaction(
          stationId, connectorId, vehicleId, promocodeId);
    } catch (e) {
      debugPrint('Error starting transaction: $e');
      return ApiResponse.error('Failed to start transaction: $e');
    }
  }

  /// Stop a charging transaction
  Future<ApiResponse<dynamic>> stopTransaction(String transactionId) async {
    try {
      return await _walletRepository.stopTransaction(transactionId);
    } catch (e) {
      debugPrint('Error stopping transaction: $e');
      return ApiResponse.error('Failed to stop transaction: $e');
    }
  }

  /// Get billing details for a transaction
  Future<ApiResponse<dynamic>> getBillingDetails(String transactionId) async {
    try {
      return await _walletRepository.getBillingDetails(transactionId);
    } catch (e) {
      debugPrint('Error getting billing details: $e');
      return ApiResponse.error('Failed to get billing details: $e');
    }
  }

  /// Get ongoing charging sessions
  Future<ApiResponse<dynamic>> getOngoingSessions() async {
    try {
      return await _walletRepository.getOngoingSessions();
    } catch (e) {
      debugPrint('Error getting ongoing sessions: $e');
      return ApiResponse.error('Failed to get ongoing sessions: $e');
    }
  }

  /// Get charging session history
  Future<ApiResponse<dynamic>> getChargingSessionHistory() async {
    try {
      return await _walletRepository.getChargingSessionHistory();
    } catch (e) {
      debugPrint('Error getting charging session history: $e');
      return ApiResponse.error('Failed to get charging session history: $e');
    }
  }

  /// Add money to wallet
  Future<ApiResponse<dynamic>> addMoney(double amount) async {
    try {
      return await _walletRepository.addMoney(amount);
    } catch (e) {
      debugPrint('Error adding money to wallet: $e');
      return ApiResponse.error('Failed to add money to wallet: $e');
    }
  }

  /// Initiate payment with any supported gateway
  Future<void> initiatePayment(
    double amount, {
    required BuildContext context,
    String? promoCode,
    String source =
        'payu', // Default to PayU, but can be 'razorpay', 'phonepe', 'cashfree'
  }) async {
    try {
      debugPrint(
          'Initiating payment request for amount: $amount, source: $source');

      // Check if context is still valid
      if (!context.mounted) {
        throw Exception('Context is no longer valid');
      }

      // Get user information from SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      final String customerName = prefs.getString('user_name') ?? "Demo User";
      final String customerEmail =
          prefs.getString('user_email') ?? "<EMAIL>";
      final String customerPhone =
          prefs.getString('user_phone') ?? "9999999999";

      debugPrint(
          'Using customer details - Name: $customerName, Email: $customerEmail, Phone: $customerPhone');

      // Determine the API endpoint based on the payment source
      String apiEndpoint;
      switch (source.toLowerCase()) {
        case 'razorpay':
          apiEndpoint = '/payment/initiate-razorpay';
          break;
        case 'phonepe':
          apiEndpoint = '/payment/initiate-phonepe';
          break;
        case 'cashfree':
          apiEndpoint = '/payment/initiate-cashfree';
          break;
        default:
          apiEndpoint = '/payment/initiate-cashfree';
          break;
      }

      // Prepare payment request data with additional info for production
      final Map<String, dynamic> requestData = {
        'amount': amount,
        'source': 'app',
        'user_name': customerName,
        'user_email': customerEmail,
        'user_phone': customerPhone,
        'device_info': _getDeviceInfo(),
        if (promoCode != null && promoCode.isNotEmpty) 'promocode': promoCode,
      };

      debugPrint(
          'Sending payment request to: $apiEndpoint with data: $requestData');

      // Make API call to get payment order
      final ApiResponse response = await _walletRepository.initiatePaymentOrder(
          apiEndpoint, requestData);

      if (response.status != ApiStatus.success) {
        throw Exception('Failed to initiate payment: ${response.message}');
      }

      final orderData = response.data;
      debugPrint('Payment order received: $orderData');

      // Check context again before proceeding
      if (!context.mounted) {
        throw Exception('Context is no longer valid');
      }

      // Start payment flow using the payment gateway service
      final paymentStatusStream = await _paymentGatewayService.initiatePayment(
        amount: amount,
        customerName: customerName,
        customerEmail: customerEmail,
        customerPhone: customerPhone,
        promoCode: promoCode,
        source: source,
        context: context,
        orderData: orderData,
      );

      // Set up a listener to handle payment status updates
      paymentStatusStream.listen((status) {
        debugPrint('Payment status update: $status');

        if (status['status'] == 'success') {
          _handleSuccessfulPayment(amount, status);

          // Verify the payment with backend for security (crucial for production)
          _verifyPaymentWithBackend(status['txnId'], source);
        } else if (status['status'] == 'failure' ||
            status['status'] == 'failed') {
          // Handle payment failure
          debugPrint('Payment failed: ${status['message']}');
        }
      });
    } catch (e) {
      debugPrint('Error initiating payment: $e');
      debugPrint('Stack trace: ${StackTrace.current}');

      // Show error to user
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Payment initialization failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }

      rethrow; // Rethrow to allow the UI to handle the error
    }
  }

  // Get basic device info for payment tracking
  Map<String, String> _getDeviceInfo() {
    return {
      'platform': kIsWeb ? 'web' : _getPlatformName(),
      'app_version': '1.0.0', // Replace with your actual app version
    };
  }

  // Helper method to get platform name safely
  String _getPlatformName() {
    try {
      if (kIsWeb) return 'web';
      return Platform.operatingSystem;
    } catch (_) {
      return 'unknown';
    }
  }

  // Verify payment with backend for security
  Future<void> _verifyPaymentWithBackend(String txnId, String source) async {
    try {
      // Make API call to verify payment on server
      final verifyEndpoint = '/payment/verify-$source';
      final response = await _walletRepository
          .verifyPayment(verifyEndpoint, {'transaction_id': txnId});

      if (response.status == ApiStatus.success) {
        debugPrint('Payment verified successfully: $txnId');
        // Payment is valid, refresh wallet data
        _refreshWalletInfo();
      } else {
        debugPrint('Payment verification failed: ${response.message}');
        // Handle verification failure (potential fraud attempt)
        // You might need to show an alert to the user
      }
    } catch (e) {
      debugPrint('Error verifying payment: $e');
    }
  }

  /// Handle a successful payment by updating wallet balance
  void _handleSuccessfulPayment(
      double amount, Map<String, dynamic> paymentDetails) {
    try {
      // In a real app, you would update the wallet balance on the server
      // and then refresh the local balance from the server

      // Notify listeners about the balance change
      notifyListeners();

      debugPrint('Wallet balance updated after successful payment');

      // Refresh wallet info to get the latest balance
      _refreshWalletInfo();
    } catch (e) {
      debugPrint('Error updating wallet after payment: $e');
    }
  }
}
