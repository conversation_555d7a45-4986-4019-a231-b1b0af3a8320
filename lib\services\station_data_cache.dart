import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/station/station_marker_response.dart';

/// Service for caching station data to provide offline fallback
class StationDataCache {
  // Singleton pattern
  static final StationDataCache _instance = StationDataCache._internal();
  factory StationDataCache() => _instance;
  StationDataCache._internal();

  // Keys for SharedPreferences
  static const String _stationMarkersKey = 'cached_station_markers';
  static const String _lastCacheTimeKey = 'station_markers_cache_time';
  static const String _cacheVersionKey = 'station_markers_cache_version';

  // Current cache version - increment when changing cache format
  static const int _currentCacheVersion = 1;

  // Cache expiration time (24 hours)
  static const Duration _cacheExpiration = Duration(hours: 24);

  /// Save station markers to cache
  Future<bool> saveStationMarkers(List<StationMarkerData> markers) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Convert markers to JSON
      final List<Map<String, dynamic>> markerMaps =
          markers.map((marker) => marker.toJson()).toList();

      // Save as JSON string
      final String markersJson = jsonEncode(markerMaps);

      // Save to SharedPreferences
      await prefs.setString(_stationMarkersKey, markersJson);
      await prefs.setInt(
          _lastCacheTimeKey, DateTime.now().millisecondsSinceEpoch);
      await prefs.setInt(_cacheVersionKey, _currentCacheVersion);

      debugPrint('Saved ${markers.length} station markers to cache');
      return true;
    } catch (e) {
      debugPrint('Error saving station markers to cache: $e');
      return false;
    }
  }

  /// Get cached station markers
  Future<List<StationMarkerData>?> getCachedStationMarkers(
      {bool ignoreExpiration = false}) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Check if cache exists
      if (!prefs.containsKey(_stationMarkersKey)) {
        debugPrint('No cached station markers found');
        return null;
      }

      // Check cache version
      final int? cacheVersion = prefs.getInt(_cacheVersionKey);
      if (cacheVersion != _currentCacheVersion) {
        debugPrint(
            'Cached station markers version mismatch: $cacheVersion != $_currentCacheVersion');
        return null;
      }

      // Check cache expiration
      if (!ignoreExpiration) {
        final int? cacheTime = prefs.getInt(_lastCacheTimeKey);
        if (cacheTime != null) {
          final DateTime cacheDateTime =
              DateTime.fromMillisecondsSinceEpoch(cacheTime);
          final Duration age = DateTime.now().difference(cacheDateTime);

          if (age > _cacheExpiration) {
            debugPrint(
                'Cached station markers expired (${age.inHours} hours old)');
            return null;
          }
        }
      }

      // Get cached data
      final String? markersJson = prefs.getString(_stationMarkersKey);
      if (markersJson == null || markersJson.isEmpty) {
        debugPrint('Cached station markers data is empty');
        return null;
      }

      // Parse JSON
      final List<dynamic> markerMaps = jsonDecode(markersJson);

      // Convert to StationMarkerData objects
      final List<StationMarkerData> markers =
          markerMaps.map((map) => StationMarkerData.fromJson(map)).toList();

      debugPrint('Retrieved ${markers.length} station markers from cache');
      return markers;
    } catch (e) {
      debugPrint('Error retrieving cached station markers: $e');
      return null;
    }
  }

  /// Check if cache is valid and not expired
  Future<bool> isCacheValid() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Check if cache exists
      if (!prefs.containsKey(_stationMarkersKey)) {
        return false;
      }

      // Check cache version
      final int? cacheVersion = prefs.getInt(_cacheVersionKey);
      if (cacheVersion != _currentCacheVersion) {
        return false;
      }

      // Check cache expiration
      final int? cacheTime = prefs.getInt(_lastCacheTimeKey);
      if (cacheTime != null) {
        final DateTime cacheDateTime =
            DateTime.fromMillisecondsSinceEpoch(cacheTime);
        final Duration age = DateTime.now().difference(cacheDateTime);

        if (age > _cacheExpiration) {
          return false;
        }
      } else {
        return false;
      }

      return true;
    } catch (e) {
      debugPrint('Error checking cache validity: $e');
      return false;
    }
  }

  /// Get cache age in hours
  Future<int?> getCacheAgeHours() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      final int? cacheTime = prefs.getInt(_lastCacheTimeKey);
      if (cacheTime != null) {
        final DateTime cacheDateTime =
            DateTime.fromMillisecondsSinceEpoch(cacheTime);
        final Duration age = DateTime.now().difference(cacheDateTime);
        return age.inHours;
      }

      return null;
    } catch (e) {
      debugPrint('Error getting cache age: $e');
      return null;
    }
  }

  /// Clear the cache
  Future<void> clearCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_stationMarkersKey);
      await prefs.remove(_lastCacheTimeKey);
      await prefs.remove(_cacheVersionKey);
      debugPrint('Station markers cache cleared');
    } catch (e) {
      debugPrint('Error clearing station markers cache: $e');
    }
  }
}
