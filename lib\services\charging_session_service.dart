import 'dart:async';
import 'package:flutter/foundation.dart';
import '../core/api/api_service.dart';
import '../models/api_response.dart';

/// Service for managing charging sessions
class ChargingSessionService {
  final ApiService _apiService;

  // Singleton instance
  static final ChargingSessionService _instance =
      ChargingSessionService._internal();
  factory ChargingSessionService() => _instance;

  ChargingSessionService._internal() : _apiService = ApiService();

  /// Start a charging session
  ///
  /// Parameters:
  /// - evseUid: The unique ID of the EVSE (Electric Vehicle Supply Equipment)
  /// - connectorId: The ID of the connector to use
  /// - chargingValue: The amount of units to charge
  /// - instantCharging: Whether to use the entire wallet balance
  /// - promoId: Optional promo ID
  /// - promoCode: Optional promo code
  Future<ApiResponse<Map<String, dynamic>>> startChargingSession({
    required String evseUid,
    required String connectorId,
    required double chargingValue,
    required bool instantCharging,
    String? promoId,
    String? promoCode,
  }) async {
    try {
      // COMPREHENSIVE DEBUG LOGGING FOR INVESTIGATION
      print('🔍 ===== CHARGING SESSION START DEBUG =====');
      print('🔍 Input Parameters:');
      print(
          '  - EVSE UID: "$evseUid" (Type: ${evseUid.runtimeType}, Length: ${evseUid.length})');
      print('  - Connector ID: "$connectorId"');
      print('  - Charging Value: $chargingValue');
      print('  - Instant Charging: $instantCharging');
      print('  - Promo ID: $promoId');
      print('  - Promo Code: $promoCode');

      // Validate EVSE UID
      if (evseUid.isEmpty || evseUid == 'unknown' || evseUid == 'null') {
        print('❌ VALIDATION FAILED: Invalid EVSE UID detected');
        return ApiResponse<Map<String, dynamic>>(
          success: false,
          message: 'Invalid EVSE UID: "$evseUid"',
        );
      }

      // Prepare request parameters - EVSE UID goes ONLY in URL path, NOT in payload
      final Map<String, dynamic> params = {
        'charge_type': 'units',
        'charging_value': chargingValue,
        'instant_charging': instantCharging ? 1 : 0,
        'connector_id': connectorId,
      };

      // Add promo code and ID if available
      if (promoId != null && promoCode != null) {
        params['promo_id'] = promoId;
        params['promo_code'] = promoCode;
      }

      // Log the complete API request details
      final String fullEndpoint = '/user/sessions/start/$evseUid';
      print('🔍 API Request Details:');
      print('  - Full Endpoint: "$fullEndpoint"');
      print('  - Request Payload: $params');
      print('  - Base URL: https://api2.eeil.online/api/v1');
      print('  - Complete URL: https://api2.eeil.online/api/v1$fullEndpoint');

      // Make the API call using the exact endpoint format from the provided URL
      final response = await _apiService.post(
        fullEndpoint,
        data: params,
      );

      // Log the complete API response
      print('🔍 API Response Details:');
      print('  - Response Type: ${response.runtimeType}');
      print('  - Response Content: $response');
      if (response is Map) {
        print('  - Success Field: ${response['success']}');
        print('  - Message Field: ${response['message']}');
        print('  - Data Field: ${response['data']}');
        print('  - All Response Keys: ${response.keys.toList()}');
      }

      // Check if the API call was successful
      if (response['success'] == true) {
        return ApiResponse<Map<String, dynamic>>(
          success: true,
          message: response['message'] ?? '',
          data: response['data'] ?? {},
        );
      } else {
        return ApiResponse<Map<String, dynamic>>(
          success: false,
          message: response['message'] ?? '',
        );
      }
    } catch (e) {
      return ApiResponse<Map<String, dynamic>>(
        success: false,
        message: e.toString(),
      );
    }
  }

  /// Verify ongoing session - Step 2 of charging flow (onGetCurrentTransaction)
  ///
  /// Parameters:
  /// - transactionId: The ID from the start transaction response
  Future<ApiResponse<Map<String, dynamic>>> verifyOngoingSession({
    required String transactionId,
  }) async {
    try {
      // Make the API call using the exact endpoint format from the documentation
      final response = await _apiService.post(
        '/user/sessions/on-going',
        data: {'id': transactionId},
      );

      // Check if the API call was successful
      if (response['success'] == true) {
        final responseData = response['data'] ?? {};
        return ApiResponse<Map<String, dynamic>>(
          success: true,
          message: response['message'] ?? '',
          data: responseData,
        );
      } else {
        return ApiResponse<Map<String, dynamic>>(
          success: false,
          message: response['message'] ?? '',
        );
      }
    } catch (e) {
      return ApiResponse<Map<String, dynamic>>(
        success: false,
        message: e.toString(),
      );
    }
  }

  /// Get real-time charging data - Step 3 of charging flow (GetCharginDataViaApi)
  ///
  /// Parameters:
  /// - authorizationReference: The reference from the ongoing session response
  Future<ApiResponse<Map<String, dynamic>>> getChargingData({
    required String authorizationReference,
  }) async {
    try {
      // Comprehensive debug logging for API request
      debugPrint('🔍 ===== REAL-TIME DATA POLLING API REQUEST =====');
      debugPrint('🔍 Endpoint: GET /api/v1/user/sessions/on-going-data');
      debugPrint('🔍 Authorization Reference: "$authorizationReference"');
      debugPrint(
          '🔍 Query Parameters: authorization_reference=$authorizationReference');
      debugPrint(
          '🔍 Full URL: https://api2.eeil.online/api/v1/user/sessions/on-going-data?authorization_reference=$authorizationReference');

      // Validate authorization reference before making API call
      if (authorizationReference.isEmpty) {
        debugPrint('❌ CRITICAL ERROR: Empty authorization reference provided');
        return ApiResponse<Map<String, dynamic>>(
          success: false,
          message: 'Invalid authorization reference',
        );
      }

      // Make the API call using the exact endpoint format from the documentation
      final response = await _apiService.get(
        '/user/sessions/on-going-data',
        queryParams: {
          'authorization_reference': authorizationReference,
        },
      );

      // Comprehensive debug logging for API response
      debugPrint('🔍 ===== REAL-TIME DATA POLLING API RESPONSE =====');
      debugPrint('🔍 Response Type: ${response.runtimeType}');
      debugPrint('🔍 Response Content: $response');

      // Validate response structure
      if (response is! Map<String, dynamic>) {
        debugPrint(
            '❌ ERROR: Invalid response structure - expected Map<String, dynamic>');
        return ApiResponse<Map<String, dynamic>>(
          success: false,
          message: 'Invalid response format from server',
        );
      }

      // Log all response fields for debugging
      debugPrint('🔍 Response Keys: ${response.keys.toList()}');
      debugPrint('🔍 Success Field: ${response['success']}');
      debugPrint('🔍 Message Field: ${response['message']}');
      debugPrint('🔍 Data Field: ${response['data']}');

      // Check if the API call was successful
      if (response['success'] == true) {
        final responseData = response['data'] ?? {};

        // Log all data fields for comprehensive debugging
        if (responseData is Map<String, dynamic>) {
          debugPrint('🔍 ===== CHARGING SESSION DATA FIELDS =====');
          debugPrint('🔍 Status: ${responseData['status']}');
          debugPrint('🔍 SOC: ${responseData['soc']}');
          debugPrint('🔍 Energy: ${responseData['energy']}');
          debugPrint('🔍 Power: ${responseData['power']}');
          debugPrint('🔍 Amount: ${responseData['amount']}');
          debugPrint('🔍 CO2: ${responseData['co2']}');
          debugPrint('🔍 Unit: ${responseData['unit']}');
          debugPrint('🔍 Power Output: ${responseData['power_output']}');
          debugPrint('🔍 All Data Keys: ${responseData.keys.toList()}');
        }

        return ApiResponse<Map<String, dynamic>>(
          success: true,
          message: response['message'] ?? '',
          data: responseData,
        );
      } else {
        debugPrint('❌ API ERROR: ${response['message']}');
        return ApiResponse<Map<String, dynamic>>(
          success: false,
          message: response['message'] ?? 'Unknown API error',
        );
      }
    } catch (e) {
      debugPrint('❌ EXCEPTION in getChargingData: $e');
      debugPrint('❌ Exception Type: ${e.runtimeType}');
      return ApiResponse<Map<String, dynamic>>(
        success: false,
        message: e.toString(),
      );
    }
  }

  /// Stop a charging session - Step 4 of charging flow (onStopTransaction)
  ///
  /// Parameters:
  /// - transactionId: The ID of the transaction to stop
  Future<ApiResponse<Map<String, dynamic>>> stopChargingSession({
    required String transactionId,
  }) async {
    try {
      // Make the API call using the exact endpoint format from the documentation
      final response = await _apiService.post(
        '/user/transaction/remote-stop',
        data: {
          'transaction_id': transactionId,
        },
      );

      // Check if the API call was successful
      if (response['success'] == true) {
        return ApiResponse<Map<String, dynamic>>(
          success: true,
          message: response['message'] ?? '',
          data: response['data'] ?? {},
        );
      } else {
        return ApiResponse<Map<String, dynamic>>(
          success: false,
          message: response['message'] ?? '',
        );
      }
    } catch (e) {
      return ApiResponse<Map<String, dynamic>>(
        success: false,
        message: e.toString(),
      );
    }
  }

  /// Get the status of a charging session
  ///
  /// Parameters:
  /// - sessionId: The ID of the session to check
  Future<ApiResponse<Map<String, dynamic>>> getChargingSessionStatus({
    required String sessionId,
  }) async {
    try {
      // Make the API call
      final response =
          await _apiService.get('/user/sessions/$sessionId/status');

      // Check if the API call was successful
      if (response['success'] == true) {
        return ApiResponse<Map<String, dynamic>>(
          success: true,
          message: response['message'] ?? '',
          data: response['data'] ?? {},
        );
      } else {
        return ApiResponse<Map<String, dynamic>>(
          success: false,
          message: response['message'] ?? '',
        );
      }
    } catch (e) {
      return ApiResponse<Map<String, dynamic>>(
        success: false,
        message: e.toString(),
      );
    }
  }
}

/// Comprehensive charging flow manager that handles the complete charging process
/// according to the backend logic provided - matches JavaScript implementation exactly
class ChargingFlowManager {
  final ChargingSessionService _chargingService;
  Timer? _dataPollingTimer;

  // Singleton instance
  static final ChargingFlowManager _instance = ChargingFlowManager._internal();
  factory ChargingFlowManager() => _instance;

  ChargingFlowManager._internal() : _chargingService = ChargingSessionService();

  /// Complete charging flow implementation with AUTHENTIC API DATA
  ///
  /// This method handles the entire charging process using authentic station detail API data:
  /// 1. Start charging session with authentic parameters (onStartTransaction)
  /// 2. Verify ongoing session (onGetCurrentTransaction)
  /// 3. Start polling for real-time data (GetCharginDataViaApi)
  /// 4. Handle session completion
  Future<void> startCompleteChargingFlow({
    required String evseUid,
    required String connectorId,
    required double chargingValue,
    required bool instantCharging,
    required String chargeType, // 'units' or 'amount'
    required double walletBalance,
    required double pricePerUnit,
    String? promoId,
    String? promoCode,
    required Function(Map<String, dynamic>) onDataReceived,
    required Function(String) onError,
    required Function() onSessionComplete,
    required Function(Map<String, dynamic>) onSessionVerified,
  }) async {
    try {
      // Calculate value using backend logic
      double calculatedValue = chargingValue;

      // Match backend logic exactly:
      if (instantCharging) {
        calculatedValue = walletBalance;
      }

      // Convert from monetary amount to units if needed (exactly as backend does)
      if (chargeType == 'amount' || instantCharging) {
        if (pricePerUnit > 0) {
          calculatedValue =
              double.parse((calculatedValue / pricePerUnit).toStringAsFixed(2));
        }
      }

      // Step 1: Start charging session
      final startResponse = await _chargingService.startChargingSession(
        evseUid: evseUid,
        connectorId: connectorId,
        chargingValue: calculatedValue,
        instantCharging: instantCharging,
        promoId: promoId,
        promoCode: promoCode,
      );

      if (!startResponse.success || startResponse.data == null) {
        onError(startResponse.message);
        return;
      }

      // Extract transaction ID from response data
      final transactionId = startResponse.data!['id'];
      if (transactionId == null) {
        onError('Transaction ID not found in response');
        return;
      }

      // Step 2: Get current transaction (matching backend onGetCurrentTransaction)
      final verifyResponse = await _chargingService.verifyOngoingSession(
        transactionId: transactionId,
      );

      if (!verifyResponse.success) {
        // Pass exact server-side error message without interpretation
        onError(verifyResponse.message);
        return;
      }

      if (verifyResponse.data == null || verifyResponse.data!.isEmpty) {
        // Pass exact server message without interpretation
        onError(verifyResponse.message);
        return;
      }

      // Extract authorization reference with proper error handling
      final authorizationReference =
          verifyResponse.data!['authorization_reference'];
      if (authorizationReference == null ||
          authorizationReference.toString().isEmpty) {
        // Pass server message if available, otherwise empty string (no interpretation)
        onError(verifyResponse.message);
        return;
      }

      final String authRef = authorizationReference.toString();

      // Notify that session is verified (matching backend alert)
      onSessionVerified(verifyResponse.data!);

      // Step 3: Start polling for real-time charging data (matching backend GetCharginDataViaApi)
      await startChargingDataPolling(
        authorizationReference: authRef,
        transactionId: transactionId,
        onDataReceived: onDataReceived,
        onError: onError,
        onSessionComplete: onSessionComplete,
      );
    } catch (e) {
      onError(e.toString());
    }
  }

  /// Start polling for real-time charging data (matching backend GetCharginDataViaApi)
  Future<void> startChargingDataPolling({
    required String authorizationReference,
    required String transactionId,
    required Function(Map<String, dynamic>) onDataReceived,
    required Function(String) onError,
    required Function() onSessionComplete,
  }) async {
    debugPrint('🔍 ===== STARTING REAL-TIME DATA POLLING =====');
    debugPrint('🔍 Authorization Reference: "$authorizationReference"');
    debugPrint('🔍 Transaction ID: "$transactionId"');
    debugPrint('🔍 Polling Interval: 15 seconds');
    debugPrint('🔍 API Endpoint: GET /api/v1/user/sessions/on-going-data');

    // Validate inputs before starting polling
    if (authorizationReference.isEmpty) {
      debugPrint(
          '❌ CRITICAL ERROR: Empty authorization reference - cannot start polling');
      onError('Invalid authorization reference for polling');
      return;
    }

    if (transactionId.isEmpty) {
      debugPrint(
          '❌ CRITICAL ERROR: Empty transaction ID - cannot start polling');
      onError('Invalid transaction ID for polling');
      return;
    }

    // Cancel any existing timer
    if (_dataPollingTimer?.isActive == true) {
      debugPrint('🔄 Cancelling existing polling timer');
      _dataPollingTimer?.cancel();
    }

    debugPrint('✅ Starting immediate first polling call');

    // First immediate call (matching backend logic)
    await _fetchChargingData(
      authorizationReference: authorizationReference,
      transactionId: transactionId,
      onDataReceived: onDataReceived,
      onError: onError,
      onSessionComplete: onSessionComplete,
    );

    debugPrint('✅ Setting up periodic polling timer (15 seconds)');

    // Set up periodic polling every 15 seconds as per backend
    _dataPollingTimer = Timer.periodic(const Duration(seconds: 15), (_) async {
      debugPrint('⏰ Periodic polling timer triggered');
      await _fetchChargingData(
        authorizationReference: authorizationReference,
        transactionId: transactionId,
        onDataReceived: onDataReceived,
        onError: onError,
        onSessionComplete: onSessionComplete,
      );
    });

    debugPrint('✅ Real-time data polling started successfully');
  }

  /// Fetch charging data from the API (matching backend GetCharginDataViaApi logic)
  Future<void> _fetchChargingData({
    required String authorizationReference,
    required String transactionId,
    required Function(Map<String, dynamic>) onDataReceived,
    required Function(String) onError,
    required Function() onSessionComplete,
  }) async {
    try {
      debugPrint('🔍 ===== POLLING ATTEMPT =====');
      debugPrint('🔍 Authorization Reference: "$authorizationReference"');
      debugPrint('🔍 Transaction ID: "$transactionId"');
      debugPrint('🔍 Polling Timer Active: ${_dataPollingTimer?.isActive}');

      final response = await _chargingService.getChargingData(
        authorizationReference: authorizationReference,
      );

      debugPrint('🔍 ===== POLLING RESPONSE =====');
      debugPrint('🔍 Response Success: ${response.success}');
      debugPrint('🔍 Response Message: "${response.message}"');
      debugPrint('🔍 Response Data: ${response.data}');

      if (response.success && response.data != null) {
        final sessionData = response.data!;
        final sessionStatus = sessionData['status'];

        debugPrint('🔍 ===== SESSION STATUS CHECK =====');
        debugPrint('🔍 Session Status: "$sessionStatus"');
        debugPrint('🔍 Is Active: ${sessionStatus == 'ACTIVE'}');

        // Extract and log all important session data fields
        final soc = sessionData['soc'];
        final energy = sessionData['energy'];
        final power = sessionData['power'];
        final amount = sessionData['amount'];
        final co2 = sessionData['co2'];
        final unit = sessionData['unit'];
        final powerOutput = sessionData['power_output'];

        debugPrint('🔍 ===== SESSION DATA SUMMARY =====');
        debugPrint('🔍 SOC: $soc');
        debugPrint('🔍 Energy: $energy');
        debugPrint('🔍 Power: $power');
        debugPrint('🔍 Amount: $amount');
        debugPrint('🔍 CO2: $co2');
        debugPrint('🔍 Unit: $unit');
        debugPrint('🔍 Power Output: $powerOutput');

        // Check if charging is still active (exactly matching backend logic)
        if (sessionStatus == 'ACTIVE') {
          debugPrint('✅ Session is ACTIVE - continuing polling');
          onDataReceived(sessionData);
        } else {
          // Charging is no longer active (matching backend logic)
          debugPrint('🔄 Session status changed to: $sessionStatus');
          debugPrint('🛑 Stopping polling and completing session');
          stopChargingDataPolling();
          onSessionComplete();
        }
      } else {
        debugPrint('❌ POLLING ERROR: ${response.message}');
        debugPrint('❌ Response Success: ${response.success}');
        debugPrint('❌ Response Data: ${response.data}');

        // Check if this is a network timeout or API error
        if (response.message.toLowerCase().contains('timeout') ||
            response.message.toLowerCase().contains('connection')) {
          debugPrint('🔄 Network error detected - continuing polling');
          // Don't stop polling for network errors, let it retry
        } else {
          debugPrint('🛑 API error detected - stopping polling');
          stopChargingDataPolling();
        }

        onError(response.message);
      }
    } catch (e) {
      debugPrint('❌ EXCEPTION in _fetchChargingData: $e');
      debugPrint('❌ Exception Type: ${e.runtimeType}');

      // Check if this is a network-related exception
      if (e.toString().toLowerCase().contains('timeout') ||
          e.toString().toLowerCase().contains('connection') ||
          e.toString().toLowerCase().contains('socket')) {
        debugPrint('🔄 Network exception detected - continuing polling');
        // Don't stop polling for network exceptions, let it retry
      } else {
        debugPrint('🛑 Critical exception detected - stopping polling');
        stopChargingDataPolling();
      }

      // Pass the raw error without interpretation
      onError(e.toString());
    }
  }

  /// Stop charging session remotely (matching backend onStopTransaction)
  Future<ApiResponse<Map<String, dynamic>>> stopChargingSession({
    required String transactionId,
    required Function(String) onError,
    required Function(Map<String, dynamic>) onSessionStopped,
  }) async {
    try {
      final response = await _chargingService.stopChargingSession(
        transactionId: transactionId,
      );

      if (response.success) {
        // Stop polling immediately
        stopChargingDataPolling();

        // Pass final billing data to callback
        if (response.data != null) {
          onSessionStopped(response.data!);
        }

        return response;
      } else {
        // Pass exact server error message
        onError(response.message);
        return response;
      }
    } catch (e) {
      // Pass raw error without interpretation
      onError(e.toString());
      return ApiResponse<Map<String, dynamic>>(
        success: false,
        message: e.toString(),
      );
    }
  }

  /// Stop the data polling timer
  void stopChargingDataPolling() {
    debugPrint('🔍 ===== STOPPING REAL-TIME DATA POLLING =====');
    debugPrint('🔍 Timer Active: ${_dataPollingTimer?.isActive}');

    if (_dataPollingTimer?.isActive == true) {
      debugPrint('🛑 Cancelling active polling timer');
      _dataPollingTimer?.cancel();
    } else {
      debugPrint('ℹ️ No active polling timer to cancel');
    }

    _dataPollingTimer = null;
    debugPrint('✅ Real-time data polling stopped successfully');
  }

  /// Clean up resources
  void dispose() {
    stopChargingDataPolling();
  }
}
