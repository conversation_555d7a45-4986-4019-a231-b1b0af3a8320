import 'dart:async';
import 'package:flutter/material.dart';
import '../../utils/app_theme.dart';

/// A search widget for filtering vehicles
/// Features:
/// - Debounced search to optimize performance
/// - Clear button to reset search
/// - Loading indicator during search
/// - Animated transitions
class VehicleSearchWidget extends StatefulWidget {
  /// Callback function when search query changes
  final Function(String) onSearch;

  /// Whether search is currently in progress
  final bool isSearching;

  const VehicleSearchWidget({
    super.key,
    required this.onSearch,
    this.isSearching = false,
  });

  @override
  State<VehicleSearchWidget> createState() => _VehicleSearchWidgetState();
}

class _VehicleSearchWidgetState extends State<VehicleSearchWidget>
    with SingleTickerProviderStateMixin {
  // Text controller for the search field
  final TextEditingController _searchController = TextEditingController();

  // Focus node for the search field
  final FocusNode _searchFocusNode = FocusNode();

  // Debounce timer for search optimization
  Timer? _debounceTimer;

  // Animation controller for the search field
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    // Create fade animation
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );

    // Start animation
    _animationController.forward();

    // Add listener to search controller
    _searchController.addListener(_onSearchChanged);

    // Add listener to focus node
    _searchFocusNode.addListener(() {
      setState(() {
        // Update UI when focus changes
      });
    });
  }

  @override
  void dispose() {
    // Cancel debounce timer
    _debounceTimer?.cancel();

    // Dispose controllers
    _searchController.dispose();
    _searchFocusNode.dispose();
    _animationController.dispose();

    super.dispose();
  }

  // Handle search text changes with debouncing
  void _onSearchChanged() {
    // Cancel previous timer if it exists
    if (_debounceTimer?.isActive ?? false) {
      _debounceTimer!.cancel();
    }

    // Create new timer
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      // Call search callback with current text
      widget.onSearch(_searchController.text.trim());
    });
  }

  // Clear search field
  void _clearSearch() {
    _searchController.clear();
    widget.onSearch('');
    _searchFocusNode.unfocus();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        decoration: BoxDecoration(
          color: isDarkMode ? Colors.grey.shade800 : Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(13), // 0.05 * 255 = ~13
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: TextField(
          controller: _searchController,
          focusNode: _searchFocusNode,
          decoration: InputDecoration(
            hintText: 'Search vehicles...',
            hintStyle: TextStyle(
              color: isDarkMode ? Colors.grey.shade400 : Colors.grey.shade500,
              fontSize: 16,
            ),
            prefixIcon: Icon(
              Icons.search,
              color: isDarkMode ? Colors.grey.shade400 : Colors.grey.shade500,
            ),
            suffixIcon: _buildSuffixIcon(),
            border: InputBorder.none,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 16,
            ),
          ),
          style: TextStyle(
            color: isDarkMode ? Colors.white : Colors.black,
            fontSize: 16,
          ),
          onSubmitted: (value) {
            // Immediately search when user presses enter
            widget.onSearch(value.trim());
          },
        ),
      ),
    );
  }

  // Build suffix icon based on state
  Widget _buildSuffixIcon() {
    // Show loading indicator if searching
    if (widget.isSearching) {
      return Padding(
        padding: const EdgeInsets.all(12.0),
        child: SizedBox(
          width: 20,
          height: 20,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
          ),
        ),
      );
    }

    // Show clear button if there is text
    if (_searchController.text.isNotEmpty) {
      return IconButton(
        icon: const Icon(Icons.clear),
        color: Colors.grey.shade500,
        onPressed: _clearSearch,
      );
    }

    // No suffix icon if empty
    return const SizedBox.shrink();
  }
}
