name: ecoplug
description: "A new Flutter project."
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'  # Modified to be more flexible with SDK versions

dependencies:
  flutter:
    sdk: flutter
  flutter_secure_storage: ^9.0.0
  percent_indicator: ^4.2.5
  cached_network_image: ^3.3.0
  lottie: ^3.3.1
  geolocator: ^14.0.0
  http: ^1.1.0  # Updated to match flutter_map's requirement
  webview_flutter: ^4.10.0
  google_nav_bar: ^5.0.6
  line_icons: ^2.0.3
  google_maps_flutter: ^2.5.0
  cupertino_icons: ^1.0.2
  webview_flutter_plus: ^0.4.12
  webview_flutter_android: ^4.3.4  # Updated to compatible version
  url_launcher: ^6.1.14
  font_awesome_flutter: ^10.5.0
  intl: ^0.20.2
  shimmer: ^3.0.0
  image_picker: ^1.1.2
  shared_preferences: ^2.5.3
  sms_autofill: ^2.4.1
  jwt_decoder: ^2.0.1
  connectivity_plus: ^5.0.2  # Downgraded for compatibility
  dio: ^5.4.1
  retry: ^3.1.2
  provider: ^6.1.1
  flutter_riverpod: ^2.6.1 # Add Riverpod
  riverpod_annotation: ^2.6.1 # Add Riverpod annotation
  device_info_plus: ^9.1.2  # For collecting device information
  package_info_plus: ^8.3.0  # Upgraded as per suggestion
  internet_connection_checker: ^1.0.0+1  # For more reliable connection checking
  freezed_annotation: ^2.4.1
  json_annotation: ^4.8.1
  collection: ^1.18.0
  # Core dependencies
  crypto: ^3.0.6
  uuid: ^4.5.1
  flutter_svg: ^2.1.0
  flutter_staggered_animations: ^1.1.1
  flutter_inappwebview: ^6.1.5
  path_provider: ^2.1.2
  fl_chart: ^0.69.0  # For analytics charts and graphs

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  mockito: ^5.4.2
  build_runner: ^2.4.15
  freezed: ^2.4.5
  json_serializable: ^6.7.1
  custom_lint: ^0.7.3 # Add custom_lint for Riverpod linting
  riverpod_generator: ^2.6.1 # Add Riverpod generator
  riverpod_lint: ^2.6.4

flutter:
  uses-material-design: true
  assets:
    - assets/images/ecoplug_logo.png
    - assets/images/ecoplug_logo_dark.png
    - assets/images/ecoplug_logo_dark_fixed.png
    # Removed local connector icon assets to ensure API-only usage
    - assets/images/EV.charger.png
    - assets/images/charging stations image/
    - assets/images/india_flag.png
    - assets/RFID_CARDS/RFIDCARDS_1.png
    - assets/RFID_CARDS/RFIDCARDS_2.png
    - assets/markers/
