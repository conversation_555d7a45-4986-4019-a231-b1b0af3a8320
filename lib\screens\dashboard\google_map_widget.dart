import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart' as geo;

import '../../services/persistent_marker_service.dart'; // Import the PersistentMarkerService
import '../../providers/dashboard_notifier.dart';

class GoogleMapWidget extends ConsumerStatefulWidget {
  final void Function(LatLng)? onTap;
  final List<Map<String, dynamic>>? stations;
  final Function(Map<String, dynamic>)? onStationSelected;
  final Function(double, double)? onLocationUpdated;
  final Function(CameraPosition)? onCameraPositionChanged;
  final double? initialLatitude;
  final double? initialLongitude;
  final double? initialZoom;
  final bool showLocationButton;

  const GoogleMapWidget({
    super.key,
    this.onTap,
    this.stations,
    this.onStationSelected,
    this.onLocationUpdated,
    this.onCameraPositionChanged,
    this.initialLatitude,
    this.initialLongitude,
    this.initialZoom,
    this.showLocationButton = true,
  });

  @override
  ConsumerState<GoogleMapWidget> createState() => GoogleMapWidgetState();
}

class GoogleMapWidgetState extends ConsumerState<GoogleMapWidget> {
  // Google Maps controller
  final Completer<GoogleMapController> _controller = Completer();
  GoogleMapController? _mapController;

  // Map state
  bool _isMapInitialized = false;
  Set<Marker> _markers = {};

  // Location state
  bool _locationPermissionGranted = false;
  bool _isLoadingLocation = false;

  // Currently selected station ID
  String? _selectedStationId;

  // Location tracking
  StreamSubscription<geo.Position>? _positionStreamSubscription;
  bool _isLocationTrackingEnabled = false;

  // Use PersistentMarkerService directly for robust marker handling
  final _persistentMarkerService = PersistentMarkerService();

  @override
  void initState() {
    super.initState();
    _checkLocationPermission();
    _initializeMap();
    _initializeMarkerService();
    _updateMarkers();
  }

  // Initialize the marker service
  Future<void> _initializeMarkerService() async {
    await _persistentMarkerService.initialize();
  }

  void _initializeMap() async {
    // Always center on India by default, regardless of provided coordinates
    // This ensures we show India map by default as requested
    double initialLat = 20.5937; // Center of India
    double initialLng = 78.9629; // Center of India
    double initialZoom = 5.0; // Zoom level to show most of India

    if (_mapController != null) {
      await _mapController!.animateCamera(
        CameraUpdate.newCameraPosition(
          CameraPosition(
            target: LatLng(initialLat, initialLng),
            zoom: initialZoom,
          ),
        ),
      );
      if (mounted) {
        setState(() {
          _isMapInitialized = true;
        });
      }

      // We don't automatically get user location on map initialization
      // This gives the user full control over the map location
      // They can use the location button if they want to see their current location
    }
  }

  @override
  void dispose() {
    // Cancel location tracking subscription when widget is disposed
    _stopLocationTracking();

    // Dispose of the map controller
    _mapController?.dispose();

    // Note: We don't dispose the PersistentMarkerService because it's a singleton
    // and might be used by other components. This helps maintain the cache.

    super.dispose();
  }

  @override
  void didUpdateWidget(GoogleMapWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Update markers if stations changed
    if (widget.stations != oldWidget.stations) {
      _updateMarkers();
    }
  }

  // Check if location permission is granted
  Future<void> _checkLocationPermission() async {
    try {
      // Check if location services are enabled
      bool serviceEnabled = await geo.Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        debugPrint('Location services are disabled');
        return;
      }

      // Check location permission
      geo.LocationPermission permission =
          await geo.Geolocator.checkPermission();
      if (permission == geo.LocationPermission.denied) {
        permission = await geo.Geolocator.requestPermission();
        if (permission == geo.LocationPermission.denied) {
          debugPrint('Location permissions are denied');
          return;
        }
      }

      if (permission == geo.LocationPermission.deniedForever) {
        debugPrint('Location permissions are permanently denied');
        return;
      }

      // Permission granted
      if (mounted) {
        setState(() {
          _locationPermissionGranted = true;
        });
      }

      // Start location tracking if map is initialized
      if (_isMapInitialized) {
        _startLocationTracking();
      }
    } catch (e) {
      debugPrint('Error checking location permission: $e');
    }
  }

  // Start tracking user's location
  void _startLocationTracking() {
    if (_isLocationTrackingEnabled || !_locationPermissionGranted) {
      return;
    }

    try {
      const locationSettings = geo.LocationSettings(
        accuracy: geo.LocationAccuracy.high,
        distanceFilter: 10, // Update location if user moves more than 10 meters
      );

      _positionStreamSubscription = geo.Geolocator.getPositionStream(
        locationSettings: locationSettings,
      ).listen((geo.Position position) {
        if (mounted) {
          setState(() {
            // Update location
          });
        }

        // Notify parent of location update
        if (widget.onLocationUpdated != null) {
          widget.onLocationUpdated!(position.latitude, position.longitude);
        }
      });

      _isLocationTrackingEnabled = true;
      debugPrint('Started location tracking');
    } catch (e) {
      debugPrint('Error starting location tracking: $e');
    }
  }

  // Stop tracking user's location
  void _stopLocationTracking() {
    if (_positionStreamSubscription != null) {
      _positionStreamSubscription!.cancel();
      _positionStreamSubscription = null;
      _isLocationTrackingEnabled = false;
      debugPrint('Stopped location tracking');
    }
  }

  // Method to focus on a specific location
  Future<void> focusOnLocation(double latitude, double longitude,
      [double zoom = 15.0]) async {
    if (_mapController == null) {
      debugPrint('Cannot focus on location: map controller is null');
      return;
    }

    try {
      // Animate to the specified location
      await _mapController!.animateCamera(
        CameraUpdate.newCameraPosition(
          CameraPosition(
            target: LatLng(latitude, longitude),
            zoom: zoom,
          ),
        ),
      );
      debugPrint('Focused on location: $latitude, $longitude with zoom $zoom');
    } catch (e) {
      debugPrint('Error focusing on location: $e');
    }
  }

  // Add a custom marker to the map
  void addCustomMarker(Marker marker) {
    if (mounted) {
      setState(() {
        // Create a new set with all existing markers plus the new one
        final newMarkers = Set<Marker>.from(_markers);

        // Remove any existing marker with the same ID
        newMarkers.removeWhere((m) => m.markerId == marker.markerId);

        // Add the new marker
        newMarkers.add(marker);

        // Update the markers
        _markers = newMarkers;
      });
    }

    debugPrint('Added custom marker with ID: ${marker.markerId.value}');
  }

  // Method to focus on user's current location
  void focusOnUserLocation() {
    handleLocationButtonPress();
  }

  // Public method to select a station from external calls
  void selectStation(Map<String, dynamic> station) {
    _selectStation(station);
  }

  void _selectStation(Map<String, dynamic> station) {
    debugPrint(
        'GoogleMapWidget._selectStation called for station: ${station['name']}');

    // Get the station ID
    final String id = station['id'].toString();

    // Log the UID if available (important for debugging missing UIDs)
    final String? uid = station['uid']?.toString();
    if (uid != null && uid.isNotEmpty) {
      debugPrint('Selected station UID: $uid');
    } else {
      debugPrint(
          'WARNING: Selected station has no UID - will attempt to find one');
    }

    // Update the selected station ID
    if (mounted) {
      setState(() {
        _selectedStationId = id;
      });
    }

    // Fly to the selected station's location
    final double latitude = station['latitude'] as double? ?? 0.0;
    final double longitude = station['longitude'] as double? ?? 0.0;

    // Focus on the selected station's location
    focusOnLocation(latitude, longitude, 15.0);

    // Update markers to show focused icon for selected station
    _updateMarkers();

    // Call the onStationSelected callback AFTER focusing on the location
    // This ensures the map is already centered when nearest stations are loaded
    if (widget.onStationSelected != null) {
      debugPrint(
          'Calling onStationSelected callback with station: ${station['name']}');

      // Always try to enrich the station with UID, even if it already has one
      // This ensures we have the most up-to-date UID from the nearest stations API
      _enrichStationWithUid(station).then((enrichedStation) {
        // Log the enriched station UID for debugging
        final String? enrichedUid = enrichedStation['uid']?.toString();
        if (enrichedUid != null && enrichedUid.isNotEmpty) {
          debugPrint('Enriched station UID: $enrichedUid');
          if (uid != enrichedUid) {
            debugPrint('UID changed during enrichment: $uid -> $enrichedUid');
          }
        } else {
          debugPrint('WARNING: Failed to enrich station with UID');
        }

        widget.onStationSelected!(enrichedStation);
      });
    } else {
      debugPrint('WARNING: onStationSelected callback is null!');
    }
  }

  // Attempt to find a UID for a station based on its coordinates and other properties
  Future<Map<String, dynamic>> _enrichStationWithUid(
      Map<String, dynamic> station) async {
    try {
      final double latitude = station['latitude'] as double? ?? 0.0;
      final double longitude = station['longitude'] as double? ?? 0.0;
      final String stationName =
          (station['name'] as String? ?? '').toLowerCase();
      final String stationAddress =
          (station['address'] as String? ?? '').toLowerCase();
      final String stationId = station['id']?.toString() ?? '';

      if (latitude == 0.0 || longitude == 0.0) {
        debugPrint('Cannot enrich station - invalid coordinates');
        return station;
      }

      // Create a new map that we can modify
      final Map<String, dynamic> enrichedStation =
          Map<String, dynamic>.from(station);

      // Use the dashboard provider to get nearest stations that might have the UID
      final dashboardState = ref.read(dashboardNotifierProvider);
      final nearestStations = dashboardState.formattedNearestStations;

      if (nearestStations.isEmpty) {
        debugPrint('No nearest stations available for UID enrichment');
        return enrichedStation;
      }

      debugPrint(
          'Searching for UID match among ${nearestStations.length} nearest stations');

      // Log the first few nearest stations for debugging
      for (int i = 0; i < math.min(3, nearestStations.length); i++) {
        final s = nearestStations[i];
        debugPrint('Nearest station $i: name=${s['name']}, uid=${s['uid']}, '
            'lat=${s['latitude']}, lng=${s['longitude']}');
      }

      // Try multiple matching strategies in priority order

      // 0. First try matching by ID if available
      if (stationId.isNotEmpty) {
        var matchingStation = nearestStations.firstWhere(
          (s) =>
              s['id']?.toString() == stationId ||
              s['stationId']?.toString() == stationId,
          orElse: () => {},
        );

        if (matchingStation.isNotEmpty) {
          debugPrint('Found matching station by ID: $stationId');
          _updateEnrichedStation(enrichedStation, matchingStation);
          return enrichedStation;
        }
      }

      // 1. Try exact coordinate match with wider threshold
      final double coordinateThreshold = 0.001; // About 100 meters
      var matchingStation = nearestStations.firstWhere(
        (s) {
          final stationLat = s['latitude'] as double? ?? 0.0;
          final stationLng = s['longitude'] as double? ?? 0.0;

          return (stationLat - latitude).abs() < coordinateThreshold &&
              (stationLng - longitude).abs() < coordinateThreshold;
        },
        orElse: () => {},
      );

      if (matchingStation.isNotEmpty) {
        debugPrint('Found matching station by coordinates');
        _updateEnrichedStation(enrichedStation, matchingStation);
        return enrichedStation;
      }

      // 2. Try matching by name
      if (stationName.isNotEmpty) {
        matchingStation = nearestStations.firstWhere(
          (s) {
            final name = (s['name'] as String? ?? '').toLowerCase();
            return name.isNotEmpty &&
                (name == stationName ||
                    name.contains(stationName) ||
                    stationName.contains(name));
          },
          orElse: () => {},
        );

        if (matchingStation.isNotEmpty) {
          debugPrint('Found matching station by name');
          _updateEnrichedStation(enrichedStation, matchingStation);
          return enrichedStation;
        }
      }

      // 3. Try matching by address
      if (stationAddress.isNotEmpty) {
        matchingStation = nearestStations.firstWhere(
          (s) {
            final address = (s['address'] as String? ?? '').toLowerCase();
            return address.isNotEmpty &&
                (address == stationAddress ||
                    address.contains(stationAddress) ||
                    stationAddress.contains(address));
          },
          orElse: () => {},
        );

        if (matchingStation.isNotEmpty) {
          debugPrint('Found matching station by address');
          _updateEnrichedStation(enrichedStation, matchingStation);
          return enrichedStation;
        }
      }

      // 4. Try matching by partial address (if address is long enough)
      if (stationAddress.length > 10) {
        matchingStation = nearestStations.firstWhere(
          (s) {
            final address = (s['address'] as String? ?? '').toLowerCase();
            if (address.length < 10) return false;

            return address.contains(stationAddress.substring(0, 10)) ||
                stationAddress.contains(address.substring(0, 10));
          },
          orElse: () => {},
        );

        if (matchingStation.isNotEmpty) {
          debugPrint('Found matching station by partial address');
          _updateEnrichedStation(enrichedStation, matchingStation);
          return enrichedStation;
        }
      }

      // 5. Last resort: use the nearest station if it's reasonably close
      if (nearestStations.isNotEmpty) {
        final nearest = nearestStations.first;
        final nearestLat = nearest['latitude'] as double? ?? 0.0;
        final nearestLng = nearest['longitude'] as double? ?? 0.0;

        // Calculate distance between points (rough approximation)
        final latDiff = (nearestLat - latitude).abs();
        final lngDiff = (nearestLng - longitude).abs();
        final roughDistance = math.sqrt(latDiff * latDiff + lngDiff * lngDiff);

        // If within ~5km (very rough approximation)
        if (roughDistance < 0.05) {
          debugPrint(
              'Using nearest station as last resort (rough distance: ${roughDistance * 111} km)');
          _updateEnrichedStation(enrichedStation, nearest);
          return enrichedStation;
        }
      }

      debugPrint('No matching station found with UID after all strategies');
      return enrichedStation;
    } catch (e) {
      debugPrint('Error enriching station with UID: $e');
      // Return the original station if there was an error
      return station;
    }
  }

  // Helper method to update enriched station with data from matching station
  void _updateEnrichedStation(Map<String, dynamic> enrichedStation,
      Map<String, dynamic> matchingStation) {
    // Copy UID
    if (matchingStation['uid'] != null &&
        matchingStation['uid'].toString().isNotEmpty) {
      final String uid = matchingStation['uid'].toString();
      enrichedStation['uid'] = uid;
      debugPrint('Updated station with UID: $uid');
    }

    // Copy other useful fields that might be missing
    final fieldsToCheck = [
      'stationId',
      'types',
      'connectors',
      'openingTimes',
      'openStatus',
      'mapPinUrl',
      'focusedMapPinUrl'
    ];

    for (final field in fieldsToCheck) {
      if (enrichedStation[field] == null && matchingStation[field] != null) {
        enrichedStation[field] = matchingStation[field];
        debugPrint('Copied missing field from matching station: $field');
      }
    }
  }

  // Handle location button press
  Future<void> handleLocationButtonPress() async {
    if (!_locationPermissionGranted) {
      await _checkLocationPermission();
      if (!_locationPermissionGranted) {
        return;
      }
    }

    if (mounted) {
      setState(() {
        _isLoadingLocation = true;
      });
    }

    try {
      final position = await geo.Geolocator.getCurrentPosition(
        locationSettings: const geo.LocationSettings(
          accuracy: geo.LocationAccuracy.high,
          timeLimit: Duration(seconds: 5),
        ),
      );

      if (mounted) {
        setState(() {
          _isLoadingLocation = false;
        });
      }

      // Focus on user's current location
      await _focusOnLocation(position.latitude, position.longitude, 15.0);

      debugPrint(
          'Moved map to user location: ${position.latitude}, ${position.longitude}');
    } catch (e) {
      debugPrint('Error getting current location: $e');
      if (mounted) {
        setState(() {
          _isLoadingLocation = false;
        });
      }
    }
  }

  // Focus map on a specific location
  Future<void> _focusOnLocation(
      double latitude, double longitude, double zoom) async {
    if (_mapController != null) {
      await _mapController!.animateCamera(
        CameraUpdate.newCameraPosition(
          CameraPosition(
            target: LatLng(latitude, longitude),
            zoom: zoom,
          ),
        ),
      );
    }
  }

  // Update markers on the map using PersistentMarkerService for reliability
  Future<void> _updateMarkers() async {
    if (widget.stations == null || widget.stations!.isEmpty) {
      if (mounted) {
        setState(() {
          _markers = {};
        });
      }
      return;
    }

    Set<Marker> newMarkers = {};

    debugPrint('Updating markers for ${widget.stations!.length} stations');

    // First, preload all marker URLs to ensure they're available before creating markers
    // This helps prevent disappearing icons during long map viewing sessions
    await _preloadAllMarkerIcons();

    // Process stations in smaller batches for mobile devices
    const int batchSize =
        5; // Increased batch size from 3 to 5 for better efficiency
    for (int i = 0; i < widget.stations!.length; i += batchSize) {
      final int end = (i + batchSize < widget.stations!.length)
          ? i + batchSize
          : widget.stations!.length;

      final batch = widget.stations!.sublist(i, end);

      // Process this batch of stations sequentially for better reliability
      for (final station in batch) {
        final double latitude = station['latitude'] as double? ?? 0.0;
        final double longitude = station['longitude'] as double? ?? 0.0;
        final String status = station['status'] as String? ?? 'Available';
        final String id = station['id'].toString();

        // Store UID if available (important for navigation to details page)
        final String? uid = station['uid']?.toString();
        debugPrint('Station $id UID: $uid');

        if (latitude == 0.0 || longitude == 0.0) {
          debugPrint('Skipping station $id with invalid coordinates');
          continue;
        }

        try {
          // Use the persistent marker service to get reliable icons
          BitmapDescriptor markerIcon;

          try {
            // Determine if this is the selected station
            bool isSelected =
                _selectedStationId != null && _selectedStationId == id;

            // Get the appropriate marker URL with multiple fallback options
            String markerUrl = '';
            List<String> potentialUrls = [];

            // First try to use mapPinUrl from the station data
            if (station['mapPinUrl'] != null &&
                (station['mapPinUrl'] as String).isNotEmpty) {
              if (isSelected) {
                // For selected stations, prioritize focused versions
                if (station['focusedMapPinUrl'] != null &&
                    (station['focusedMapPinUrl'] as String).isNotEmpty) {
                  potentialUrls.add(station['focusedMapPinUrl'] as String);
                }

                // Add converted focused URL as fallback
                String defaultUrl = station['mapPinUrl'] as String;
                if (defaultUrl.contains('_default')) {
                  potentialUrls
                      .add(defaultUrl.replaceAll('_default', '_focus'));
                }

                // Add standard focus icon as last resort
                potentialUrls
                    .add('https://api2.eeil.online/mapicons/ecoplug_focus.png');
              } else {
                // For non-selected stations, use the provided mapPinUrl
                potentialUrls.add(station['mapPinUrl'] as String);
              }
            }

            // Add status-based fallback URLs
            if (status.toLowerCase().contains('unavailable')) {
              potentialUrls.add(
                  'https://api2.eeil.online/mapicons/ecoplug_unavailable.png');
            } else if (status.toLowerCase().contains('in use') ||
                status.toLowerCase().contains('charging')) {
              if (isSelected) {
                potentialUrls.add(
                    'https://api2.eeil.online/mapicons/ecoplug_charging_focus_new.png');
                potentialUrls.add(
                    'https://api2.eeil.online/mapicons/ecoplug_charging_focus.png');
              } else {
                potentialUrls.add(
                    'https://api2.eeil.online/mapicons/ecoplug_charging_default_new.png');
                potentialUrls.add(
                    'https://api2.eeil.online/mapicons/ecoplug_charging_default.png');
                potentialUrls.add(
                    'https://api2.eeil.online/mapicons/ecoplug_charging.png');
              }
            } else {
              if (isSelected) {
                potentialUrls
                    .add('https://api2.eeil.online/mapicons/ecoplug_focus.png');
              } else {
                potentialUrls.add(
                    'https://api2.eeil.online/mapicons/ecoplug_available.png');
                potentialUrls.add(
                    'https://api2.eeil.online/mapicons/ecoplug_default.png');
              }
            }

            // Try each URL in the potential URLs list until one works
            BitmapDescriptor? descriptor;
            for (final url in potentialUrls) {
              // Ensure URL starts with https://
              String normalizedUrl =
                  url.startsWith('http') ? url : 'https://$url';

              // Try to get the descriptor
              descriptor = await _persistentMarkerService
                  .getBitmapDescriptorFromUrl(normalizedUrl);
              if (descriptor != null) {
                markerUrl = normalizedUrl; // Remember which URL worked
                break;
              }
            }

            if (descriptor != null) {
              markerIcon = descriptor;
              // Log which URL was used
              debugPrint(
                  'Using marker icon from URL: $markerUrl for station $id');
            } else {
              // All URLs failed, use status-based fallback
              markerIcon = await _persistentMarkerService
                      .getMarkerDescriptorForStatus(status,
                          focused: isSelected,
                          // Force refresh if we're having failures
                          forceRefresh: true) ??
                  await _persistentMarkerService
                      .createCustomMarker(Colors.green);
              debugPrint('Using status-based fallback marker for station $id');
            }
          } catch (e) {
            debugPrint('Error creating marker icon for station $id: $e');
            // Use custom fallback markers instead of default Google markers
            try {
              // Create a custom marker programmatically based on status
              Color markerColor;
              if (status.toLowerCase().contains('unavailable')) {
                markerColor = Colors.red;
              } else if (status.toLowerCase().contains('in use') ||
                  status.toLowerCase().contains('charging')) {
                markerColor = Colors.blue;
              } else {
                markerColor = Colors.green;
              }

              // Use the persistent marker service to create a custom marker
              markerIcon = await _persistentMarkerService
                  .createCustomMarker(markerColor);
            } catch (fallbackError) {
              debugPrint(
                  'Error creating custom fallback marker: $fallbackError');
              // Last resort - create a minimal colored pixel marker
              final color = status.toLowerCase().contains('unavailable')
                  ? Colors.red
                  : Colors.green;
              markerIcon =
                  await _persistentMarkerService.createSimpleMarker(color);
            }
          }

          // Create marker with UID tag for navigation
          final marker = Marker(
            markerId: MarkerId(id),
            position: LatLng(latitude, longitude),
            icon: markerIcon,
            // Set anchor point for proper alignment
            anchor: const Offset(0.5, 0.5),
            infoWindow: InfoWindow(
              title: station['name'] as String? ?? 'Unknown Station',
              snippet:
                  '${station['distance']?.toStringAsFixed(2) ?? '0.0'} km • $status',
              onTap: () => _selectStation(station),
            ),
            onTap: () {
              _selectStation(station);
            },
            // Ensure we're passing the complete station data with UID
            consumeTapEvents: true,
          );

          // Add to markers set
          newMarkers.add(marker);
        } catch (e) {
          debugPrint('Error creating marker for station $id: $e');
        }

        // Small delay between processing each marker
        await Future.delayed(const Duration(
            milliseconds: 15)); // Reduced delay for better performance
      }

      // Update UI with available markers after each batch
      if (mounted) {
        setState(() {
          _markers = newMarkers;
        });

        // Delay between batches to let UI update
        await Future.delayed(const Duration(
            milliseconds: 50)); // Reduced delay for better performance
      } else {
        break; // Stop processing if widget is unmounted
      }
    }

    debugPrint('Finished updating ${_markers.length} markers');
  }

  // Preload all marker icons to ensure they don't disappear during long sessions
  Future<void> _preloadAllMarkerIcons() async {
    try {
      // Make sure the persistent marker service is initialized
      await _persistentMarkerService.initialize();

      // First, load the standard marker icon set
      await _persistentMarkerService.preloadCommonMarkers();

      // Then extract all unique marker URLs from stations
      final Set<String> allMarkerUrls = {};

      // Process all stations to collect marker URLs
      if (widget.stations != null) {
        for (final station in widget.stations!) {
          // Get standard pin URL
          if (station['mapPinUrl'] != null &&
              (station['mapPinUrl'] as String).isNotEmpty) {
            String url = station['mapPinUrl'] as String;
            if (!url.startsWith('http')) {
              url = 'https://$url';
            }
            allMarkerUrls.add(url);
          }

          // Get focused pin URL
          if (station['focusedMapPinUrl'] != null &&
              (station['focusedMapPinUrl'] as String).isNotEmpty) {
            String url = station['focusedMapPinUrl'] as String;
            if (!url.startsWith('http')) {
              url = 'https://$url';
            }
            allMarkerUrls.add(url);
          }
        }
      }

      // Preload in small batches
      final List<String> urlList = allMarkerUrls.toList();
      debugPrint('Preloading ${urlList.length} unique marker icons');

      const int preloadBatchSize = 3;
      for (int i = 0; i < urlList.length; i += preloadBatchSize) {
        final int end = (i + preloadBatchSize < urlList.length)
            ? i + preloadBatchSize
            : urlList.length;

        final batch = urlList.sublist(i, end);

        // Load images and descriptors in parallel for this small batch
        await Future.wait(batch.map((url) async {
          // Load image first
          await _persistentMarkerService.getMarkerImage(url);
          // Then create descriptors at different sizes
          await _persistentMarkerService.getBitmapDescriptorFromUrl(url);
        }));

        // Short delay between batches
        await Future.delayed(const Duration(milliseconds: 50));
      }

      // Get cache statistics
      final stats = _persistentMarkerService.getCacheStatistics();
      debugPrint(
          'Marker cache status: ${stats['imageCount']} images, ${stats['descriptorCount']} descriptors');
    } catch (e) {
      debugPrint('Error preloading marker icons: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Expanded(
          child: Stack(
            children: [
              GoogleMap(
                initialCameraPosition: CameraPosition(
                  target: LatLng(widget.initialLatitude ?? 20.5937,
                      widget.initialLongitude ?? 78.9629),
                  zoom: widget.initialZoom ?? 5.0,
                ),
                myLocationEnabled: _locationPermissionGranted,
                myLocationButtonEnabled: false,
                zoomControlsEnabled: false,
                markers: _markers,
                onMapCreated: (GoogleMapController controller) {
                  _mapController = controller;
                  _controller.complete(controller);
                  if (mounted) {
                    setState(() {
                      _isMapInitialized = true;
                    });
                  }

                  // Initialize markers once map is ready
                  _updateMarkers();
                },
              ),

              // REMOVED: Duplicate location button - using dashboard location button instead
              // This prevents duplicate target buttons and conflicting behaviors
            ],
          ),
        ),
        // Location button removed from here as it's already in the dashboard screen
      ],
    );
  }
}
