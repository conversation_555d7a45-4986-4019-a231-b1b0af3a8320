import 'package:flutter/material.dart';

class HistoryPage extends StatelessWidget {
  const HistoryPage({super.key});

  @override
  Widget build(BuildContext context) {
    // Example charging history data. Replace with real data from your backend.
    final List<Map<String, dynamic>> historyData = [
      {
        'stationName': 'Central Mall Charging Station',
        'date': DateTime.now().subtract(const Duration(hours: 2)),
        'cost': 199.50,
        'kwh': 12.5,
      },
      {
        'stationName': 'City Center EV Station',
        'date': DateTime.now().subtract(const Duration(days: 1, hours: 3)),
        'cost': 349.00,
        'kwh': 18.2,
      },
      {
        'stationName': 'Highway Plaza Charger',
        'date': DateTime.now().subtract(const Duration(days: 3, hours: 1)),
        'cost': 90.00,
        'kwh': 6.0,
      },
      {
        'stationName': 'E Plug energy Office DC Charger',
        'date': DateTime.now().subtract(const Duration(days: 5)),
        'cost': 210.75,
        'kwh': 14.3,
      },
      {
        'stationName': 'Ecoplug AC Charger Office',
        'date': DateTime.now().subtract(const Duration(days: 7)),
        'cost': 280.25,
        'kwh': 17.0,
      },
    ];

    // Example summary stats. You might compute these from history data.
    final totalSessions = historyData.length;
    final totalCost = historyData.fold<double>(
      0.0,
      (sum, item) => sum + (item['cost'] as double),
    );
    final totalKwh = historyData.fold<double>(
      0.0,
      (sum, item) => sum + (item['kwh'] as double),
    );

    return Scaffold(
      appBar: AppBar(
        title: const Text('History'),
        elevation: 0,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 1) Summary Card
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: const Color(0xFF67C44C),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  // Total Sessions
                  _buildSummaryItem(
                    title: 'Total Sessions',
                    value: '$totalSessions',
                  ),
                  // Total kWh
                  _buildSummaryItem(
                    title: 'Total kWh',
                    value: totalKwh.toStringAsFixed(1),
                  ),
                  // Total Spent
                  _buildSummaryItem(
                    title: 'Total Spent',
                    value: '₹${totalCost.toStringAsFixed(2)}',
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),
            // 2) Heading for "Recent Sessions"
            const Text(
              'Recent Sessions',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
            ),

            const SizedBox(height: 12),
            // 3) History List
            ListView.builder(
              physics: const NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              itemCount: historyData.length,
              itemBuilder: (context, index) {
                final item = historyData[index];
                return _buildHistoryCard(item);
              },
            ),
          ],
        ),
      ),
    );
  }

  // A small helper to build each summary item (Sessions, kWh, Spent).
  Widget _buildSummaryItem({required String title, required String value}) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Text(title, style: const TextStyle(color: Colors.white70)),
      ],
    );
  }

  // Build each history card with station name, date/time, cost, etc.
  Widget _buildHistoryCard(Map<String, dynamic> item) {
    final stationName = item['stationName'] as String? ?? 'Unknown Station';
    final cost = item['cost'] as double? ?? 0.0;
    final kwh = item['kwh'] as double? ?? 0.0;
    final date = item['date'] as DateTime? ?? DateTime.now();

    // Format date/time
    final dateStr = _formatDate(date);
    final timeStr = _formatTime(date);

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 6),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      elevation: 2,
      child: ListTile(
        contentPadding: const EdgeInsets.all(12),
        title: Text(
          stationName,
          style: const TextStyle(fontWeight: FontWeight.w600, fontSize: 16),
        ),
        subtitle: Text(
          '$dateStr • $timeStr\n$kwh kWh',
          style: const TextStyle(color: Colors.grey, height: 1.3),
        ),
        trailing: Text(
          '₹${cost.toStringAsFixed(2)}',
          style: const TextStyle(fontWeight: FontWeight.w600, fontSize: 16),
        ),
      ),
    );
  }

  // Format date as "25 Mar" or something similar
  String _formatDate(DateTime date) {
    // You can use intl package for localization, but here's a quick example:
    final months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];
    return '${date.day} ${months[date.month - 1]}';
  }

  // Format time as "2:30 PM" for example
  String _formatTime(DateTime date) {
    final hour = date.hour;
    final minute = date.minute.toString().padLeft(2, '0');
    final isAm = hour < 12;
    final displayHour = hour == 0 ? 12 : (hour > 12 ? hour - 12 : hour);

    return '$displayHour:$minute ${isAm ? 'AM' : 'PM'}';
  }
}
