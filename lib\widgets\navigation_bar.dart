import 'package:flutter/material.dart';
import 'package:google_nav_bar/google_nav_bar.dart';
import 'package:line_icons/line_icons.dart';
import '../screens/wallet/wallet_screen.dart';
import '../utils/app_themes.dart';
import '../screens/dashboard/dashboard_screen.dart';
import '../screens/Trip/trip_page.dart';
import '../screens/Profile/Profilescreen/profile_screen_riverpod.dart';
// Removed unused import
import '../services/service_locator.dart';

class MainNavigation extends StatefulWidget {
  const MainNavigation({super.key});

  @override
  State<MainNavigation> createState() => _MainNavigationState();
}

class _MainNavigationState extends State<MainNavigation> {
  int _selectedIndex = 0;

  @override
  void initState() {
    super.initState();
    // Preload wallet data in the background
    _preloadWalletData();
  }

  // Preload wallet data to ensure it's ready when user switches to wallet tab
  void _preloadWalletData() {
    // Fetch wallet data in the background without showing loading state
    ServiceLocator().walletRepositoryImpl.getWalletInfo();
  }

  // Get the current screen based on the selected index
  Widget _getCurrentScreen() {
    switch (_selectedIndex) {
      case 0:
        return DashboardScreen(); // Home tab
      case 1:
        return const WalletPage(); // Wallet tab - recreated each time for fresh data
      case 2:
        return TripPage(); // Trip tab
      case 3:
        return const ProfileScreenRiverpod(); // Profile tab
      default:
        return DashboardScreen();
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      body: AnimatedSwitcher(
        duration: const Duration(milliseconds: 300), // Faster transition
        transitionBuilder: (Widget child, Animation<double> animation) {
          return FadeTransition(opacity: animation, child: child);
        },
        child: KeyedSubtree(
          key: ValueKey<int>(_selectedIndex),
          child: _getCurrentScreen(),
        ),
      ),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: isDarkMode ? AppThemes.darkBackground : Colors.white,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
          boxShadow: [
            BoxShadow(
              blurRadius: 20,
              color: Colors.black.withAlpha(isDarkMode ? 50 : 26),
            ),
          ],
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.fromLTRB(15.0, 12, 15.0, 12),
            child: Container(
              decoration: BoxDecoration(
                color: isDarkMode
                    ? Color(
                        0xFF1E1E1E) // Slightly lighter than darkCard for better contrast
                    : Colors.grey.shade50,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: isDarkMode
                        ? Colors.black.withAlpha(30)
                        : Colors.black.withAlpha(10),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: GNav(
                  rippleColor:
                      isDarkMode ? Colors.grey[800]! : Colors.grey[300]!,
                  hoverColor:
                      isDarkMode ? Colors.grey[700]! : Colors.grey[100]!,
                  gap: 8,
                  activeColor: Colors.white,
                  iconSize: 24,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                  duration:
                      const Duration(milliseconds: 200), // Faster animation
                  tabBackgroundColor: AppThemes.primaryColor,
                  color: isDarkMode ? Colors.grey[400]! : Colors.grey[700]!,
                  textStyle: const TextStyle(
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                    fontSize: 14,
                  ),
                  curve: Curves.easeOutCubic,
                  tabs: const [
                    GButton(icon: LineIcons.home, text: 'Home'),
                    GButton(icon: LineIcons.wallet, text: 'Wallet'),
                    GButton(icon: LineIcons.car, text: 'Trips'),
                    GButton(icon: LineIcons.user, text: 'Profile'),
                  ],
                  selectedIndex: _selectedIndex,
                  onTabChange: (index) {
                    // If switching to wallet tab, preload data again
                    if (index == 1 && _selectedIndex != 1) {
                      _preloadWalletData();
                    }

                    setState(() {
                      _selectedIndex = index;
                    });
                  },
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
