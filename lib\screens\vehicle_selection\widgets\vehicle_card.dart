import 'package:flutter/material.dart';
import '../../../models/api_vehicle_model.dart';
import '../../../utils/app_theme.dart';

class VehicleCard extends StatelessWidget {
  final ApiVehicle vehicle;
  final Function(ApiVehicle) onTap;
  final bool isSelected;
  final Animation<double>? animation;

  const VehicleCard({
    super.key,
    required this.vehicle,
    required this.onTap,
    this.isSelected = false,
    this.animation,
  });

  @override
  Widget build(BuildContext context) {
    final Widget card = Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13), // 0.05 * 255 = ~13
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
        border: Border.all(
          color: isSelected
              ? AppTheme.primaryColor
              : Colors.grey.withAlpha(26), // 0.1 * 255 = ~26
          width: isSelected ? 2 : 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(16),
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () => onTap(vehicle),
          splashColor: AppTheme.primaryColor.withAlpha(26), // 0.1 * 255 = ~26
          highlightColor:
              AppTheme.primaryColor.withAlpha(13), // 0.05 * 255 = ~13
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Selected indicator
              if (isSelected)
                Align(
                  alignment: Alignment.topRight,
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: const BoxDecoration(
                      color: AppTheme.primaryColor,
                      borderRadius: BorderRadius.only(
                        topRight: Radius.circular(14),
                        bottomLeft: Radius.circular(14),
                      ),
                    ),
                    child: const Icon(
                      Icons.check,
                      color: Colors.white,
                      size: 16,
                    ),
                  ),
                ),

              // Vehicle image
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Hero(
                    tag: 'vehicle_${vehicle.id}',
                    child: vehicle.vehicleImage.contains('http')
                        ? Image.network(
                            vehicle.vehicleImage,
                            fit: BoxFit.contain,
                            errorBuilder: (context, error, stackTrace) {
                              return const Icon(
                                Icons.directions_car,
                                size: 60,
                                color: Colors.grey,
                              );
                            },
                          )
                        : const Icon(
                            Icons.directions_car,
                            size: 60,
                            color: Colors.grey,
                          ),
                  ),
                ),
              ),

              // Vehicle info
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  color: isSelected
                      ? AppTheme.primaryColor.withAlpha(26) // 0.1 * 255 = ~26
                      : AppTheme.backgroundColor,
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(16),
                    bottomRight: Radius.circular(16),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      vehicle.name,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: AppTheme.textPrimaryColor,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Battery: ${vehicle.batteryCapacity}',
                      style: TextStyle(
                        fontSize: 14,
                        color: AppTheme.textSecondaryColor
                            .withAlpha(204), // 0.8 * 255 = ~204
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );

    // Apply animation if provided
    if (animation != null) {
      return FadeTransition(
        opacity: animation!,
        child: SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(0, 0.1),
            end: Offset.zero,
          ).animate(CurvedAnimation(
            parent: animation!,
            curve: Curves.easeOut,
          )),
          child: card,
        ),
      );
    }

    return card;
  }
}
