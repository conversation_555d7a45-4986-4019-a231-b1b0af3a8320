import '../../../models/wallet/wallet_model.dart';
import '../../../models/wallet/transaction_model.dart';

/// Wallet state class
class WalletState {
  final bool isLoading;
  final WalletModel? wallet;
  final List<TransactionModel> transactions;
  final String? errorMessage;

  WalletState({
    this.isLoading = false,
    this.wallet,
    List<TransactionModel>? transactions,
    this.errorMessage,
  }) : transactions = transactions ?? <TransactionModel>[];

  // Initial state
  factory WalletState.initial() => WalletState();

  // Loading state
  factory WalletState.loading() => WalletState(isLoading: true);

  // Loaded state
  factory WalletState.loaded({
    required WalletModel wallet,
    required List<TransactionModel> transactions,
  }) =>
      WalletState(
        wallet: wallet,
        transactions: transactions,
      );

  // Error state
  factory WalletState.error(String message) => WalletState(
        errorMessage: message,
      );

  // Copy with method
  WalletState copyWith({
    bool? isLoading,
    WalletModel? wallet,
    List<TransactionModel>? transactions,
    String? errorMessage,
  }) {
    return WalletState(
      isLoading: isLoading ?? this.isLoading,
      wallet: wallet ?? this.wallet,
      transactions: transactions ?? this.transactions,
      errorMessage: errorMessage,
    );
  }
}
