import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../features/wallet/application/wallet_provider.dart';
import 'wallet_screen.dart';

/// This file re-exports the WalletPage from wallet_screen.dart
/// to maintain compatibility with existing code that imports wallet_screen_real.dart
class WalletScreenReal extends ConsumerWidget {
  const WalletScreenReal({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Always fetch fresh wallet data when this screen is shown
    // This ensures real-time data is loaded when the user navigates to this screen
    // No caching is used - data is always fetched directly from the server
    ref.read(walletProvider.notifier).fetchWallet();

    // Watch the wallet provider state
    final walletState = ref.watch(walletProvider);

    // Handle loading, error, and data states
    return walletState.when(
      loading: () => const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      ),
      error: (error, stackTrace) => Scaffold(
        appBar: AppBar(
          title: const Text('Wallet'),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                color: Colors.red,
                size: 60,
              ),
              const SizedBox(height: 16),
              Text(
                'Error loading wallet data',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 8),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 32),
                child: Text(
                  error.toString(),
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () {
                  // Retry fetching wallet data
                  ref.read(walletProvider.notifier).fetchWallet();
                },
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      ),
      data: (state) {
        // If we have a wallet state with an error, show the error screen
        if (state.errorMessage != null) {
          return Scaffold(
            appBar: AppBar(
              title: const Text('Wallet'),
              leading: IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: () => Navigator.of(context).pop(),
              ),
            ),
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    color: Colors.red,
                    size: 60,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Error loading wallet data',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 32),
                    child: Text(
                      state.errorMessage!,
                      textAlign: TextAlign.center,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () {
                      // Retry fetching wallet data
                      ref.read(walletProvider.notifier).fetchWallet();
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ),
            ),
          );
        }

        // If we have a loaded wallet state, show the wallet page
        return const WalletPage();
      },
    );
  }
}
