import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import '../../../shared/models/user.dart';
import '../../../models/api_response.dart';
import '../../../models/auth/auth_models.dart';
import '../domain/auth_state.dart';
import '../../../services/auth/auth_service.dart';
import '../../../providers/core_providers.dart';

/// Provider for authentication state management
final authProvider =
    StateNotifierProvider<AuthNotifier, AsyncValue<AuthState>>((ref) {
  final authService = ref.watch(authServiceProvider);
  return AuthNotifier(authService);
});

/// Authentication state notifier
class AuthNotifier extends StateNotifier<AsyncValue<AuthState>> {
  final AuthService _authService;

  AuthNotifier(this._authService) : super(const AsyncValue.loading()) {
    // Initialize the auth state
    _initializeAuthState();
  }

  /// Initialize the authentication state
  Future<void> _initializeAuthState() async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      // Check if user is already logged in
      final isLoggedIn = await _authService.isLoggedIn();
      if (isLoggedIn) {
        try {
          // Get user profile if logged in
          final userProfile = await _getUserProfile();
          if (userProfile != null) {
            return AuthState.authenticated(userProfile);
          }
        } catch (e) {
          debugPrint('Error getting user profile: $e');
        }
      }
      return AuthState.unauthenticated();
    });
  }

  /// Sign in with phone number and OTP
  Future<void> signInWithOtp({
    required String phoneNumber,
    required String otp,
  }) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      try {
        // Verify OTP using the fixed auth service
        final response = await _authService.verifyOtp(phoneNumber, otp);

        if (response['success'] == true) {
          // Get user data from the response
          final userData = response['data'] ?? response['user'];
          if (userData != null) {
            // Convert to User model if needed
            final user = User.fromJson(userData);
            return AuthState.authenticated(user);
          }
          return AuthState.error('Failed to get user profile');
        }
        return AuthState.error(
            response['message'] ?? 'OTP verification failed');
      } catch (e) {
        return AuthState.error(e.toString());
      }
    });
  }

  /// Send OTP to phone number
  Future<ApiResponse<LoginResponse>> sendOtp(String phoneNumber) async {
    try {
      // Send OTP using the fixed auth service
      final response = await _authService.sendOtp(phoneNumber);

      // Convert the response to the expected format
      return ApiResponse<LoginResponse>(
        success: response['success'] ?? false,
        message: response['message'] ?? 'Unknown error',
        data: response['success'] == true
            ? LoginResponse(
                requestId: phoneNumber,
                message: response['message'] ?? 'OTP sent successfully',
              )
            : null,
      );
    } catch (e) {
      debugPrint('Error sending OTP: $e');
      return ApiResponse<LoginResponse>(
        success: false,
        message: 'Failed to send OTP: $e',
      );
    }
  }

  /// Sign out the current user
  Future<void> signOut() async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      await _authService.logout();
      return AuthState.unauthenticated();
    });
  }

  /// Helper method to get user profile
  Future<User?> _getUserProfile() async {
    try {
      final userData = await _authService.getUserData();
      if (userData != null) {
        return User.fromJson(userData);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting user profile in AuthNotifier: $e');
      return null;
    }
  }
}
