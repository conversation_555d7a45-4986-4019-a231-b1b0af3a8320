import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

/// Service for handling location-related functionality
class LocationService {
  // Cache the last known location
  static Position? _lastKnownLocation;
  static DateTime? _lastLocationTime;

  /// Get the current location of the device
  Future<Position?> getCurrentLocation() async {
    try {
      // Check if we have a recent location (less than 1 minute old)
      if (_lastKnownLocation != null &&
          _lastLocationTime != null &&
          DateTime.now().difference(_lastLocationTime!).inMinutes < 1) {
        return _lastKnownLocation;
      }

      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        debugPrint('Location services are disabled');
        return null;
      }

      // Check for location permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          debugPrint('Location permissions are denied');
          return null;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        debugPrint('Location permissions are permanently denied');
        return null;
      }

      // Get the current position
      final position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
          timeLimit: Duration(seconds: 5),
        ),
      );

      // Cache the location
      _lastKnownLocation = position;
      _lastLocationTime = DateTime.now();

      return position;
    } catch (e) {
      debugPrint('Error getting current location: $e');
      return null;
    }
  }

  /// Get the last known location without requesting a new one
  Position? getLastKnownLocation() {
    return _lastKnownLocation;
  }

  /// Calculate distance between two coordinates in kilometers
  double calculateDistance(LatLng point1, LatLng point2) {
    return Geolocator.distanceBetween(
          point1.latitude,
          point1.longitude,
          point2.latitude,
          point2.longitude,
        ) /
        1000; // Convert meters to kilometers
  }

  /// Format distance for display
  String formatDistance(double distanceInKm) {
    if (distanceInKm < 1) {
      // If less than 1 km, show in meters
      return '${(distanceInKm * 1000).toStringAsFixed(0)} m';
    } else if (distanceInKm < 10) {
      // If less than 10 km, show with 1 decimal place
      return '${distanceInKm.toStringAsFixed(1)} km';
    } else {
      // Otherwise, show as integer
      return '${distanceInKm.toStringAsFixed(0)} km';
    }
  }
}
