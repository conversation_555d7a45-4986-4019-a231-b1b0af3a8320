import 'package:flutter/material.dart';

import 'RFID LINK PAGE/link_rfid_card_sheet.dart';
import 'RFID_ORDER_FORMED_PAGE/rfid_order_form.dart';

class RFIDPage extends StatelessWidget {
  const RFIDPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const RfidCardsSheet();
  }
}

class RfidCardsSheet extends StatefulWidget {
  const RfidCardsSheet({super.key});

  @override
  State<RfidCardsSheet> createState() => _RfidCardsSheetState();
}

class _RfidCardsSheetState extends State<RfidCardsSheet> {
  // Future to simulate fetching multiple RFID cards from an API.
  late Future<List<Map<String, String>>> _futureRfidCards;

  // PageController for the swipeable banner.
  final PageController _pageController = PageController();
  int _currentPage = 0;

  // Example data for “How to use” (title + description).
  final List<_FaqItem> _faqItems = [
    _FaqItem(
      title: 'Activate Your Card',
      content:
          'Make sure your RFID card is activated with your EV charging service provider.\n'
          'Check your email for activation instructions or log into your online account to confirm your card status.\n'
          'If the card isn’t activated, contact support or follow the steps in your welcome packet.',
    ),
    _FaqItem(
      title: 'Locate a Station',
      content:
          'Use the in-app map or website to find charging stations that accept RFID cards.\n'
          'Look for stations labeled “RFID compatible” or “Contactless.”\n'
          'Plan your route and ensure the station supports your EV’s connector type.',
    ),
    _FaqItem(
      title: 'Keep Card Secure',
      content:
          'Store your RFID card in a safe place, such as a wallet sleeve or protective case.\n'
          'Avoid bending, scratching, or exposing the card to extreme temperatures.\n'
          'If lost or stolen, contact support immediately to deactivate your card.',
    ),
    _FaqItem(
      title: 'Manage Multiple Cards',
      content:
          'If you have multiple RFID cards (e.g., family members), label each card for easy identification.\n'
          'Track usage history in the app to see which card was used at each station.\n'
          'Set up notifications for low balance or membership renewal dates if applicable.',
    ),
  ];

  @override
  void initState() {
    super.initState();
    // Simulate fetching RFID cards from an API.
    _futureRfidCards = _fetchRfidCards();
  }

  /// Simulated API call returning a list of RFID cards.
  // Update the path in your _fetchRfidCards method
  Future<List<Map<String, String>>> _fetchRfidCards() async {
    await Future.delayed(const Duration(seconds: 2)); // simulate network delay

    return [
      {
        'rfidNumber': '1679363011',
        'cardImage': 'assets/RFID_CARDS/RFIDCARDS_1.png' // Updated path
      },
      {
        'rfidNumber': '2345678912',
        'cardImage': 'assets/RFID_CARDS/RFIDCARDS_2.png' // Updated path
      },
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0E0E0E), // Dark background
      appBar: AppBar(
        backgroundColor: const Color(0xFF0E0E0E),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Color(0xFF67C44C)),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'RFID Cards',
          style: TextStyle(color: Colors.white, fontSize: 18),
        ),
      ),
      // Use a Stack to pin the bottom container (buttons + contact info).
      body: Stack(
        children: [
          // The scrollable content (behind the pinned bottom container).
          Positioned.fill(
            child: SingleChildScrollView(
              padding:
                  const EdgeInsets.only(bottom: 160), // Reduced from 180 to 160
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // FutureBuilder to load RFID cards.
                  FutureBuilder<List<Map<String, String>>>(
                    future: _futureRfidCards,
                    builder: (context, snapshot) {
                      if (snapshot.connectionState == ConnectionState.waiting) {
                        // Show a loading indicator.
                        return const SizedBox(
                          height: 220,
                          child: Center(
                            child: CircularProgressIndicator(
                              color: Color(0xFF67C44C),
                            ),
                          ),
                        );
                      } else if (snapshot.hasError) {
                        return const Padding(
                          padding: EdgeInsets.all(16),
                          child: Text(
                            'Error loading cards.',
                            style: TextStyle(color: Colors.white),
                          ),
                        );
                      } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
                        return const Padding(
                          padding: EdgeInsets.all(16),
                          child: Text(
                            'No RFID cards available.',
                            style: TextStyle(color: Colors.white),
                          ),
                        );
                      }
                      final rfidCards = snapshot.data!;
                      return Column(
                        children: [
                          // Swipe able banner for multiple cards.
                          _buildCardBanner(rfidCards),
                          // Page indicators.
                          if (rfidCards.length > 1)
                            _buildPageIndicators(rfidCards),
                        ],
                      );
                    },
                  ),
                  // “How to use” heading.
                  Padding(
                    padding: const EdgeInsets.fromLTRB(16, 24, 16, 8),
                    child: Text(
                      'How to use',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                  ),
                  // Custom expandable panels.
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Column(
                      children: _faqItems
                          .map(
                            (faq) => _CustomExpandablePanel(
                              title: faq.title,
                              description: faq.content,
                            ),
                          )
                          .toList(),
                    ),
                  ),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),
          // Pinned bottom container: two buttons + contact info.
          Positioned(
            left: 0,
            right: 0,
            bottom: 0,
            child: Container(
              color: const Color(0xFF0E0E0E),
              padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Two large, squarish buttons.
                  Row(
                    children: [
                      Expanded(
                        child: SizedBox(
                          height: 50,
                          child: ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF262626),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            onPressed: () {
                              Navigator.of(context).push(
                                MaterialPageRoute(
                                  builder: (_) => const RfidOrderForm(),
                                ),
                              );
                            },
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: const [
                                Icon(Icons.add, color: Color(0xFF67C44C)),
                                SizedBox(width: 8),
                                Text(
                                  'Order New Card',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: SizedBox(
                          height:
                              50, // Changed from 56 to 50 to match other button
                          child: ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF262626),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            onPressed: () {
                              showModalBottomSheet(
                                context: context,
                                isScrollControlled: true,
                                backgroundColor: Colors.transparent,
                                builder: (BuildContext context) {
                                  return const LinkRfidCardSheet();
                                },
                              );
                            },
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: const [
                                Icon(Icons.link, color: Color(0xFF67C44C)),
                                SizedBox(width: 8),
                                Text(
                                  'Link Card',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12), // Reduced from 16 to 12
                  // Contact info box.
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12, // Reduced from 16 to 12
                    ),
                    decoration: BoxDecoration(
                      color: const Color(0xFF262626),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: const [
                            Icon(
                              Icons.email_outlined,
                              color: Color(0xFF67C44C),
                              size: 16, // Reduced from 18 to 16
                            ),
                            SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                '<EMAIL>',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 13, // Added smaller font size
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8), // Reduced from 12 to 8
                        Row(
                          children: const [
                            Icon(
                              Icons.language,
                              color: Color(0xFF67C44C),
                              size: 16, // Reduced from 18 to 16
                            ),
                            SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                'www.eeil.in',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 13, // Added smaller font size
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Builds a swipeable banner of RFID cards.
  Widget _buildCardBanner(List<Map<String, String>> rfidCards) {
    return SizedBox(
      height: 220,
      child: PageView.builder(
        controller: _pageController,
        itemCount: rfidCards.length,
        onPageChanged: (index) => setState(() => _currentPage = index),
        itemBuilder: (context, index) {
          final cardData = rfidCards[index];
          return _buildCardImage(
            cardImagePath: cardData['cardImage'] ?? '',
            rfidNumber: cardData['rfidNumber'] ?? '',
          );
        },
      ),
    );
  }

  /// A single card image widget with an overlay showing the RFID number.

  /// A single card image widget with only the RFID number near the bottom-left.
  Widget _buildCardImage({
    required String cardImagePath,
    required String rfidNumber,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(16)),
      child: Stack(
        children: [
          // Card background image
          ClipRRect(
            borderRadius: BorderRadius.circular(16),
            child: Image.asset(
              cardImagePath,
              fit: BoxFit.cover,
              width: double.infinity,
              height: double.infinity,
            ),
          ),
          // RFID number text positioned in the red box area as shown in the screenshot
          Positioned(
            left: 53,
            top: 40,
            child: Container(
              width: 150,
              height: 30,
              alignment: Alignment.centerLeft,
              decoration: BoxDecoration(
                // Uncomment the line below if you want to see the container for debugging
                // color: Colors.red.withOpacity(0.3),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                rfidNumber,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Builds page indicators for the PageView.
  Widget _buildPageIndicators(List<Map<String, String>> rfidCards) {
    return Padding(
      padding: const EdgeInsets.only(top: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: List.generate(rfidCards.length, (index) {
          final isActive = (index == _currentPage);
          return AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            width: isActive ? 12 : 8,
            height: 8,
            margin: const EdgeInsets.symmetric(horizontal: 4),
            decoration: BoxDecoration(
              color: isActive ? const Color(0xFF67C44C) : Colors.grey,
              borderRadius: BorderRadius.circular(4),
            ),
          );
        }),
      ),
    );
  }
}

/// A simple custom expandable panel widget that shows a title and expands to reveal a description.
class _CustomExpandablePanel extends StatefulWidget {
  final String title;
  final String description;

  const _CustomExpandablePanel({
    required this.title,
    required this.description,
  });

  @override
  State<_CustomExpandablePanel> createState() => _CustomExpandablePanelState();
}

class _CustomExpandablePanelState extends State<_CustomExpandablePanel>
    with SingleTickerProviderStateMixin {
  bool _isExpanded = false;
  late AnimationController _controller;
  late Animation<double> _expandAnimation;
  late Animation<double> _iconRotation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _expandAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));
    _iconRotation = Tween<double>(begin: 0, end: 0.5).animate(_controller);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _toggleExpansion() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: const Color(0xFF1E1E1E),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color(0xFF2E2E2E), width: 1),
      ),
      child: Column(
        children: [
          InkWell(
            borderRadius: BorderRadius.circular(8),
            onTap: _toggleExpansion,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      widget.title,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  RotationTransition(
                    turns: _iconRotation,
                    child: const Icon(
                      Icons.keyboard_arrow_down,
                      color: Colors.white70,
                    ),
                  ),
                ],
              ),
            ),
          ),
          AnimatedBuilder(
            animation: _controller,
            builder: (context, child) {
              return ClipRect(
                child: Align(
                  heightFactor: _expandAnimation.value,
                  child: child,
                ),
              );
            },
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
              child: Text(
                widget.description,
                style: const TextStyle(
                  color: Colors.white70,
                  fontSize: 14,
                  height: 1.4,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Simple data class for each FAQ item (title + content).
class _FaqItem {
  final String title;
  final String content;
  _FaqItem({required this.title, required this.content});
}
