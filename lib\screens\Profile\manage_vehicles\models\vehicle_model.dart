class Vehicle {
  final int id;
  final String name;
  final String? variants;
  final String batteryCapacity;
  final String vehicleImage;
  final int? brandId;
  final String? brandName;
  final bool isRegistered;
  final String? registrationNumber;
  final bool isDefault;
  final String license;
  final String imageUrl;

  Vehicle({
    required this.id,
    required this.name,
    this.variants,
    required this.batteryCapacity,
    required this.vehicleImage,
    this.brandId,
    this.brandName,
    this.isRegistered = false,
    this.registrationNumber,
    this.isDefault = false,
    this.license = '',
    String? imageUrl,
  }) : imageUrl = imageUrl ?? vehicleImage;

  factory Vehicle.fromJson(Map<String, dynamic> json) {
    return Vehicle(
      id: json['id'],
      name: json['name'],
      variants: json['variants'],
      batteryCapacity: json['battery_capacity'].toString(),
      vehicleImage: json['vehicle_image'],
      brandId: json['brand_id'],
      brandName: json['brand_name'],
      license: json['license'] ?? '',
      isDefault: json['is_default'] ?? false,
      registrationNumber: json['registration_number'],
      isRegistered: json['is_registered'] ?? false,
    );
  }
}
