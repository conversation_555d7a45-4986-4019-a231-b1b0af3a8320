import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:io';
import 'package:sms_autofill/sms_autofill.dart';
import '../utils/app_themes.dart';

class PinCodeFields extends StatefulWidget {
  final int length;
  final ValueChanged<String> onCompleted;
  final ValueChanged<String> onChanged;
  final TextEditingController controller;
  final bool autoFocus;
  final bool obscureText;
  final bool enablePinAutofill;

  const PinCodeFields({
    super.key,
    this.length = 6,
    required this.onCompleted,
    required this.onChanged,
    required this.controller,
    this.autoFocus = true,
    this.obscureText = false,
    this.enablePinAutofill = true,
  });

  @override
  State<PinCodeFields> createState() => _PinCodeFieldsState();
}

class _PinCodeFieldsState extends State<PinCodeFields> with CodeAutoFill {
  late FocusNode _focusNode;
  late List<String> _inputList;
  String _currentText = "";

  @override
  void initState() {
    super.initState();
    _focusNode = FocusNode();
    _inputList = List<String>.filled(widget.length, '');

    // Listen for changes in the controller
    widget.controller.addListener(() {
      setState(() {
        _currentText = widget.controller.text;
        _inputList = _currentText.split('');
        if (_inputList.length < widget.length) {
          _inputList = _inputList +
              List<String>.filled(widget.length - _inputList.length, '');
        } else if (_inputList.length > widget.length) {
          _inputList = _inputList.sublist(0, widget.length);
        }
      });

      // Check if the code is complete
      if (_currentText.length == widget.length) {
        debugPrint('\n=== OTP COMPLETED ===');
        debugPrint('OTP value: $_currentText');
        debugPrint('Calling _verifyOTP() from onCompleted');
        widget.onCompleted(_currentText);
      }

      widget.onChanged(_currentText);
      debugPrint('PinCodeField current text: $_currentText');
    });

    if (widget.autoFocus) {
      Future.delayed(const Duration(milliseconds: 100), () {
        _focusNode.requestFocus();
      });
    }

    // Initialize SMS autofill - only on physical devices
    if (widget.enablePinAutofill) {
      try {
        if (!Platform.isWindows && !Platform.isMacOS && !Platform.isLinux) {
          listenForCode();
        }
      } catch (e) {
        // If we can't determine the platform, don't try to listen for SMS
        debugPrint('Error initializing SMS autofill: $e');
      }
    }
  }

  @override
  void codeUpdated() {
    if (code != null) {
      debugPrint('\n=== SMS OTP DETECTED ===');
      debugPrint('Received OTP from SMS: $code');

      // Extract only digits if the code contains other characters
      final digitsOnly = code!.replaceAll(RegExp(r'\D'), '');
      debugPrint('Extracted digits: $digitsOnly');

      if (digitsOnly.length == widget.length) {
        debugPrint('Setting controller text to: $digitsOnly');
        widget.controller.text = digitsOnly;

        debugPrint('\n=== OTP COMPLETED ===');
        debugPrint('OTP value: $digitsOnly');
        debugPrint('Calling onCompleted() from codeUpdated');
        widget.onCompleted(digitsOnly);
      } else {
        debugPrint(
            'SMS code length mismatch. Expected: ${widget.length}, Got: ${digitsOnly.length}');
      }
    }
  }

  @override
  void dispose() {
    _focusNode.dispose();
    try {
      if (!Platform.isWindows && !Platform.isMacOS && !Platform.isLinux) {
        cancel();
      }
    } catch (e) {
      // If we can't determine the platform, just ignore
      debugPrint('Error canceling SMS autofill: $e');
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      // Make the entire area tappable to focus the input field
      onTap: () {
        _focusNode.requestFocus();
      },
      behavior: HitTestBehavior.opaque, // This ensures the entire area is tappable
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Hidden text field for input
          Opacity(
            opacity: 0,
            child: TextField(
              controller: widget.controller,
              focusNode: _focusNode,
              keyboardType: TextInputType.number,
              inputFormatters: [
                LengthLimitingTextInputFormatter(widget.length),
                FilteringTextInputFormatter.digitsOnly,
              ],
              autofillHints:
                  widget.enablePinAutofill ? [AutofillHints.oneTimeCode] : null,
              onChanged: (value) {
                // This is handled by the controller listener
              },
              decoration: const InputDecoration(
                border: InputBorder.none,
                counterText: '',
              ),
            ),
          ),

          // Visible pin dots
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4.0, vertical: 8.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: List.generate(
                widget.length,
                (index) => _buildPinDot(index),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPinDot(int index) {
    bool isCurrentIndex = index == _currentText.length;
    bool isCompletedIndex = index < _currentText.length;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      width: 42,
      height: 52,
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: isDarkMode
            ? isCompletedIndex
                ? AppThemes.darkCard
                : AppThemes.darkSurface
            : isCompletedIndex
                ? const Color(0xFFF0FFF0)
                : Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isCurrentIndex
              ? AppThemes.primaryColor
              : isCompletedIndex
                  ? AppThemes.primaryColor.withAlpha(isDarkMode ? 100 : 128)
                  : isDarkMode
                      ? AppThemes.darkBorder
                      : Colors.grey.withAlpha(77),
          width: isCurrentIndex ? 2 : 1.5,
        ),
        boxShadow: isCompletedIndex
            ? [
                BoxShadow(
                  color: AppThemes.primaryColor.withAlpha(isDarkMode ? 20 : 26),
                  blurRadius: 8,
                  spreadRadius: 1,
                )
              ]
            : null,
      ),
      child: isCompletedIndex
          ? widget.obscureText
              ? Icon(Icons.circle, size: 12, color: AppThemes.primaryColor)
              : Text(
                  _inputList[index],
                  style: TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                    color: AppThemes.primaryColor,
                    letterSpacing: 1.2,
                  ),
                )
          : isCurrentIndex
              ? Container(
                  width: 12,
                  height: 12,
                  decoration: BoxDecoration(
                    color: AppThemes.primaryColor.withAlpha(128),
                    borderRadius: BorderRadius.circular(6),
                  ),
                )
              : Container(),
    );
  }
}
