import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../core/api/api_service.dart';
import '../../core/api/api_config.dart';
import '../../models/auth/otp_verification_request.dart';

/// Unified authentication service with robust error handling
class AuthService {
  // Keys for storing data in SharedPreferences
  static const String tokenKey = 'auth_token';
  static const String userDataKey = 'user_data';
  static const String lastPhoneNumberKey = 'last_phone_number';
  static const String lastOtpTimeKey = 'last_otp_time';

  // Centralized API service for making requests
  final ApiService _apiService = ApiService();

  // Singleton pattern
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  /// Send OTP to the provided phone number with robust error handling
  Future<Map<String, dynamic>> sendOtp(String phoneNumber) async {
    try {
      // Log the request
      debugPrint('\n=== SENDING OTP (FIXED) ===');
      debugPrint('Phone number: $phoneNumber');

      // Format the phone number (remove +91 prefix if present)
      final formattedNumber = phoneNumber.startsWith('+91')
          ? phoneNumber.substring(3)
          : phoneNumber;

      // Prepare request data with exact format expected by API
      final data = {
        'mobile_no': formattedNumber,
        'domain': 'eeil.online',
      };

      // Save the phone number for verification later
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(lastPhoneNumberKey, formattedNumber);
      await prefs.setInt(lastOtpTimeKey, DateTime.now().millisecondsSinceEpoch);

      debugPrint('Formatted request data: $data');
      debugPrint('Using endpoint: ${ApiConfig.login}');

      // Make the API call with our fixed centralized API service
      final response = await _apiService.publicPost(ApiConfig.login, data);

      // Log the response
      debugPrint('\n=== SEND OTP RESPONSE (FIXED) ===');
      debugPrint('Response type: ${response.runtimeType}');
      debugPrint('Response: $response');

      // The fixed API service now returns proper Map responses
      if (response is Map<String, dynamic>) {
        // Ensure success field exists
        if (!response.containsKey('success')) {
          response['success'] =
              response.containsKey('data') || response.containsKey('message');
        }

        // If no explicit success indicator, check for typical success patterns
        if (response['success'] != true && response.containsKey('message')) {
          final message = response['message']?.toString().toLowerCase() ?? '';
          if (message.contains('sent') || message.contains('success')) {
            response['success'] = true;
          }
        }

        return response;
      } else {
        // Handle non-Map responses
        debugPrint('Warning: Non-Map response received: $response');
        return {
          'success': false,
          'message': 'Invalid response format from server',
          'error_code': 'INVALID_RESPONSE',
        };
      }
    } catch (e) {
      // Handle errors - the fixed API service should not throw exceptions for OTP
      debugPrint('\n=== SEND OTP ERROR (UNEXPECTED) ===');
      debugPrint('Error: $e');
      debugPrint('Error type: ${e.runtimeType}');

      return {
        'success': false,
        'message': 'Network error occurred. Please try again.',
        'error_code': 'NETWORK_ERROR',
        'details': e.toString(),
      };
    }
  }

  /// Verify OTP with the provided phone number and OTP code
  Future<Map<String, dynamic>> verifyOtp(String phoneNumber, String otp) async {
    try {
      // Log the request
      debugPrint('\n=== VERIFYING OTP (FIXED) ===');
      debugPrint('Phone number: $phoneNumber');
      debugPrint('OTP: $otp');

      // Format the phone number (remove +91 prefix if present)
      final formattedNumber = phoneNumber.startsWith('+91')
          ? phoneNumber.substring(3)
          : phoneNumber;

      // Create the request with proper formatting
      final request = OtpVerificationRequest(
        mobileNumber: formattedNumber,
        otp: otp,
      );

      // Get the formatted data
      final data = request.toJson();
      debugPrint('Formatted request data: $data');
      debugPrint('Using endpoint: ${ApiConfig.verifyOtp}');

      // Make the API call with our fixed centralized API service
      final response = await _apiService.publicPost(ApiConfig.verifyOtp, data);

      // Log the response
      debugPrint('\n=== VERIFY OTP RESPONSE (FIXED) ===');
      debugPrint('Response type: ${response.runtimeType}');
      debugPrint('Response: $response');

      // The fixed API service now returns proper Map responses
      if (response is Map<String, dynamic>) {
        // Handle successful verification
        if (response['success'] == true ||
            response['message'] == 'OTP Verify' ||
            response.containsKey('user') ||
            response.containsKey('data')) {
          // Extract user data and token
          Map<String, dynamic>? userData;
          String? token;

          if (response.containsKey('data') &&
              response['data'] is Map<String, dynamic>) {
            userData = response['data'] as Map<String, dynamic>;
            token = userData['token'];
          } else if (response.containsKey('user') &&
              response['user'] is Map<String, dynamic>) {
            userData = response['user'] as Map<String, dynamic>;
            token = userData['token'];
          }

          // Save token and user data if available
          if (token != null && token.isNotEmpty) {
            await _saveToken(token);
            debugPrint('Token saved successfully');
          }

          if (userData != null) {
            await _saveUserData(userData);
            debugPrint('User data saved successfully');
          }

          // Normalize the response format
          final normalizedResponse = {
            'success': true,
            'message': response['message'] ?? 'OTP verified successfully',
            'data': userData,
            'user': userData, // Keep both for compatibility
            'token': token,
          };

          return normalizedResponse;
        } else {
          // Handle verification failure
          return {
            'success': false,
            'message': response['message'] ?? 'OTP verification failed',
            'error_code': response['error_code'] ?? 'VERIFICATION_FAILED',
          };
        }
      } else {
        // Handle non-Map responses
        debugPrint('Warning: Non-Map response received: $response');
        return {
          'success': false,
          'message': 'Invalid response format from server',
          'error_code': 'INVALID_RESPONSE',
        };
      }
    } catch (e) {
      // Handle errors - the fixed API service should not throw exceptions for OTP
      debugPrint('\n=== VERIFY OTP ERROR (UNEXPECTED) ===');
      debugPrint('Error: $e');
      debugPrint('Error type: ${e.runtimeType}');

      return {
        'success': false,
        'message': 'Network error occurred. Please try again.',
        'error_code': 'NETWORK_ERROR',
        'details': e.toString(),
      };
    }
  }

  /// Update user profile
  Future<Map<String, dynamic>> updateProfile(
      Map<String, dynamic> userData) async {
    try {
      // Log the request
      debugPrint('\n=== UPDATING PROFILE ===');
      debugPrint('User data: $userData');

      // Get the token
      final token = await getToken();

      // Check if token is available
      if (token == null || token.isEmpty) {
        return {
          'success': false,
          'message': 'Authentication token not found. Please log in again.',
          'error_code': 'NO_TOKEN'
        };
      }

      debugPrint('Using profile update endpoint: ${ApiConfig.updateProfile}');

      // Make the API call with our centralized API service
      final response =
          await _apiService.post(ApiConfig.updateProfile, data: userData);

      // Log the response
      debugPrint('Profile update response: $response');

      if (response is Map<String, dynamic> && response['success'] == true) {
        debugPrint('Profile update successful');
      } else {
        final message = response is Map<String, dynamic>
            ? response['message'] ?? 'Unknown error'
            : 'Unknown error';
        debugPrint('Profile update failed: $message');
      }

      // If the response is successful, update the stored user data
      if (response is Map<String, dynamic> && response['success'] == true) {
        // Get the current user data
        final currentUserData = await getUserData();

        // If we have current user data, update it with the new data
        if (currentUserData != null) {
          // Merge the current data with the new data
          currentUserData.addAll(userData);

          // Save the updated user data
          await _saveUserData(currentUserData);
        }
      }

      return response is Map<String, dynamic>
          ? response
          : {'success': false, 'message': 'Invalid response format'};
    } catch (e) {
      // Handle errors
      debugPrint('\n=== UPDATE PROFILE ERROR ===');
      debugPrint('Error: $e');

      return {
        'success': false,
        'message': 'Failed to update profile: $e',
        'error_code': 'UNEXPECTED_ERROR',
      };
    }
  }

  /// Save the auth token
  Future<void> _saveToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(tokenKey, token);
    debugPrint('Token saved successfully');
  }

  /// Save user data
  Future<void> _saveUserData(Map<String, dynamic> userData) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(userDataKey, json.encode(userData));
    debugPrint('User data saved successfully');
  }

  /// Get the stored auth token
  Future<String?> getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(tokenKey);
  }

  /// Get the stored user data
  Future<Map<String, dynamic>?> getUserData() async {
    final prefs = await SharedPreferences.getInstance();
    final userDataString = prefs.getString(userDataKey);

    if (userDataString != null) {
      try {
        return json.decode(userDataString) as Map<String, dynamic>;
      } catch (e) {
        debugPrint('Error parsing user data: $e');
      }
    }

    return null;
  }

  /// Check if user is logged in
  Future<bool> isLoggedIn() async {
    final token = await getToken();
    return token != null && token.isNotEmpty;
  }

  /// Log out the user
  Future<void> logout() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(tokenKey);
    await prefs.remove(userDataKey);
    debugPrint('User logged out successfully');
  }

  /// Save the last phone number used for login
  Future<void> saveLastPhoneNumber(String phoneNumber) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('last_phone_number', phoneNumber);
  }

  /// Get the last phone number used for login
  Future<String?> getLastPhoneNumber() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('last_phone_number');
  }

  /// Handle verification errors with user-friendly messages
  Future<Map<String, dynamic>> handleVerificationError(dynamic error) async {
    // Default error response
    final response = {
      'success': false,
      'message': 'Failed to verify OTP. Please try again.',
      'error_code': 'VERIFICATION_ERROR',
    };

    // Add more specific error messages based on the error type
    final errorString = error.toString().toLowerCase();

    if (errorString.contains('connection reset by peer')) {
      response['message'] =
          'The server unexpectedly closed the connection. This is a known issue with the EcoPlug server that our team is working to fix. Please try again later.';
      response['error_code'] = 'CONNECTION_RESET';
    } else if (errorString.contains('connection refused')) {
      response['message'] =
          'Could not connect to the server. The server might be down or undergoing maintenance.';
      response['error_code'] = 'CONNECTION_REFUSED';
    } else if (errorString.contains('timeout')) {
      response['message'] =
          'The server took too long to respond. Please check your internet connection and try again.';
      response['error_code'] = 'TIMEOUT';
    }

    return response;
  }
}
