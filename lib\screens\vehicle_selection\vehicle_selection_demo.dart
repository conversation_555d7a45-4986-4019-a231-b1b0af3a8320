import 'package:flutter/material.dart';
import '../../utils/app_theme.dart';
import 'vehicle_selection_screen.dart';

class VehicleSelectionDemo extends StatelessWidget {
  const VehicleSelectionDemo({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Vehicle Selection Demo'),
        backgroundColor: AppTheme.primaryColor,
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.directions_car,
                size: 80,
                color: AppTheme.primaryColor,
              ),
              const SizedBox(height: 24),
              const Text(
                'Vehicle Selection Demo',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              const Text(
                'This demo shows how to select and register a vehicle using the Vehicle Selection Screen.',
                style: TextStyle(
                  fontSize: 16,
                  color: AppTheme.textSecondaryColor,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 40),
              ElevatedButton.icon(
                onPressed: () {
                  final messenger = ScaffoldMessenger.of(context);
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const VehicleSelectionScreen(),
                    ),
                  ).then((result) {
                    if (result != null && result['success'] == true) {
                      final vehicle = result['vehicle'];
                      final registrationNumber = result['registrationNumber'];

                      messenger.showSnackBar(
                        SnackBar(
                          content: Text(
                            'Registered ${vehicle.name} with number $registrationNumber',
                          ),
                          backgroundColor: AppTheme.primaryColor,
                          duration: const Duration(seconds: 3),
                        ),
                      );
                    }
                  });
                },
                icon: const Icon(Icons.add),
                label: const Text('Select Vehicle'),
                style: AppTheme.primaryButtonStyle,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
