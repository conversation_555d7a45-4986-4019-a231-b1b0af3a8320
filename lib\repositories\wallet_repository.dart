import 'dart:async';
import 'package:flutter/foundation.dart';
import '../core/api/api_config.dart';
import '../models/api_response.dart';
import '../models/wallet/wallet_models.dart';
import '../utils/api_exception.dart';
import '../core/api/api_service.dart'; // Use centralized API service

/// Repository for wallet-related API calls
class WalletRepository {
  final ApiService _apiService = ApiService();

  /// Get wallet info with proper handling for the actual API response format
  Future<ApiResponse<WalletInfo>> getWalletInfo() async {
    debugPrint(
        'Fetching wallet info from API endpoint: ${ApiConfig.walletInfo}');

    try {
      final response = await _apiService.get(ApiConfig.walletInfo);
      debugPrint('Raw wallet API response: $response');

      // The API returns data directly without a success flag
      if (response is Map<String, dynamic>) {
        // Check for the wallet data - the API directly returns the wallet object
        if (response.containsKey('wallet')) {
          try {
            final walletData = response['wallet'];

            if (walletData is Map<String, dynamic>) {
              // Create WalletInfo from the wallet data
              final walletInfo = WalletInfo(
                balance: (walletData['balance'] as num?)?.toDouble() ?? 0.0,
                rewardPoints: walletData['reward_points'] ?? 0,
                recentTransactions:
                    _parseTransactions(walletData['payment_history']),
              );

              debugPrint(
                  'Successfully parsed wallet info with balance: ${walletInfo.balance}');

              return ApiResponse<WalletInfo>(
                success: true,
                message: 'Wallet info retrieved successfully',
                data: walletInfo,
              );
            }
          } catch (parseError) {
            debugPrint('Error parsing wallet data: $parseError');
            return ApiResponse<WalletInfo>(
              success: false,
              message: 'Failed to parse wallet data: $parseError',
            );
          }
        }

        // If we get here, the expected wallet structure wasn't found
        debugPrint('API response missing wallet structure: $response');
        return ApiResponse<WalletInfo>(
          success: false,
          message: 'Invalid wallet data format from API',
        );
      } else {
        // Invalid response format
        debugPrint('Invalid API response format: $response');
        return ApiResponse<WalletInfo>(
          success: false,
          message: 'Invalid API response format',
        );
      }
    } catch (e) {
      debugPrint('Error in getWalletInfo: $e');
      return ApiResponse<WalletInfo>(
        success: false,
        message: 'An unexpected error occurred: $e',
      );
    }
  }

  /// Helper method to parse transactions from API response
  List<Transaction> _parseTransactions(dynamic paymentHistory) {
    final List<Transaction> transactions = [];

    if (paymentHistory is List) {
      for (final item in paymentHistory) {
        if (item is Map<String, dynamic>) {
          try {
            final amount = (item['amount'] as num?)?.toDouble() ?? 0.0;
            final type = (item['type'] as String?)?.toLowerCase() ?? '';
            final isCredit = type == 'cr';

            transactions.add(Transaction(
              id: item['id']?.toString() ?? '',
              title: item['remark'] ?? (isCredit ? 'Balance Added' : 'Payment'),
              description: item['remark'] ?? '',
              amount: amount,
              timestamp: item['created_at'] != null
                  ? DateTime.parse(item['created_at'])
                  : DateTime.now(),
              type: isCredit ? 'credit' : 'debit',
              status: (item['status'] as String?)?.toLowerCase() ?? 'completed',
              transactionReference: item['source']?.toString(),
            ));
          } catch (e) {
            debugPrint('Error parsing transaction: $e');
            // Skip this transaction and continue with others
          }
        }
      }
    }

    return transactions;
  }

  /// Start a charging transaction
  Future<ApiResponse<String>> startTransaction(
    String stationId,
    String connectorId,
    String? vehicleId,
    String? promocodeId,
  ) async {
    try {
      final response = await _apiService.post(
        ApiConfig.startTransaction,
        data: {
          'stationId': stationId,
          'connectorId': connectorId,
          if (vehicleId != null) 'vehicleId': vehicleId,
          if (promocodeId != null) 'promocodeId': promocodeId,
        },
      );

      final String transactionId = response['data']['transactionId'];

      return ApiResponse<String>(
        success: true,
        message: 'Transaction started successfully',
        data: transactionId,
      );
    } on ApiException catch (e) {
      return ApiResponse<String>(
        success: false,
        message: e.message,
      );
    } catch (e) {
      return ApiResponse<String>(
        success: false,
        message: 'Failed to start transaction: $e',
      );
    }
  }

  /// Stop a charging transaction
  Future<ApiResponse<BillingDetails>> stopTransaction(
      String transactionId) async {
    try {
      // Always use real API data, no mock data

      final response = await _apiService.post(
        ApiConfig.stopTransaction,
        data: {'transactionId': transactionId},
      );

      final BillingDetails billingDetails =
          BillingDetails.fromJson(response['data']);

      return ApiResponse<BillingDetails>(
        success: true,
        message: 'Transaction stopped successfully',
        data: billingDetails,
      );
    } on ApiException catch (e) {
      return ApiResponse<BillingDetails>(
        success: false,
        message: e.message,
      );
    } catch (e) {
      return ApiResponse<BillingDetails>(
        success: false,
        message: 'Failed to stop transaction: $e',
      );
    }
  }

  /// Get billing details for a transaction
  Future<ApiResponse<BillingDetails>> getBillingDetails(
      String transactionId) async {
    try {
      // Always use real API data, no mock data

      final response = await _apiService.get(
        ApiConfig.billingDetails,
        queryParams: {'transactionId': transactionId},
      );

      final BillingDetails billingDetails =
          BillingDetails.fromJson(response['data']);

      return ApiResponse<BillingDetails>(
        success: true,
        message: 'Billing details fetched successfully',
        data: billingDetails,
      );
    } on ApiException catch (e) {
      return ApiResponse<BillingDetails>(
        success: false,
        message: e.message,
      );
    } catch (e) {
      return ApiResponse<BillingDetails>(
        success: false,
        message: 'Failed to fetch billing details: $e',
      );
    }
  }

  /// Get ongoing charging sessions
  Future<ApiResponse<List<ChargingSession>>> getOngoingSessions() async {
    try {
      // Always use real API data, no mock data

      final response = await _apiService.get(ApiConfig.ongoingList);

      final List<ChargingSession> sessions = (response['data'] as List<dynamic>)
          .map((e) => ChargingSession.fromJson(e as Map<String, dynamic>))
          .toList();

      return ApiResponse<List<ChargingSession>>(
        success: true,
        message: 'Ongoing sessions fetched successfully',
        data: sessions,
      );
    } on ApiException catch (e) {
      return ApiResponse<List<ChargingSession>>(
        success: false,
        message: e.message,
      );
    } catch (e) {
      return ApiResponse<List<ChargingSession>>(
        success: false,
        message: 'Failed to fetch ongoing sessions: $e',
      );
    }
  }

  /// Get charging session history
  Future<ApiResponse<List<ChargingSession>>> getChargingSessionHistory() async {
    try {
      // Always use real API data, no mock data

      final response = await _apiService.post(ApiConfig.chargingSessionsList);

      final List<ChargingSession> sessions = (response['data'] as List<dynamic>)
          .map((e) => ChargingSession.fromJson(e as Map<String, dynamic>))
          .toList();

      return ApiResponse<List<ChargingSession>>(
        success: true,
        message: 'Charging session history fetched successfully',
        data: sessions,
      );
    } on ApiException catch (e) {
      return ApiResponse<List<ChargingSession>>(
        success: false,
        message: e.message,
      );
    } catch (e) {
      return ApiResponse<List<ChargingSession>>(
        success: false,
        message: 'Failed to fetch charging session history: $e',
      );
    }
  }

  /// Add money to wallet
  Future<ApiResponse<bool>> addMoney(double amount) async {
    try {
      // Make the API call to add money
      final response = await _apiService.post(
        ApiConfig.addMoney,
        data: {'amount': amount},
      );

      return ApiResponse<bool>(
        success: true,
        message: response['message'] ?? 'Money added successfully',
        data: true,
      );
    } on ApiException catch (e) {
      return ApiResponse<bool>(
        success: false,
        message: e.message,
        data: false,
      );
    } catch (e) {
      return ApiResponse<bool>(
        success: false,
        message: 'Failed to add money: $e',
        data: false,
      );
    }
  }

  /// Initiate a payment order with the server
  Future<ApiResponse> initiatePaymentOrder(
      String endpoint, Map<String, dynamic> requestData) async {
    try {
      final response = await _apiService.post(
        endpoint,
        data: requestData,
      );

      if (response.containsKey('success') && response['success'] == true) {
        return ApiResponse.success(response);
      } else {
        return ApiResponse.error(
            response['message'] ?? 'Failed to initiate payment order');
      }
    } catch (e) {
      return ApiResponse.error('Error initiating payment order: $e');
    }
  }

  /// Verify a payment with the server for security
  Future<ApiResponse> verifyPayment(
      String endpoint, Map<String, dynamic> requestData) async {
    try {
      final response = await _apiService.post(
        endpoint,
        data: requestData,
      );

      if (response.containsKey('success') && response['success'] == true) {
        return ApiResponse.success(response);
      } else {
        return ApiResponse.error(
            response['message'] ?? 'Failed to verify payment');
      }
    } catch (e) {
      return ApiResponse.error('Error verifying payment: $e');
    }
  }
}
