import 'package:flutter/material.dart';
import 'package:line_icons/line_icons.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../features/profile/application/profile_notifier.dart';
import '../../../features/profile/application/profile_notifier_riverpod.dart' as riverpod;
import '../../../providers/theme_provider.dart';
import '../../../utils/app_themes.dart';
import '../../wallet/wallet_screen_real.dart';
import '../RFID_PAGE/RFIDPAGE.dart';
import '../Notification/notification_page.dart';
import '../Privacy_Settings/privacy_settings_page.dart';
import '../FAQ/faq_page.dart';
import '../FAQ/HELP&SUPPORT/support_center_page.dart' as support;
import 'package:ecoplug/screens/Profile/EditProfile/edit_profile_page.dart';
import 'package:ecoplug/models/user_model.dart';
import 'package:ecoplug/widgets/sync_status_indicator.dart';

class ProfilePage extends ConsumerStatefulWidget {
  const ProfilePage({super.key});

  @override
  ConsumerState<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends ConsumerState<ProfilePage> {
  @override
  void initState() {
    super.initState();
    // Load profile data when the widget is initialized
    ref.read(riverpod.profileProvider.notifier).loadUserData();
  }

  // Method to update profile data after editing
  void updateProfileData(Map<String, dynamic> updatedData) {
    // Use the profile provider to update the profile
    ref.read(riverpod.profileProvider.notifier).updateProfile(updatedData);
  }

  // Toggle instant charging
  void toggleInstantCharging(bool value) {
    ref.read(riverpod.profileProvider.notifier).toggleInstantCharging(value);
  }

  // Toggle auto charge
  void toggleAutoCharge(bool value) {
    ref.read(riverpod.profileProvider.notifier).toggleAutoCharge(value);
  }

  // Define local state variables
  bool isInstantChargingOn = false;
  bool isAutoChargeOn = false;

  // Mock wallet balance
  final double _walletBalance = 1250.00;

  // Mock user data
  final String _userName = "John Doe";
  final String _userEmail = "<EMAIL>";
  final String _userPhone = "+91 **********";

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // Watch the profile state
    // final profileState = ref.watch(profileProvider);

    return Scaffold(
      // AppBar
      appBar: AppBar(
        elevation: 0,
        centerTitle: true,
        title: const Text(
          'Profile',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),

      // Body
      body: SingleChildScrollView(
        child: Column(
          children: [
            // 1) Top user info section
            _buildUserHeader(),

            // 2) Row with balance + vehicles
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Row(
                children: [
                  Expanded(
                    child: _buildAnimatedCard(
                      icon: LineIcons.wallet,
                      iconBackgroundColor: const Color.fromARGB(
                        255,
                        183,
                        207,
                        245,
                      ),
                      title: 'Balance',
                      value: '₹${_walletBalance.toStringAsFixed(2)}',
                      backgroundColor: const Color.fromARGB(255, 212, 226, 252),
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const WalletScreenReal(),
                          ),
                        );
                      },
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildAnimatedCard(
                      icon: Icons.directions_car_filled_outlined,
                      iconBackgroundColor: const Color.fromARGB(
                        255,
                        191,
                        240,
                        203,
                      ),
                      title: 'Vehicles',
                      value: '2', // TODO: Replace with real data
                      backgroundColor: const Color.fromARGB(255, 222, 243, 226),
                      onTap: () {
                        // TODO: Implement ManageVehiclesPage
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content:
                                Text('Manage Vehicles feature coming soon'),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // 3) Boxed container for the toggles
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Container(
                decoration: BoxDecoration(
                  color: isDarkMode ? AppThemes.darkCard : Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                      color: isDarkMode
                          ? AppThemes.darkBorder
                          : Colors.grey.shade300,
                      width: 1.5),
                ),
                child: Column(
                  children: [
                    _buildSwitchTile(
                      icon: Icons.bolt,
                      label: 'Instant Charging',
                      value: isInstantChargingOn,
                      activeColor: const Color.fromARGB(255, 248, 175, 16),
                      onChanged: (val) {
                        setState(() => isInstantChargingOn = val);
                        // TODO: Persist the state (e.g., using SharedPreferences or API)
                      },
                    ),
                    Container(
                      height: 1,
                      color: Colors.grey.shade300,
                      margin: const EdgeInsets.symmetric(horizontal: 16),
                    ),
                    _buildSwitchTile(
                      icon: Icons.refresh,
                      label: 'Auto Charge',
                      value: isAutoChargeOn,
                      activeColor: Colors.blue,
                      onChanged: (val) {
                        setState(() => isAutoChargeOn = val);
                        // TODO: Persist the state (e.g., using SharedPreferences or API)
                      },
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // 4) Boxed container for the menu list
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Container(
                decoration: BoxDecoration(
                  color: isDarkMode ? AppThemes.darkCard : Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                      color: isDarkMode
                          ? AppThemes.darkBorder
                          : Colors.grey.shade300,
                      width: 1.5),
                ),
                child: Column(
                  children: [
                    _buildMenuItem(
                      icon: Icons.directions_car_filled_outlined,
                      title: 'Manage Vehicles',
                      onTap: () {
                        // TODO: Implement ManageVehiclesPage
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content:
                                Text('Manage Vehicles feature coming soon'),
                          ),
                        );
                      },
                    ),
                    _buildThinDivider(),
                    _buildMenuItem(
                      icon: Icons.credit_card,
                      title: 'RFID Cards',
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => RfidCardsSheet(),
                          ),
                        );
                      },
                    ),
                    _buildThinDivider(),
                    _buildMenuItem(
                      icon: Icons.account_balance_wallet_outlined,
                      title: 'Payment Methods',
                      onTap: () {
                        // TODO: Implement PaymentMethodsPage
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content:
                                Text('Payment Methods feature coming soon'),
                          ),
                        );
                      },
                    ),
                    _buildThinDivider(),
                    _buildMenuItem(
                      icon: Icons.notifications_outlined,
                      title: 'Notifications',
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const NotificationPage(),
                          ),
                        );
                      },
                    ),
                    _buildThinDivider(),
                    _buildMenuItem(
                      icon: Icons.lock_outline_rounded,
                      title: 'Privacy Settings',
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const PrivacySettingsPage(),
                          ),
                        );
                      },
                    ),
                    _buildThinDivider(),
                    _buildMenuItem(
                      icon: Icons.question_answer_outlined,
                      title: 'FAQs',
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const FAQPage(),
                          ),
                        );
                      },
                    ),
                    _buildThinDivider(),
                    _buildMenuItem(
                      icon: Icons.support_agent,
                      title: 'Help & Support',
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) =>
                                const support.HelpSupportPage(),
                          ),
                        );
                      },
                    ),
                    _buildThinDivider(),
                    // Theme Settings with improved UI
                    ListTile(
                      title: const Text(
                        'Theme Settings',
                        style: TextStyle(
                          fontSize: 16,
                        ),
                      ),
                      contentPadding:
                          const EdgeInsets.symmetric(horizontal: 16.0),
                      onTap: () {
                        // Toggle theme when tapping the row
                        ref.read(themeNotifierProvider.notifier).toggleTheme();
                      },
                      trailing: Consumer(
                        builder: (context, ref, _) {
                          final themeNotifier =
                              ref.watch(themeNotifierProvider.notifier);
                          final isDarkMode = themeNotifier.isDarkMode;
                          return Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 12, vertical: 6),
                            decoration: BoxDecoration(
                              color: isDarkMode
                                  ? const Color(0xFF1E1E1E)
                                  : const Color(0xFFF0F0F0),
                              borderRadius: BorderRadius.circular(16),
                              border: Border.all(
                                color: isDarkMode
                                    ? const Color(0xFF333333)
                                    : const Color(0xFFDDDDDD),
                                width: 1,
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  isDarkMode ? 'Dark' : 'Light',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                    color: isDarkMode
                                        ? Colors.white
                                        : Colors.black87,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Icon(
                                  isDarkMode
                                      ? Icons.dark_mode
                                      : Icons.light_mode,
                                  size: 20,
                                  color: isDarkMode
                                      ? const Color(0xFF34C759)
                                      : Colors.amber,
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // 5) Logout button
            _buildLogoutButton(),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  // -----------------------
  // 1) User Header Section
  // -----------------------
  Widget _buildUserHeader() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // Watch the profile state
    final AsyncValue<riverpod.ProfileState> profileState = ref.watch(riverpod.profileProvider);

    // Handle different states of the AsyncValue
    if (profileState.isLoading) {
      return Padding(
        padding: const EdgeInsets.only(top: 8, bottom: 16),
        child: Column(
          children: [
            const SizedBox(height: 20),
            const CircularProgressIndicator(),
            const SizedBox(height: 20),
            Text(
              'Loading profile...',
              style: TextStyle(
                fontSize: 16,
                color: isDarkMode
                    ? AppThemes.darkTextSecondary
                    : Colors.grey.shade700,
              ),
            ),
            const SizedBox(height: 20),
          ],
        ),
      );
    } else if (profileState.hasError) {
      return Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            const Icon(Icons.error_outline, color: Colors.red, size: 48),
            const SizedBox(height: 16),
            Text(
              'Error loading profile',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Text(
              profileState.error.toString(),
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                ref.read(riverpod.profileProvider.notifier).loadUserData();
              },
              child: const Text('Try Again'),
            ),
          ],
        ),
      );
    } else {
      // Data is available
        return Padding(
          padding: const EdgeInsets.only(top: 8, bottom: 16),
          child: Column(
            children: [
              SizedBox(
                width: 80,
                height: 80,
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    CircleAvatar(
                      radius: 40,
                      backgroundColor:
                          isDarkMode ? AppThemes.darkCard : Colors.grey[200],
                      backgroundImage: const NetworkImage(
                        'https://i.pravatar.cc/300?img=10',
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    _userName, // Using loaded user name
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                      color:
                          isDarkMode ? AppThemes.darkTextPrimary : Colors.black,
                    ),
                  ),
                  const SizedBox(width: 6),
                  GestureDetector(
                    onTap: () async {
                      // Navigate to edit profile page with current user data
                      final result = await Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => EditProfilePage(
                            userProfile: UserProfile(
                              name: _userName,
                              email: _userEmail,
                              phone: _userPhone,
                            ),
                          ),
                        ),
                      );

                      // If user updated profile, update the cached data
                      if (result != null && result is UserProfile) {
                        // Update the profile data using our caching mechanism
                        updateProfileData({
                          'name': result.name,
                          'email': result.email,
                          'mobile_number':
                              _userPhone, // Keep the original phone number
                        });
                      }
                    },
                    child: const Icon(Icons.edit,
                        size: 18, color: Colors.blueGrey),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Text(
                _userEmail, // Using loaded user email
                style: TextStyle(
                  fontSize: 14,
                  color: isDarkMode
                      ? AppThemes.darkTextSecondary
                      : Colors.grey.shade700,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                _userPhone, // Using loaded user phone
                style: TextStyle(
                  fontSize: 14,
                  color: isDarkMode
                      ? AppThemes.darkTextSecondary
                      : Colors.grey.shade700,
                ),
              ),
              const SizedBox(height: 16),
              const SyncStatusIndicator(),
            ],
          ),
        );

  }

  // -----------------------
  // Switches Section
  // -----------------------
  Widget _buildSwitchTile({
    required IconData icon,
    required String label,
    required bool value,
    required Color activeColor,
    required ValueChanged<bool> onChanged,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return ListTile(
      leading: Icon(
        icon,
        color: value
            ? activeColor
            : (isDarkMode ? Colors.white70 : Colors.grey.shade600),
      ),
      title: Text(
        label,
        style: TextStyle(
          fontSize: 16,
          color: isDarkMode ? Colors.white : Colors.black87,
        ),
      ),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: activeColor,
      ),
    );
  }

  // -----------------------
  // 4) Menu List
  // -----------------------
  Widget _buildMenuItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return ListTile(
      leading: Icon(
        icon,
        color: isDarkMode ? Colors.white70 : Colors.grey.shade700,
      ),
      title: Text(
        title,
        style: TextStyle(
          fontSize: 16,
          color: isDarkMode ? Colors.white : Colors.black87,
        ),
      ),
      trailing: Icon(
        Icons.chevron_right,
        color: isDarkMode ? Colors.white54 : Colors.grey.shade400,
      ),
      onTap: onTap,
    );
  }

  Widget _buildThinDivider() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Container(
      height: 1,
      color:
          isDarkMode ? Colors.white.withAlpha(25) : Colors.grey.withAlpha(50),
      margin: const EdgeInsets.symmetric(horizontal: 16),
    );
  }

  // -----------------------
  // 5) Logout Button
  // -----------------------
  Widget _buildLogoutButton() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          onPressed: () {
            _showLogoutConfirmationDialog();
          },
          style: ElevatedButton.styleFrom(
            backgroundColor:
                isDarkMode ? const Color(0xFF2C2C2C) : Colors.white,
            foregroundColor: isDarkMode ? Colors.white : Colors.black87,
            elevation: 0,
            padding: const EdgeInsets.symmetric(vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
              side: BorderSide(
                color: isDarkMode ? Colors.white24 : Colors.grey.shade300,
                width: 1.5,
              ),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.logout_rounded,
                color: Colors.red.shade400,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Logout',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.red.shade400,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // -----------------------
  // Logout Confirmation Dialog
  // -----------------------
  void _showLogoutConfirmationDialog() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Text(
          'Logout',
          style: TextStyle(
            color: isDarkMode ? Colors.white : Colors.black,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Text(
          'Are you sure you want to logout?',
          style: TextStyle(
            color: isDarkMode ? Colors.white70 : Colors.black87,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: Text(
              'Cancel',
              style: TextStyle(
                color: isDarkMode ? Colors.white70 : Colors.grey.shade700,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              // Perform logout
              Navigator.of(context).pop();
              // TODO: Implement actual logout functionality
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Logged out successfully'),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red.shade400,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('Logout'),
          ),
        ],
      ),
    );
  }

  // -----------------------
  // Animated Card Widget
  // -----------------------
  Widget _buildAnimatedCard({
    required IconData icon,
    required Color iconBackgroundColor,
    required String title,
    required String value,
    required Color backgroundColor,
    required VoidCallback onTap,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isDarkMode ? const Color(0xFF1E1E1E) : backgroundColor,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isDarkMode ? Colors.white24 : Colors.transparent,
            width: 1.5,
          ),
          boxShadow: isDarkMode
              ? []
              : [
                  BoxShadow(
                    color: Colors.black.withAlpha(15),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: isDarkMode
                        ? iconBackgroundColor.withAlpha(40)
                        : iconBackgroundColor.withAlpha(100),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    icon,
                    color: isDarkMode ? Colors.white : iconBackgroundColor,
                    size: 20,
                  ),
                ),
                const Spacer(),
                Icon(
                  Icons.chevron_right,
                  color: isDarkMode ? Colors.white54 : Colors.grey.shade400,
                  size: 20,
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              title,
              style: TextStyle(
                fontSize: 14,
                color: isDarkMode ? Colors.white70 : Colors.grey.shade700,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: isDarkMode ? Colors.white : Colors.black87,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Placeholder class for PaymentMethodsPage
class PaymentMethodsPage extends StatelessWidget {
  const PaymentMethodsPage({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Payment Methods'),
      ),
      body: const Center(
        child: Text('Payment Methods Page - Coming Soon'),
      ),
    );
  }
}
